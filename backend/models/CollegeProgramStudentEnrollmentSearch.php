<?php

namespace backend\models;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use common\models\CollegeProgramStudentEnrollment;

/**
 * CollegeProgramStudentEnrollmentSearch represents the model behind the search form of `common\models\CollegeProgramStudentEnrollment`.
 */
class CollegeProgramStudentEnrollmentSearch extends CollegeProgramStudentEnrollment
{
    public $college_name;
    public $course_name;
    public $program_name;

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'college_id', 'course_id', 'program_id', 'gender', 'year', 'count', 'status'], 'integer'],
            [['department', 'financing_type', 'created_at', 'updated_at', 'college_name', 'course_name', 'program_name'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = CollegeProgramStudentEnrollment::find();
        $query->joinWith(['college', 'course', 'program']);

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => ['defaultOrder' => ['id' => SORT_DESC]]
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'id' => $this->id,
            // 'college_id' => $this->college_id,
            // 'course_id' => $this->course_id,
            // 'program_id' => $this->program_id,
            'gender' => $this->gender,
            'year' => $this->year,
            'count' => $this->count,
            'college_program_student_enrollment.status' => $this->status,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ]);

        $query->andFilterWhere(['like', 'department', $this->department])
            ->andFilterWhere(['like', 'financing_type', $this->financing_type])
            ->andFilterWhere(['like', 'college.name', $this->college_name])
            ->andFilterWhere(['like', 'course.name', $this->course_name])
            ->andFilterWhere(['like', 'program.name', $this->program_name]);


        return $dataProvider;
    }
}
