<?php

namespace backend\models;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use common\models\DsaStateMapping;

/**
 * DsaStateMappingSearch represents the model behind the search form of `common\models\DsaStateMapping`.
 */
class DsaStateMappingSearch extends DsaStateMapping
{
    public $state_name;
    public $stream_name;
    public $degree_name;
    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'campaign_id', 'status'], 'integer'],
            [['created_at', 'updated_at', 'state_name', 'stream_name', 'degree_name'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = DsaStateMapping::find();
        $query->joinWith(['state', 'stream', 'degree']);

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => ['defaultOrder' => ['id' => SORT_DESC]]
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'id' => $this->id,
            'campaign_id' => $this->campaign_id,
            // 'state_id' => $this->state_id,
            'status' => $this->status,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ]);

        $query->andFilterWhere(['like', 'state.name', $this->state_name])
            ->andFilterWhere(['like', 'stream.name', $this->stream_name])
            ->andFilterWhere(['like', 'degree.display_name', $this->degree_name]);

        return $dataProvider;
    }
}
