<?php

namespace backend\models;

use common\models\CollegeCourseMapping;
use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use common\models\CollegeProgram;

/**
 * CollegeProgramSearch represents the model behind the search form of `common\models\CollegeProgram`.
 */
class CollegeProgramSearch extends CollegeProgram
{
    public $college;
    public $course;
    public $pageSize;
    public $program_name;
    
    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'college_id', 'position', 'page_index', 'status', 'created_by', 'updated_by'], 'integer'],
            [['pageSize'], 'safe'],
            [['program_id', 'program_name'], 'string'],
            [['mode', 'type', 'application_link', 'salary', 'college_id', 'college', 'duration', 'created_at', 'updated_at', 'course', 'course_id'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = CollegeProgram::find();
        $query->joinWith(['college', 'course', 'program']);

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => ['defaultOrder' => ['id' => SORT_DESC]]
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'college_program.id' => $this->id,
            // 'program_id' => $this->program_id,
            'college_id' => $this->college_id,
            'course_id' => $this->course_id,
            'position' => $this->position,
            'page_index' => $this->page_index,
            // 'status' => $this->status,
            'created_by' => $this->created_by,
            'updated_by' => $this->updated_by,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ]);

        $query->andFilterWhere(['like', 'program.slug', $this->program_id])
            ->andFilterWhere(['like', 'program.name', $this->program_name])
            ->andFilterWhere(['like', 'course.name', $this->course])
            // ->andFilterWhere(['like', 'program.mode', $this->mode])
            ->andFilterWhere(['like', 'college_program.duration', $this->duration])
            // ->andFilterWhere(['like', 'program.type', $this->type])
            ->andFilterWhere(['like', 'college.name', $this->college])
            ->andFilterWhere(['like', 'college_program.status', $this->status]);

        return $dataProvider;
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function home($params)
    {
        $query = CollegeProgram::find()
            ->where(['college_id' => $params['id']])
            // ->andWhere(['not', ['program_id' => 'NULL']])
            // ->andWhere(['college_program.status' => CollegeProgram::STATUS_ACTIVE])
            ->groupBy('course_id');
            
        $query->joinWith(['college', 'course']);

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => ['defaultOrder' => ['id' => SORT_DESC]]
        ]);

        $this->load($params);

        if (!$this->validate()) {
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere(['like', 'course.name', $this->course_id])
            ->andFilterWhere(['like', 'college.name', $this->college]);

        return $dataProvider;
    }
}
