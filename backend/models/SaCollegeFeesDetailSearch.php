<?php

namespace backend\models;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use common\models\SaCollegeFeesDetail;

/**
 * SaCollegeFeesDetailSearch represents the model behind the search form of `common\models\SaCollegeFeesDetail`.
 */
class SaCollegeFeesDetailSearch extends SaCollegeFeesDetail
{
    public $sa_college_name;
    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'sa_college_id', 'fees', 'status'], 'integer'],
            [['type', 'created_at', 'updated_at', 'sa_college_name', 'created_by', 'updated_by'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = SaCollegeFeesDetail::find();
        $query->joinWith(['saCollege']);

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => ['defaultOrder' => ['id' => SORT_DESC]]
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'id' => $this->id,
            'sa_college_id' => $this->sa_college_id,
            'fees' => $this->fees,
            'sa_college_fees_detail.status' => $this->status,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ]);

        $query->andFilterWhere(['like', 'type', $this->type])
            ->andFilterWhere(['like', 'sa_college.name', $this->sa_college_name]);


        return $dataProvider;
    }
}
