<?php

namespace backend\models;

use common\models\SaCollegeSubpage;
use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use common\models\SaCollegeSubpageSidemenuDetail;

/**
 * SaCollegeSubpageSidemenuDetailSearch represents the model behind the search form of `common\models\SaCollegeSubpageSidemenuDetail`.
 */
class SaCollegeSubpageSidemenuDetailSearch extends SaCollegeSubpageSidemenuDetail
{
    public $sa_college_name;
    public $sa_subpage_name;
    public $sa_sidemenu_name;

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'sa_college_id', 'sa_college_subpage_id', 'sa_college_subpage_sidemenu_id', 'status', 'created_by', 'updated_by'], 'integer'],
            [['sa_college_name', 'sa_subpage_name', 'sa_sidemenu_name', 'content', 'created_at', 'updated_at'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = SaCollegeSubpageSidemenuDetail::find();
        $query->joinWith(['saCollege', 'saCollegeSubpage', 'saCollegeSubpageSidemenu']);

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => ['defaultOrder' => ['id' => SORT_DESC]]
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'id' => $this->id,
            'sa_college_id' => $this->sa_college_id,
            'sa_college_subpage_id' => $this->sa_college_subpage_id,
            'sa_college_subpage_sidemenu_id' => $this->sa_college_subpage_sidemenu_id,
            'sa_college_subpage_sidemenu_detail.status' => $this->status,
            'created_by' => $this->created_by,
            'updated_by' => $this->updated_by,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ]);

        $query->andFilterWhere(['like', 'content', $this->content])
            ->andFilterWhere(['like', 'sa_college.name', $this->sa_college_name])
            ->andFilterWhere(['like', 'sa_college_subpage_sidemenu.name', $this->sa_sidemenu_name])
            ->andFilterWhere(['like', 'sa_college_subpage.name', $this->sa_subpage_name]);

        return $dataProvider;
    }
}
