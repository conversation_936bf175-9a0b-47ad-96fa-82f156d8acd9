<?php

namespace backend\models;

use common\models\CollegeCourse;
use common\models\CollegeCourseContent;
use common\models\CollegeProgram;
use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use common\models\CourseProgramDates;

/**
 * CourseProgramDatesSearch represents the model behind the search form of `common\models\CourseProgramDates`.
 */
class CourseProgramDatesSearch extends CourseProgramDates
{
    public $course;
    public $program;
    public $college;

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'entity_type', 'entity_id', 'type', 'status'], 'integer'],
            [['name', 'slug', 'start', 'end', 'created_at', 'updated_at', 'course', 'college', 'program'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function home($params)
    {
        // dd($param);
        if ($params['entity_type'] == 1) {
            return self::courses($params);
        }

        if ($params['entity_type'] == 2) {
            return self::programs($params);
        }
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function courses($params)
    {
        $query = CollegeCourseContent::find()
            ->where(['college_id' => $params['college_id']]);

        $query->joinWith(['college', 'course']);

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => ['defaultOrder' => ['id' => SORT_DESC]]
        ]);

        $this->load($params);

        if (!$this->validate()) {
            return $dataProvider;
        }

        // // grid filtering conditions
        $query->andFilterWhere(['like', 'course.name', $this->course])
            ->andFilterWhere(['like', 'college.name', $this->college]);


        return $dataProvider;
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function programs($params)
    {
        $query = CollegeProgram::find()
            ->where(['college_id' => $params['college_id']])
            ->andWhere(['NOT', ['program_id' => null]]);

        $query->joinWith(['college', 'program']);

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => ['defaultOrder' => ['id' => SORT_DESC]]
        ]);

        $this->load($params);

        if (!$this->validate()) {
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere(['like', 'program.name', $this->program])
            ->andFilterWhere(['like', 'college.name', $this->college]);

        return $dataProvider;
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = CourseProgramDates::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => ['defaultOrder' => ['id' => SORT_DESC]]
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'id' => $this->id,
            'entity_type' => $this->entity_type,
            'entity_id' => $this->entity_id,
            'start' => $this->start,
            'end' => $this->end,
            'type' => $this->type,
            'status' => $this->status,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ]);

        $query->andFilterWhere(['like', 'name', $this->name])
            ->andFilterWhere(['like', 'slug', $this->slug]);

        return $dataProvider;
    }
}
