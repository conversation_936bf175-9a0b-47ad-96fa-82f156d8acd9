<?php

namespace backend\models;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use common\models\SaCountryDetail;

/**
 * SaCountryDetailSearch represents the model behind the search form of `common\models\SaCountryDetail`.
 */
class SaCountryDetailSearch extends SaCountryDetail
{
    public $sa_country_name;
    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'sa_country_id', 'status', 'created_by', 'updated_by'], 'integer'],
            [['title', 'description', 'h1', 'meta_title', 'meta_description', 'cover_image', 'related_article_h1', 'content', 'created_at', 'updated_at', 'sa_country_name'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = SaCountryDetail::find();
        $query->joinWith(['saCountry']);

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => ['defaultOrder' => ['id' => SORT_DESC]]
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'id' => $this->id,
            'sa_country_id' => $this->sa_country_id,
            'status' => $this->status,
            'created_by' => $this->created_by,
            'updated_by' => $this->updated_by,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ]);

        $query->andFilterWhere(['like', 'title', $this->title])
            ->andFilterWhere(['like', 'description', $this->description])
            ->andFilterWhere(['like', 'sa_country.name', $this->sa_country_name])
            ->andFilterWhere(['like', 'h1', $this->h1])
            ->andFilterWhere(['like', 'meta_title', $this->meta_title])
            ->andFilterWhere(['like', 'meta_description', $this->meta_description])
            ->andFilterWhere(['like', 'cover_image', $this->cover_image])
            ->andFilterWhere(['like', 'related_article_h1', $this->related_article_h1])
            ->andFilterWhere(['like', 'content', $this->content]);

        return $dataProvider;
    }
}
