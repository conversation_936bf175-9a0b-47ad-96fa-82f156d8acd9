<?php

namespace backend\models;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use common\models\GetgisArticle;

/**
 * GetgisArticleSearch represents the model behind the search form of `common\models\GetgisArticle`.
 */
class GetgisArticleSearch extends GetgisArticle
{
    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'category_id', 'view_count', 'is_popular', 'status', 'created_by', 'is_published', 'updated_by', 'published_by'], 'integer'],
            [['author_id', 'title', 'slug', 'cover_image', 'thumbnail_image', 'description', 'h1', 'meta_title', 'meta_description', 'reading_time', 'published_at', 'created_at', 'updated_at'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = GetgisArticle::find();
        $query->joinWith(['backendauthor']);

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => ['defaultOrder' => ['id' => SORT_DESC]]
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'id' => $this->id,
            'category_id' => $this->category_id,
            'view_count' => $this->view_count,
            'getgis_article.is_popular' => $this->is_popular,
            'getgis_article.is_published' => $this->is_published,
            'getgis_article.status' => $this->status,
            'getgis_article.created_by' => $this->created_by,
            'getgis_article.updated_by' => $this->updated_by,
            'published_by' => $this->published_by,
            'published_at' => $this->published_at,
        ]);

        $query->andFilterWhere(['like', 'title', $this->title])
            ->andFilterWhere(['like', 'slug', $this->slug])
            ->andFilterWhere(['like', 'cover_image', $this->cover_image])
            ->andFilterWhere(['like', 'thumbnail_image', $this->thumbnail_image])
            ->andFilterWhere(['like', 'description', $this->description])
            ->andFilterWhere(['like', 'h1', $this->h1])
            ->andFilterWhere(['like', 'meta_title', $this->meta_title])
            ->andFilterWhere(['like', 'meta_description', $this->meta_description])
            ->andFilterWhere(['like', 'reading_time', $this->reading_time])
            ->andFilterWhere(['like', 'user.name', $this->author_id]);

        $query->andFilterWhere(['DATE(getgis_article.created_at)' => $this->created_at ? date('Y-m-d', strtotime($this->created_at)) : null])
            ->andFilterWhere(['DATE(getgis_article.updated_at)' => $this->updated_at ? date('Y-m-d', strtotime($this->updated_at)) : null]);

        return $dataProvider;
    }
}
