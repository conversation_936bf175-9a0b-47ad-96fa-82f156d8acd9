<?php

namespace backend\models;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use common\models\SaCollegeDetail;

/**
 * SaCollegeDetailSearch represents the model behind the search form of `common\models\SaCollegeDetail`.
 */
class SaCollegeDetailSearch extends SaCollegeDetail
{
    public $sa_college_name;

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'sa_college_id', 'international_students', 'total_enrollment', 'accepts_direct_application', 'accepts_common_application', 'total_applications', 'status', 'minimum_toefl_type', 'minimum_ielts_type'], 'integer'],
            [['student_faculty_ratio', 'acceptance_rate', 'created_at', 'updated_at', 'sa_college_name', 'male_female_ratio', 'minimum_toefl', 'minimum_ielts', 'created_by', 'updated_by'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = SaCollegeDetail::find();
        $query->joinWith(['saCollege']);

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => ['defaultOrder' => ['id' => SORT_DESC]]
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'id' => $this->id,
            'sa_college_id' => $this->sa_college_id,
            'international_students' => $this->international_students,
            'total_enrollment' => $this->total_enrollment,
            'minimum_toefl' => $this->minimum_toefl,
            'minimum_ielts' => $this->minimum_ielts,
            'accepts_direct_application' => $this->accepts_direct_application,
            'accepts_common_application' => $this->accepts_common_application,
            'total_applications' => $this->total_applications,
            'sa_college_detail.status' => $this->status,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ]);

        $query->andFilterWhere(['like', 'student_faculty_ratio', $this->student_faculty_ratio])
            ->andFilterWhere(['like', 'sa_college.name', $this->sa_college_name])
            ->andFilterWhere(['like', 'acceptance_rate', $this->acceptance_rate]);

        return $dataProvider;
    }
}
