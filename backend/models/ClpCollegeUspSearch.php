<?php

namespace backend\models;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use common\models\ClpCollegeUsp;

/**
 * ClpCollegeUspSearch represents the model behind the search form of `common\models\ClpCollegeUsp`.
 */
class ClpCollegeUspSearch extends ClpCollegeUsp
{
    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'college_id', 'status'], 'integer'],
            [['usp_title', 'usp_sub_title', 'created_at', 'updated_at'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = ClpCollegeUsp::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => ['defaultOrder' => ['id' => SORT_DESC]]
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'id' => $this->id,
            'college_id' => $this->college_id,
            'status' => $this->status,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ]);

        $query->andFilterWhere(['like', 'usp_title', $this->usp_title])
            ->andFilterWhere(['like', 'usp_sub_title', $this->usp_sub_title]);

        return $dataProvider;
    }
}
