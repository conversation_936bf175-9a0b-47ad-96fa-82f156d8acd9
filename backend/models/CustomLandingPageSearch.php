<?php

namespace backend\models;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use common\models\CustomLandingPage;
use common\models\CustomLandingPageLead;

/**
 * CustomLandingPageSearch represents the model behind the search form of `common\models\CustomLandingPage`.
 */
class CustomLandingPageSearch extends CustomLandingPage
{
    public $created_at_from;
    public $created_at_to;

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'template_id', 'city_id', 'state_id', 'college_id', 'stream_id', 'level_id', 'course_id', 'program_id', 'status'], 'integer'],
            [['page_title', 'page_sub_title', 'page_description', 'form_title', 'form_description', 'form_cta_text', 'featured_content', 'created_at', 'updated_at', 'slug', 'created_at_from', 'created_at_to', 'domain'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = CustomLandingPage::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => ['defaultOrder' => ['id' => SORT_DESC]]
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'id' => $this->id,
            'template_id' => $this->template_id,
            'city_id' => $this->city_id,
            'state_id' => $this->state_id,
            'college_id' => $this->college_id,
            'stream_id' => $this->stream_id,
            'level_id' => $this->level_id,
            'course_id' => $this->course_id,
            'program_id' => $this->program_id,
            'status' => $this->status,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ]);

        $query->andFilterWhere(['like', 'page_title', $this->page_title])
            ->andFilterWhere(['like', 'page_sub_title', $this->page_sub_title])
            ->andFilterWhere(['like', 'page_description', $this->page_description])
            ->andFilterWhere(['like', 'form_title', $this->form_title])
            ->andFilterWhere(['like', 'form_description', $this->form_description])
            ->andFilterWhere(['like', 'form_cta_text', $this->form_cta_text])
            ->andFilterWhere(['like', 'slug', $this->slug])
            ->andFilterWhere(['like', 'domain', $this->domain])
            ->andFilterWhere(['like', 'featured_content', $this->featured_content]);

        return $dataProvider;
    }

    //lead count search
    public function leadCountSearch($params, $clpSlug)
    {
        $this->load($params);

        $query = CustomLandingPageLead::find()
            ->select([
                'DATE(created_at) as created_date',
                'COUNT(*) as total_leads',
                'COUNT(DISTINCT phone) as distinct_leads',
                'slug'
            ])->where(['slug' => $clpSlug]);


        if (!empty($this->created_at_from)) {
            $query->andWhere(['>=', 'created_at', $this->created_at_from . ' 00:00:00']);
        }

        if (!empty($this->created_at_to)) {
            $query->andWhere(['<=', 'created_at', $this->created_at_to . ' 23:59:59']);
        }
        
        $query->groupBy(['DATE(created_at)'])
            ->orderBy(['created_date' => SORT_DESC])
            ->asArray();

        $dataProvider = new \yii\data\ArrayDataProvider([
            'allModels' => $query->all(),
            'pagination' => ['pageSize' => 10],
            'sort' => false,
        ]);

        return $dataProvider;
    }
}
