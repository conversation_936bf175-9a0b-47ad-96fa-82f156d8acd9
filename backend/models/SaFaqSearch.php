<?php

namespace backend\models;

use common\models\SaCollege;
use common\models\SaCountry;
use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use common\models\SaFaq;

/**
 * SaFaqSearch represents the model behind the search form of `common\models\SaFaq`.
 */
class SaFaqSearch extends SaFaq
{
    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'status', 'entity'], 'integer'],
            [['entity_id', 'qnas', 'created_at', 'updated_at'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = SaFaq::find();
        $query->joinWith(['country', 'college']);

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => ['defaultOrder' => ['id' => SORT_DESC]]
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'id' => $this->id,
            'status' => $this->status,
            'entity' => $this->entity,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ]);

        $query->andFilterWhere(['like', 'qnas', $this->qnas])
            ->andFilterWhere(['or', ['like', 'sa_country.name', $this->entity_id], ['like', 'sa_college.name', $this->entity_id]]);

        return $dataProvider;
    }
}
