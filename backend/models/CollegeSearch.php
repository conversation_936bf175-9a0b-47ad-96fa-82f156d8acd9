<?php

namespace backend\models;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use common\models\College;
use yii\behaviors\SluggableBehavior;

/**
 * CollegeSearch represents the model behind the search form of `common\models\College`.
 */
class CollegeSearch extends College
{
    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'parent_id', 'status', 'is_popular'], 'integer'],
            [['name', 'slug', 'address', 'city_id', 'image', 'type', 'created_at', 'updated_at'], 'safe'],
            [['latitude', 'longitude'], 'number'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'sluggable' => [
                'class' => SluggableBehavior::class,
                'attribute' => 'slug',
            ],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = College::find()->where(['not', ['college.status' => College::STATUS_IS_DELETED]]);
        $query->joinWith(['city']);

        // add conditions that should always apply here
        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => ['defaultOrder' => ['id' => SORT_DESC]]
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            // 'college.id' => $this->id,
            // 'city_id' => $this->city_id,
            'latitude' => $this->latitude,
            'longitude' => $this->longitude,
            'parent_id' => $this->parent_id,
            'college.status' => $this->status,
            'college.is_popular' => $this->is_popular,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ]);

        $query->andFilterWhere(['like', 'college.id', $this->id])
            ->andFilterWhere(['like', 'college.name', $this->name])
            ->andFilterWhere(['like', 'college.slug', $this->slug])
            ->andFilterWhere(['like', 'city.name', $this->city_id])
            ->andFilterWhere(['like', 'college.address', $this->address])
            ->andFilterWhere(['like', 'college.image', $this->image])
            ->andFilterWhere(['like', 'college.type', $this->type]);

        return $dataProvider;
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function sponsored($params)
    {
        $query = College::find()
            ->where(['not', ['college.status' => College::STATUS_IS_DELETED]])
            ->andWhere(['is_sponsored' => College::SPONSORED_YES]);

        $query->joinWith(['city']);

        // add conditions that should always apply here
        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => ['defaultOrder' => ['id' => SORT_DESC]]
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'id' => $this->id,
            // 'city_id' => $this->city_id,
            'latitude' => $this->latitude,
            'longitude' => $this->longitude,
            'parent_id' => $this->parent_id,
            'college.status' => $this->status,
            'college.is_popular' => $this->is_popular,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ]);

        $query->andFilterWhere(['like', 'college.name', $this->name])
            ->andFilterWhere(['like', 'college.slug', $this->slug])
            ->andFilterWhere(['like', 'city.name', $this->city_id])
            ->andFilterWhere(['like', 'college.address', $this->address])
            ->andFilterWhere(['like', 'college.image', $this->image])
            ->andFilterWhere(['like', 'college.type', $this->type]);

        return $dataProvider;
    }
}
