<?php

namespace backend\models;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use common\models\TrendingPage;

/**
 * TrendingPageSearch represents the model behind the search form of `common\models\TrendingPage`.
 */
class TrendingPageSearch extends TrendingPage
{
    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'status'], 'integer'],
            [['display_name', 'entity', 'entity_id', 'url', 'expires_at', 'created_at', 'updated_at'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = TrendingPage::find();
        $query->joinWith(['exam', 'college', 'board', 'course']);

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => ['defaultOrder' => ['id' => SORT_DESC]]
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'id' => $this->id,
            'trending_pages.status' => $this->status,
            'trending_pages.display_name' => $this->display_name,
            'expires_at' => $this->expires_at,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ]);

        $query->andFilterWhere(['like', 'trending_pages.display_name', $this->display_name])
        ->andFilterWhere(['like', 'trending_pages.entity', $this->entity])
        ->andFilterWhere(['or', ['like', 'exam.name', $this->entity_id], ['like', 'board.display_name', $this->entity_id], ['like', 'college.name', $this->entity_id]])
        ->andFilterWhere(['like', 'trending_pages.url', $this->url]);

        return $dataProvider;
    }
}
