<?php

namespace backend\models;

use common\helpers\DataHelper;
use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use common\models\AuditTrail;
use yii\data\ArrayDataProvider;

/**
 * AuditTrailSearch represents the model behind the search form of `common\models\AuditTrail`.
 */
class AuditTrailSearch extends AuditTrail
{
    /**
     * @inheritdoc
     */
    public $entity_id;
    public $name;
    public $page;
    public $user_name;

    public function rules()
    {
        return [
            [['id', 'entity_id', 'entry_id', 'user_id'], 'integer'],
            [['action', 'model', 'model_id', 'field', 'old_value', 'new_value', 'created', 'name', 'page', 'user_name'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params, $outputArray)
    {
        // First DataProvider with pre-fetched array data
        $arrayDataProvider = new ArrayDataProvider([
            'allModels' => $outputArray,
            'pagination' => ['pageSize' => 20],
        ]);

        // Then build the DB query and create ActiveDataProvider
        $this->load($params);

        $model = $params['AuditTrail']['model'] ?? '';
        $model_id = DataHelper::$audit_entity[$model];
        $dbName = Yii::$app->db->createCommand('SELECT DATABASE()')->queryScalar();

        $query = AuditTrail::find()->from('gmu_log.audit_trail')
            ->innerJoin($dbName . '.' . $model_id, $model_id . '.id = audit_trail.model_id');

        if ($model_id == 'college') {
            $query->innerJoin($dbName . '.sponsor_college', 'sponsor_college.college_id = audit_trail.model_id');
        }
        $query->innerJoin($dbName . '.user', 'user.id = audit_trail.user_id');

        if (!$this->validate()) {
            $query->where('0=1'); // no results if invalid
        } else {
            $query->andFilterWhere([
                'audit_trail.id' => $this->id,
                $model_id . '.id' => $this->entity_id,
                'audit_trail.user_id' => $this->user_id,
                'audit_trail.created' => $this->created,
            ]);
            $query->andFilterWhere(['like', 'user.name', $this->user_name])
                ->andFilterWhere(['like', 'name', $this->name])
                ->andFilterWhere(['like', 'page', $this->page])
                ->andWhere(['in', 'field', ['content', 'description']]);
        }

        $activeDataProvider = new ActiveDataProvider([
            'query' => $query,
            'pagination' => ['pageSize' => 20],
            'sort' => ['defaultOrder' => ['audit_trail.id' => SORT_DESC]],
        ]);

        // Return both DataProviders in an array or as properties
        return [
            'arrayDataProvider' => $arrayDataProvider,
            'activeDataProvider' => $activeDataProvider,
        ];
    }
}
