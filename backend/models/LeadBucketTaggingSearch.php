<?php

namespace backend\models;

use common\models\Article;
use yii\base\Model;
use yii\db\Query;
use yii\data\ActiveDataProvider;
use common\helpers\DataHelper;
use common\helpers\BoardHelper;
use yii\helpers\ArrayHelper;
use common\helpers\CollegeHelper;
use common\helpers\CourseHelper;
use common\helpers\CareerHelper;
use yii\data\ArrayDataProvider;
use common\models\LeadBucketTagging;
use common\models\LeadBucket;
use common\models\BoardPages;
use common\models\LeadBucketCta;
use common\models\News;
use common\models\NewsSubdomain;
use common\models\NcertArticles;
use frontend\helpers\Url;

/**
 * LeadBucketTaggingSearch represents the model behind the search form of `common\models\LeadBucketTagging`.
 */
class LeadBucketTaggingSearch extends LeadBucketTagging
{
    public $page;
    public $text;
    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'entity', 'entity_id', 'bucket_id', 'status'], 'integer'],
            [['created_at', 'updated_at', 'sub_page', 'page', 'text'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        // $query = LeadBucketTagging::find()->select(['sub_page'])->distinct()->where(['entity' => $params['entity']]);
        $entity = ArrayHelper::getValue(DataHelper::getConstantList('LEAD_ENTITY', LeadBucketTagging::class), $params['entity']);
        $allBoardSubpages =  ArrayHelper::map(BoardPages::find()->where(['status'=>BoardPages::STATUS_ACTIVE])->andWhere(['is', 'parent_id', new \yii\db\Expression('null')])->all(), 'slug', 'name');
        // add conditions that should always apply here

        $entitySubPageArr = [
            'Article' => [],
            'News' => [],
            'Boards' => $allBoardSubpages,
            'Colleges' => CollegeHelper::$subPages,
            'Exam' => DataHelper::examContentList(),
            'Courses' => CourseHelper::$subPages,
            'Career' => CareerHelper::$subPages,
            'Ncert' => [],
            'College Listing' => ['college-listing' => 'College Lisiting'],
            'News And Article' => [],
            'Scholarship' => ['overview' => 'Overview'],
            'Olympiad' => DataHelper::$olympiadSubPages,
            'College Admission' => ['college-admission' => 'College Admission']
        ];

        $dataProvider = new ArrayDataProvider([
            'allModels' => !empty($entitySubPageArr[$entity]) ? $entitySubPageArr[$entity] : '',
            'pagination' => ['pageSize' => 20],
        ]);
        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // $query->andFilterWhere(['like', 'sub_page', $this->sub_page]);

        return $dataProvider;
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function page($params)
    {
        if ($params['entity'] == LeadBucketTagging::LEAD_ENTITY_ARTICLE) {
            $query = Article::find()->select(['id', 'slug']);
            $tableName = 'article.slug';
        } else if ($params['entity'] == LeadBucketTagging::LEAD_ENTITY_NEWS) {
            //$query = News::find(['id', 'slug']);
            $query = NewsSubdomain::find(['id', 'slug']);
            $tableName = 'news_subdomain.slug';
        } else if ($params['entity'] == LeadBucketTagging::LEAD_ENTITY_NCERT) {
            $query = NcertArticles::find()->select(['id', 'slug']);
            $tableName = 'ncert_articles.slug';
        } else if ($params['entity'] == LeadBucketTagging::LEAD_ENTITY_NEWS_AND_ARTICLE) {
            $query = LeadBucket::find()->select(['id', 'bucket'])->where(['entity_id' => 10]);
            $tableName = 'lead_bucket.bucket';
        }

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => ['defaultOrder' => ['id' => SORT_DESC]]
        ]);
        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        $query->andFilterWhere(['like', 'entity_id', $this->entity_id]);
        $query->andFilterWhere(['like', $tableName, $this->page]);

        return $dataProvider;
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function home($params)
    {
        // $query = LeadBucketTagging::find()->select(['entity'])->distinct()->orderBy(['entity' => SORT_ASC]);
        // dd($query->createCommand()->getRawSql());
        $query = LeadBucket::find()->select(['entity_id'])->distinct()->where(['not in', 'entity_id', [LeadBucket::LEAD_ENTITY_NEWS_AND_ARTICLE, LeadBucket::LEAD_ENTITY_NEWS, LeadBucket::LEAD_ENTITY_ARTICLE]])->orderBy(['entity_id' => SORT_ASC]);

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => ['defaultOrder' => ['id' => SORT_DESC]]
        ]);

        $this->load($params);

        if (!$this->validate()) {
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere(['like', 'entity_id', $this->entity]);

        return $dataProvider;
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function position($params)
    {
        $query = new Query();
        if (isset($params['entity_id']) && !empty($params['entity_id'])) {
            $query->select(['lead_bucket_cta.cta_position', 'lead_bucket_cta.id', 'bucket_id'])
                ->from('lead_bucket_cta')
                ->innerJoin('lead_bucket', 'lead_bucket.id = lead_bucket_cta.bucket_id')
                ->innerJoin('lead_bucket_tagging', 'lead_bucket_tagging.bucket_id = lead_bucket_cta.bucket_id')
                ->where(['lead_bucket_tagging.entity' => $params['entity']])
                ->andWhere(['lead_bucket_tagging.entity_id' => $params['entity_id']]);
        } else {
            $query->select(['lead_bucket_cta.cta_position', 'lead_bucket_cta.id', 'bucket_id'])
                ->from('lead_bucket_cta')
                ->innerJoin('lead_bucket', 'lead_bucket.id = lead_bucket_cta.bucket_id')
                ->innerJoin('lead_bucket_tagging', 'lead_bucket_tagging.bucket_id = lead_bucket_cta.bucket_id')
                ->where(['lead_bucket_tagging.entity' => $params['entity']])
                ->andWhere(['lead_bucket_tagging.sub_page' => $params['page']]);
        }

        $dataProvider = new ActiveDataProvider([
            'query' => $query ?? [],
            'sort' => ['defaultOrder' => ['id' => SORT_DESC]]
        ]);

        $this->load($params);

        if (!$this->validate()) {
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere(['like', 'entity', $this->entity])
            ->andFilterWhere(['like', 'lead_bucket1.cta_text', $this->text]);

        return $dataProvider;
    }

    public function default($params)
    {
        $query = new Query();
        $query->select(['cta_position', 'web', 'wap'])
            ->from('lead_bucket_cta')
            ->innerJoin('lead_bucket_tagging', 'lead_bucket_tagging.bucket_id = lead_bucket_cta.bucket_id')
            ->where(['lead_bucket_tagging.entity' => $params['entity']]);

        $dataProvider = new ActiveDataProvider([
            'query' => $query ?? [],
            'sort' => ['defaultOrder' => ['cta_position' => SORT_DESC]]
        ]);

        $this->load($params);

        if (!$this->validate()) {
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere(['like', 'entity', $this->entity])
            ->andFilterWhere(['like', 'lead_bucket_cta.cta_text', $this->text]);

        return $dataProvider;
    }
}
