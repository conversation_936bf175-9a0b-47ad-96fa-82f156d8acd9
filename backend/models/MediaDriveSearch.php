<?php

namespace backend\models;

use yii\base\Model;
use yii\data\ActiveDataProvider;
use common\models\MediaDrive;

/**
 * MediaDriveSearch represents the model behind the search form of `common\models\MediaDrive`.
 */
class MediaDriveSearch extends MediaDrive
{
    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id'], 'integer'],
            [['file_name', 'entity_id', 'created_at', 'updated_at', 'sub_page'], 'safe'],
            [['description', 'entity', 'page'], 'string'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = MediaDrive::find();
        $query->joinWith(['exam', 'college', 'board', 'uploadType']);

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => ['defaultOrder' => ['id' => SORT_DESC]]
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }
        // grid filtering conditions
        $query->andFilterWhere([
            'id' => $this->id,
            // 'sub_page' => $this->sub_page,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ]);

        $query->andFilterWhere(['like', 'media_drive.file_name', $this->file_name])
        ->andFilterWhere(['like', 'media_drive.page', $this->page])
        ->andFilterWhere(['like', 'media_drive_upload_type.upload_type', $this->sub_page])
        ->andFilterWhere(['like', 'media_drive.description', $this->description])
        ->andFilterWhere(['or', ['like', 'exam.name', $this->entity_id], ['like', 'board.display_name', $this->entity_id], ['like', 'college.name', $this->entity_id]])
        ->andFilterWhere(['like', 'media_drive.entity', $this->entity]);
        return $dataProvider;
    }
}
