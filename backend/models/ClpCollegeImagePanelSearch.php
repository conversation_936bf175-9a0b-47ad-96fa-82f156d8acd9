<?php

namespace backend\models;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use common\models\ClpCollegeImagePanel;

/**
 * ClpCollegeImagePanelSearch represents the model behind the search form of `common\models\ClpCollegeImagePanel`.
 */
class ClpCollegeImagePanelSearch extends ClpCollegeImagePanel
{
    public $college_name;

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'college_id', 'status'], 'integer'],
            [['logo_image', 'banner_image', 'created_at', 'updated_at', 'college_name'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = ClpCollegeImagePanel::find();
        $query->joinWith(['college']);

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => ['defaultOrder' => ['id' => SORT_DESC]]
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'id' => $this->id,
            // 'college_id' => $this->college_id,
            'clp_college_image_panel.status' => $this->status,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ]);

        $query->andFilterWhere(['like', 'logo_image', $this->logo_image])
            ->andFilterWhere(['like', 'banner_image', $this->banner_image])
            ->andFilterWhere(['like', 'college.name', $this->college_name]);

        return $dataProvider;
    }
}
