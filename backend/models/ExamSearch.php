<?php

namespace backend\models;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use common\models\Exam;

/**
 * ExamSearch represents the model behind the search form of `common\models\Exam`.
 */
class ExamSearch extends Exam
{
    public $streams;
    public $courses;
    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'parent_id', 'level', 'mode', 'type', 'is_popular', 'status', 'created_at', 'updated_at','lang_code'], 'integer'],
            [['name', 'slug', 'display_name', 'cover_image', 'streams', 'courses'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = Exam::find();
        // $query->joinWith(['streams']);
        // $query->joinWith(['courses']);

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'pagination' => ['pageSize' => 50],
            'sort' => ['defaultOrder' => ['id' => SORT_DESC]]
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'id' => $this->id,
            'parent_id' => $this->parent_id,
            'level' => $this->level,
            'mode' => $this->mode,
            'type' => $this->type,
            'is_popular' => $this->is_popular,
            'status' => $this->status,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'stream.name' => $this->streams,
            'course.name' => $this->courses
        ]);

        $query->andFilterWhere(['like', 'exam.name', $this->name])
            ->andFilterWhere(['like', 'exam.slug', $this->slug])
            ->andFilterWhere(['like', 'exam.display_name', $this->display_name])
            ->andFilterWhere(['like', 'cover_image', $this->cover_image])
            ->andFilterWhere(['like', 'exam.lang_code', $this->lang_code]);
        // dd($dataProvider->getModels());
        return $dataProvider;
    }
}
