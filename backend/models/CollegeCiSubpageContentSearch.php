<?php

namespace backend\models;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use common\models\CollegeCiSubpageContent;

/**
 * CollegeCiSubpageContentSearch represents the model behind the search form of `common\models\CollegeCiSubpageContent`.
 */
class CollegeCiSubpageContentSearch extends CollegeCiSubpageContent
{
    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'college_course_content_id', 'year', 'status'], 'integer'],
            [['subpage', 'content', 'created_at', 'updated_at'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = CollegeCiSubpageContent::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => ['defaultOrder' => ['id' => SORT_DESC]]
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'id' => $this->id,
            'college_course_content_id' => $this->college_course_content_id,
            'year' => $this->year,
            'status' => $this->status,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ]);

        $query->andFilterWhere(['like', 'subpage', $this->subpage])
            ->andFilterWhere(['like', 'content', $this->content]);

        return $dataProvider;
    }
}
