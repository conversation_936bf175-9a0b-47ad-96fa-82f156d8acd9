<?php

namespace backend\models;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use common\models\SaCourseDetail;

/**
 * SaCourseDetailSearch represents the model behind the search form of `common\models\SaCourseDetail`.
 */
class SaCourseDetailSearch extends SaCourseDetail
{
    public $sa_college_name;
    public $sa_course_name;
    public $sa_specialization_name;
    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'sa_course_id', 'sa_college_id', 'sa_stream_id', 'sa_degree_id', 'sa_specialization_id', 'total_fees', 'duration', 'duration_type', 'sa_course_duration_type_id', 'is_published', 'status'], 'integer'],
            [['created_at', 'updated_at', 'created_by', 'updated_by', 'sa_college_name', 'sa_course_name', 'sa_specialization_name'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = SaCourseDetail::find();
        $query->joinWith(['saCollege', 'saCourse', 'saSpecialization']);

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => ['defaultOrder' => ['id' => SORT_DESC]]
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'id' => $this->id,
            'sa_course_id' => $this->sa_course_id,
            'sa_college_id' => $this->sa_college_id,
            'sa_stream_id' => $this->sa_stream_id,
            'sa_degree_id' => $this->sa_degree_id,
            'sa_specialization_id' => $this->sa_specialization_id,
            'total_fees' => $this->total_fees,
            'duration' => $this->duration,
            'duration_type' => $this->duration_type,
            'sa_course_duration_type_id' => $this->sa_course_duration_type_id,
            'is_published' => $this->is_published,
            'sa_course_detail.status' => $this->status,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ]);

        $query->andFilterWhere(['like', 'sa_course.name', $this->sa_course_name])
            ->andFilterWhere(['like', 'sa_college.name', $this->sa_college_name])
            ->andFilterWhere(['like', 'sa_specialization.name', $this->sa_specialization_name]);

        return $dataProvider;
    }
}
