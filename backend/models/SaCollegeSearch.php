<?php

namespace backend\models;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use common\models\SaCollege;

/**
 * SaCollegeSearch represents the model behind the search form of `common\models\SaCollege`.
 */
class SaCollegeSearch extends SaCollege
{
    public $sa_country_name;

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'sa_country_id', 'established_year', 'is_popular', 'position', 'status', 'is_deleted'], 'integer'],
            [['name', 'display_name', 'slug', 'email', 'address', 'cover_image', 'logo_image', 'url', 'location', 'phone', 'created_at', 'updated_at', 'sa_country_name', 'created_by', 'updated_by'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = SaCollege::find();
        $query->joinWith(['saCountry']);

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => ['defaultOrder' => ['id' => SORT_DESC]]
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'id' => $this->id,
            'sa_country_id' => $this->sa_country_id,
            'established_year' => $this->established_year,
            'is_popular' => $this->is_popular,
            'position' => $this->position,
            'status' => $this->status,
            'is_deleted' => $this->is_deleted,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ]);

        $query->andFilterWhere(['like', 'sa_college.name', $this->name])
            ->andFilterWhere(['like', 'display_name', $this->display_name])
            ->andFilterWhere(['like', 'sa_country.name', $this->sa_country_name])
            ->andFilterWhere(['like', 'sa_college.slug', $this->slug])
            ->andFilterWhere(['like', 'email', $this->email])
            ->andFilterWhere(['like', 'address', $this->address])
            ->andFilterWhere(['like', 'cover_image', $this->cover_image])
            ->andFilterWhere(['like', 'logo_image', $this->logo_image])
            ->andFilterWhere(['like', 'url', $this->url])
            ->andFilterWhere(['like', 'location', $this->location])
            ->andFilterWhere(['like', 'phone', $this->phone]);

        return $dataProvider;
    }
}
