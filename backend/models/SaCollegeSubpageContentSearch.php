<?php

namespace backend\models;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use common\models\SaCollegeSubpageContent;

/**
 * SaCollegeSubpageContentSearch represents the model behind the search form of `common\models\SaCollegeSubpageContent`.
 */
class SaCollegeSubpageContentSearch extends SaCollegeSubpageContent
{
    public $sa_subpage_name;
    public $sa_college_name;
    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'sa_college_id', 'sa_college_subpage_id', 'status', 'created_by', 'updated_by'], 'integer'],
            [['title', 'description', 'h1', 'meta_title', 'meta_description', 'content', 'created_at', 'updated_at', 'sa_subpage_name', 'sa_college_name'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = SaCollegeSubpageContent::find();
        $query->joinWith(['saCollege', 'saCollegeSubpage']);

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'sort' => ['defaultOrder' => ['id' => SORT_DESC]]
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'id' => $this->id,
            'sa_college_id' => $this->sa_college_id,
            'sa_college_subpage_id' => $this->sa_college_subpage_id,
            'sa_college_subpage_content.status' => $this->status,
            'created_by' => $this->created_by,
            'updated_by' => $this->updated_by,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ]);

        $query->andFilterWhere(['like', 'title', $this->title])
            ->andFilterWhere(['like', 'description', $this->description])
            ->andFilterWhere(['like', 'sa_college_subpage.name', $this->sa_subpage_name])
            ->andFilterWhere(['like', 'sa_college.name', $this->sa_college_name])
            ->andFilterWhere(['like', 'h1', $this->h1])
            ->andFilterWhere(['like', 'meta_title', $this->meta_title])
            ->andFilterWhere(['like', 'meta_description', $this->meta_description])
            ->andFilterWhere(['like', 'content', $this->content]);

        return $dataProvider;
    }
}
