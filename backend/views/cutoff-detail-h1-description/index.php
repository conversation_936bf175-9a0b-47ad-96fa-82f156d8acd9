<?php

use yii\helpers\Html;
use yii\grid\GridView;

/* @var $this yii\web\View */
/* @var $searchModel backend\models\CutoffDetailH1DescriptionSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Cutoff Detail H1descriptions';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="cutoff-detail-h1-description-index box box-primary">
    <div class="box-header with-border">
        <?= Html::a('Create Cutoff Detail H1description', ['create'], ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?php // echo $this->render('_search', ['model' => $searchModel]); ?>
        <?= GridView::widget([
            'dataProvider' => $dataProvider,
            'filterModel' => $searchModel,
            'layout' => "{items}\n{summary}\n{pager}",
            'columns' => [
                ['class' => 'yii\grid\SerialColumn'],

                'id',
                'college_id',
                'course_id',
                'h1:ntext',
                'description:ntext',
                // 'status',
                // 'created_at',
                // 'updated_at',

                ['class' => 'yii\grid\ActionColumn'],
            ],
        ]); ?>
    </div>
</div>
