<?php

use common\helpers\DataHelper;
use common\models\College;
use common\models\Course;
use common\models\CutoffDetailH1Description;
use yii\bootstrap\ActiveForm;
use unclead\multipleinput\TabularInput;
use yii\helpers\Html;
use unclead\multipleinput\TabularColumn;
use yii\helpers\ArrayHelper;

$courses = Course::find()->select(['id', 'name'])->where(['id' => $courseIds])->asArray()->all();

// Get default values
$h1Template = DataHelper::$cutOffH1DesDefaultValue['h1'];
$desTemplate = $isSingleProgram ? DataHelper::$cutOffH1DesDefaultValue['single_desc'] : DataHelper::$cutOffH1DesDefaultValue['multi_desc'];

// Extract all {} placeholders
preg_match_all('/\{([^}]+)\}/', $h1Template . ' ' . $desTemplate, $matches);
$placeholders = array_unique($matches[0]);
?>
<style>
    .list-cell__college_id {
        pointer-events: none;
        width: 200px;
    }

    .list-cell__course_id {
        pointer-events: none;
        width: 330px;
    }

    .multiple-input-list__btn.js-input-remove {
        display: none !important;
    }
</style>
<div id="alertMsg">

</div>
<div class="col-md-12 mb-3">
    <div class="alert alert-info">
        <h4 class="mb-2"><strong>Available Placeholders:</strong></h4>
        <ul>
            <?php foreach ($placeholders as $placeholder): ?>
                <li><code><?= Html::encode($placeholder) ?></code></li>
            <?php endforeach; ?>
        </ul>
    </div>
</div>
<!-- <div class="exam-date-form box box-primary"> -->
<?php $form = \yii\bootstrap\ActiveForm::begin([
    'id' => 'f-value-tabular-form',
    // 'action' => '/feature-value/create?college-id=' . $college->id,
    'options' => [
        'enctype' => 'multipart/form-data',
    ]
]) ?>
<!-- <div class="box-body"> -->
<div class="col-md-12">
    <?= TabularInput::widget([
        'models' => $cutoffModels,
        'form' => $form,
        'modelClass' => CutoffDetailH1Description::class,
        // 'cloneButton' => true,
        // 'sortable' => true,
        'allowEmptyList' => 'false',
        'min' => 1,
        'max' => count($courses),
        'layoutConfig' => [
            'offsetClass'   => 'col-sm-offset-4',
            'labelClass'    => 'col-sm-2',
            'wrapperClass'  => 'col-sm-10',
            'errorClass'    => 'col-sm-4'
        ],
        'attributeOptions' => [
            'enableAjaxValidation'   => false,
            'enableClientValidation' => true,
            'validateOnChange'       => true,
            'validateOnSubmit'       => true,
            'validateOnBlur'         => false,
        ],
        'columns' => [
            [
                'name' => 'id',
                'type' => TabularColumn::TYPE_HIDDEN_INPUT
            ],
            [
                'name' => 'college_id',
                'title' => 'College',
                'options' => ['readonly' => true, 'class' => 'custom-style-college'],
                'type' => TabularColumn::TYPE_DROPDOWN,
                'items' => ArrayHelper::map(College::find()->select(['id', 'name'])->where(['id' => $college_id])->asArray()->all(), 'id', 'name')
            ],
            [
                'name' => 'course_id',
                'title' => 'Course',
                'options' => ['readonly' => true, 'class' => 'custom-style-course'],
                'type' => TabularColumn::TYPE_DROPDOWN,
                'items' => ArrayHelper::map($courses, 'id', 'name')
            ],
            [
                'name' => 'h1',
                'title' => 'H1',
                'type' => TabularColumn::TYPE_TEXT_INPUT,
            ],
            [
                'name' => 'description',
                'title' => 'Description',
                'type' => TabularColumn::TYPE_TEXT_INPUT,
            ],
            [
                'name' => 'status',
                'title' => 'Status',
                'defaultValue' => CutoffDetailH1Description::STATUS_INACTIVE,
                'type' => TabularColumn::TYPE_DROPDOWN,
                'items' => DataHelper::getConstantList('STATUS', CutoffDetailH1Description::class),
            ],
        ],
    ]) ?>
</div>
<!-- </div> -->

<div class="box-footer">
    <?= Html::submitButton('Save', ['class' => 'btn btn-success btn-flat', 'id' => 'tb-fvalue', 'style' => 'float: right; margin-right: 10px; width: 8%;']) ?>
</div>
<?php ActiveForm::end(); ?>
<!-- </div> -->