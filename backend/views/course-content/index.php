<?php

use common\helpers\DataHelper;
use common\models\CourseContent;
use yii\helpers\Html;
use yii\grid\GridView;
use yii\helpers\ArrayHelper;

/* @var $this yii\web\View */
/* @var $searchModel backend\models\CourseContentSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Course Contents';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="course-content-index box box-primary">
    <div class="box-header with-border">
        <?= Html::a('Create', ['create'], ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?php // echo $this->render('_search', ['model' => $searchModel]);
        ?>
        <?= GridView::widget([
            'dataProvider' => $dataProvider,
            'filterModel' => $searchModel,
            'layout' => "{items}\n{summary}\n{pager}",
            'columns' => [
                ['class' => 'yii\grid\SerialColumn'],
                [
                    'attribute' => 'course_id',
                    'label' => 'Course',
                    'value' => 'course.name',
                ],
                [
                    'attribute' => 'slug',
                    'label' => 'Slug',
                    'value' => 'course.slug'
                ],
                'page',
                [
                    'attribute' => 'status',
                    'value' => function ($model) {
                        return ArrayHelper::getValue(DataHelper::getConstantList('STATUS', CourseContent::class), $model->status);
                    },
                    'filter' => DataHelper::getConstantList('STATUS', CourseContent::class)
                ],
                [
                    'class' => 'yii\grid\ActionColumn',
                    'template' => '{view} {update}',
                    // 'buttons' => [
                    //     'preview' => function ($url) {
                    //         return Html::a('<span class="glyphicon glyphicon-check"></span>', $url, ['title' => 'Preview']);
                    //     },
                    // ],
                ],
                [
                    'attribute' => 'preview',
                    'value' => function ($model) {
                        return Html::a('Preview', ['preview', 'id' => $model->id], ['target' => '_blank']);
                    },
                    'format' => 'raw',
                ],
            ],
        ]); ?>
    </div>
</div>