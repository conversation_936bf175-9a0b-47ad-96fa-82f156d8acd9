<?php

use common\helpers\DataHelper;
use common\models\SaCollegeFeesDetail;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model common\models\SaCollegeFeesDetail */

$this->title = !empty($model->saCollege) ? $model->saCollege->name : '';
$this->params['breadcrumbs'][] = ['label' => 'Sa College Fees Details', 'url' => ['index']];
$this->params['breadcrumbs'][] = ['label' => 'Sa College' , 'url' => '/sa-college/index?SaCollegeSearch[name]=' . $model->saCollege->name];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="sa-college-fees-detail-view box box-primary">
    <div class="box-header">
        <?= Html::a('Update', ['update', 'id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?= DetailView::widget([
            'model' => $model,
            'attributes' => [
                [
                    'attribute' => 'sa_college_id',
                    'value' => function ($model) {
                        return !empty($model->saCollege) ? $model->saCollege->name : '';
                    }
                ],
                'type',
                'fees',
                [
                    'label' => 'Status',
                    'attribute' => 'status',
                    'value' => ArrayHelper::getValue(DataHelper::getConstantList('STATUS', SaCollegeFeesDetail::class), $model->status)
                ],
                'created_at:datetime',
                'updated_at:datetime',
            ],
        ]) ?>
    </div>
</div>
