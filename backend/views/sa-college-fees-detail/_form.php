<?php

use common\helpers\DataHelper;
use common\models\SaCollege;
use common\models\SaCollegeFeesDetail;
use kartik\select2\Select2;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\web\JsExpression;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model common\models\SaCollegeFeesDetail */
/* @var $form yii\widgets\ActiveForm */

$collegeList = ArrayHelper::map(SaCollege::find()->andWhere(['id' => $model->sa_college_id])->all(), 'id', 'name');
$disabled = false;

if (isset($_GET['college_id'])) {
    $selectedCollege = SaCollege::findOne($_GET['college_id']); // Fetch the country by ID
    if ($selectedCollege) {
        $collegeList[$selectedCollege->id] = $selectedCollege->name;
        $model->sa_college_id = $selectedCollege->id; // Set the model's value
    }
}

if (isset($_GET['college_id']) || !$model->isNewRecord) {
    $disabled = true;
}
?>

<div class="sa-college-fees-detail-form box box-primary">
    <?php $form = ActiveForm::begin(); ?>
    <div class="box-body table-responsive">

        <div class="col-md-12">
            <?= $form->field($model, 'sa_college_id')->widget(Select2::classname(), [
                'disabled' => $disabled,
                'data' => $collegeList, // array of text to show in the tag for the selected items
                'options' => [
                    'placeholder' => '--Select--',
                    'multiple' => false,
                ],
                'pluginOptions' => [
                    'allowClear' => true,
                    'minimumInputLength' => 3,
                    'language' => [
                        'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                    ],
                    'ajax' => [
                        'url' => ['../ajax/sa-college'],
                        'dataType' => 'json',
                        'data' => new JsExpression('function(params) {return {q:params.term}; }')
                    ],
                    'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                    'templateResult' => new JsExpression('function(data) { return data.text; }'),
                    'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                ],
            ])->label('SA College Name');
?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'type')->dropDownList(
                DataHelper::$saFeesType,
                [
                    'prompt' => '-- Select Type --',
                    'disabled' => !$model->isNewRecord
                ]
            )->label('Fees Type') ?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'fees')->textInput() ?>
        </div>

        <div class="col-md-12">
            <?= $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', SaCollegeFeesDetail::class)) ?>
        </div>

    </div>
    <div class="box-footer">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <?php ActiveForm::end(); ?>
</div>