<?php

use common\helpers\CollegeHelper;
use yii\helpers\Html;
use common\helpers\DataHelper;
use common\models\SaCollegeFeesDetail;
use yii\bootstrap\ActiveForm;
use unclead\multipleinput\TabularInput;
use unclead\multipleinput\TabularColumn;

/* @var $this \yii\web\View */
/* @var $models Item[] */

?>

<div class="exam-date-form box box-primary">
    <?php $form = \yii\bootstrap\ActiveForm::begin([
        'id' => 'tabular-form',
        'options' => [
            'enctype' => 'multipart/form-data'
        ]
    ]) ?>
    <div class="box-body">

        <?= TabularInput::widget([
            'models' => $models,
            'modelClass' => SaCollegeFeesDetail::class,
            'allowEmptyList' => 'true',
            'min' => 0,
            'max' => 6,
            'addButtonPosition' => [
                TabularInput::POS_HEADER,
                TabularInput::POS_ROW
            ],
            'layoutConfig' => [
                'offsetClass'   => 'col-sm-offset-4',
                'labelClass'    => 'col-sm-2',
                'wrapperClass'  => 'col-sm-10',
                'errorClass'    => 'col-sm-4'
            ],
            'attributeOptions' => [
                'enableAjaxValidation'   => false,
                'enableClientValidation' => true,
                'validateOnChange'       => true,
                'validateOnSubmit'       => true,
                'validateOnBlur'         => false,
            ],
            'form' => $form,
            'columns' => [
                [
                    'name' => 'id',
                    'type' => TabularColumn::TYPE_HIDDEN_INPUT
                ],
                [
                    'name' => 'sa_college_id',
                    'defaultValue' => Yii::$app->request->get('college_id'),
                    'type' => TabularColumn::TYPE_HIDDEN_INPUT
                ],
                [
                    'name' => 'type',
                    'title' => 'Type',
                    'type' => TabularColumn::TYPE_DROPDOWN,
                    'items' => DataHelper::$saFeesType,
                ],
                [
                    'name' => 'fees',
                    'title' => 'Fees',
                    'type' => TabularColumn::TYPE_TEXT_INPUT,
                ],
                [
                    'name'  => 'status',
                    'type' => TabularColumn::TYPE_DROPDOWN,
                    'items' => DataHelper::getConstantList('STATUS', SaCollegeFeesDetail::class),
                ],

            ],
        ]) ?>

    </div>

    <div class="box-footer">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <?php ActiveForm::end(); ?>
</div>