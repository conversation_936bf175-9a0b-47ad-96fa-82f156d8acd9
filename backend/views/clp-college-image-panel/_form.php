<?php

use common\helpers\DataHelper;
use common\models\ClpCollegeImagePanel;
use common\models\College;
use kartik\select2\Select2;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\web\JsExpression;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model common\models\ClpCollegeImagePanel */
/* @var $form yii\widgets\ActiveForm */

$data =  ArrayHelper::map(College::find()->andWhere(['id' => $model->college_id])->all(), 'id', 'name');

?>

<style>
    .banner-upload-section {
        margin-top: 10px;
    }

    .hint-block {
        color: #dd4b39;
    }
</style>

<div class="clp-college-image-panel-form box box-primary">
    <?php $form = ActiveForm::begin(); ?>
    <div class="box-body table-responsive">

        <div class="col-md-6">
            <?= $form->field($model, 'college_id')->widget(Select2::classname(), [
                'disabled' => !$model->isNewRecord || $model->college_id,
                'data' => $data, // array of text to show in the tag for the selected items
                'options' => [
                    'placeholder' => '--Select--',
                    'multiple' => false,
                ],
                'pluginOptions' => [
                    'allowClear' => true,
                    'minimumInputLength' => 3,
                    'language' => [
                        'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                    ],
                    'ajax' => [
                        'url' => ['../ajax/college-list'],
                        'dataType' => 'json',
                        'data' => new JsExpression('function(params) {return {q:params.term}; }')
                    ],
                    'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                    'templateResult' => new JsExpression('function(data) { return data.text; }'),
                    'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                ],
            ])->label('College Name'); ?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', ClpCollegeImagePanel::class)); ?>
        </div>

        <div class="col-md-6">
            <?php
            if (!empty($model->logo_image)) {
                echo Html::img(\Yii::$aliases['@gmuAzureClpCollegeLogo'] . '/' . $model->logo_image, ['width' => '50', 'height' => '50']);
            } ?>
            <?= $form->field($model, 'logo_image')->fileInput()->hint('Recommended Dimension: 276 × 207 px. Max size: 50 KB') ?>
        </div>

        <div class="col-md-6">
            <label class="control-label">Banner Image Upload</label><br>

            <!-- Toggle buttons -->
            <div class="btn-group mb-3" role="group" aria-label="Toggle Web/Wap">
                <button type="button" class="btn btn-sm btn-primary toggle-btn active" data-target="#webBanner">Web</button>
                <button type="button" class="btn btn-sm btn-secondary toggle-btn" data-target="#wapBanner">Wap</button>
            </div>

            <!-- Web Banner Upload -->
            <div id="webBanner" class="banner-upload-section">
                <?php
                $webImage = $model->banner_image[0]['web'] ?? '';
                if (!empty($webImage)) {
                    echo Html::img(Yii::getAlias('@gmuAzureClpCollegeBanner') . '/' . $webImage, ['width' => 100, 'class' => 'img-thumbnail mb-2']);
                }
                ?>
                <?= $form->field($model, 'banner_image_web')->fileInput()->hint('Recommended Dimension: 1600 × 600 px. Max size: 100 KB') ?>
            </div>

            <!-- Wap Banner Upload -->
            <div id="wapBanner" class="banner-upload-section" style="display:none;">
                <?php
                $wapImage = $model->banner_image[0]['wap'] ?? '';
                if (!empty($wapImage)) {
                    echo Html::img(Yii::getAlias('@gmuAzureClpCollegeBanner') . '/' . $wapImage, ['width' => 100, 'class' => 'img-thumbnail mb-2']);
                }
                ?>
                <?= $form->field($model, 'banner_image_wap')->fileInput()->hint('Recommended Dimension: 600 × 830 px. Max size: 100 KB') ?>
            </div>
        </div>
    </div>

    <div class="box-footer">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <?php ActiveForm::end(); ?>
</div>

<?php
$script = <<<JS
$('.toggle-btn').on('click', function () {
    var target = $(this).data('target');

    // Change button styles
    $('.toggle-btn').removeClass('btn-primary').addClass('btn-secondary').removeClass('active');
    $(this).addClass('btn-primary active');

    // Hide all sections, show target
    $('.banner-upload-section').hide();
    $(target).show();
});
JS;
$this->registerJs($script);
?>