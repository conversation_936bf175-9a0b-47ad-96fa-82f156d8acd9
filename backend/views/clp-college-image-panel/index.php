<?php

use common\helpers\DataHelper;
use common\models\ClpCollegeImagePanel;
use frontend\helpers\Url;
use yii\helpers\Html;
use yii\grid\GridView;
use yii\helpers\ArrayHelper;

/* @var $this yii\web\View */
/* @var $searchModel backend\models\ClpCollegeImagePanelSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Clp College Image Panels';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="clp-college-image-panel-index box box-primary">
    <div class="box-body table-responsive no-padding">
        <?php // echo $this->render('_search', ['model' => $searchModel]);
        ?>
        <?= GridView::widget([
            'dataProvider' => $dataProvider,
            'filterModel' => $searchModel,
            'layout' => "{items}\n{summary}\n{pager}",
            'columns' => [
                ['class' => 'yii\grid\SerialColumn'],

                [
                    'attribute' => 'college_name',
                    'value' => function ($model) {
                        return $model->college->name;
                    }
                ],
                'logo_image',
                'banner_image',
                 [
                    'attribute' => 'status',
                    'value' => function ($model) {
                        return ArrayHelper::getValue(DataHelper::getConstantList('STATUS', ClpCollegeImagePanel::class), $model->status);
                    },
                    'filter' => DataHelper::getConstantList('STATUS', ClpCollegeImagePanel::class)
                ],

                [
                    'class' => 'yii\grid\ActionColumn',
                    'template' => '{view} {update}',
                    'urlCreator' => function ($action, $model, $key, $index) {
                        if ($action === 'update') {
                            return Url::to(['clp-college-image-panel/create', 'college_id' => $model->college_id]);
                        }
                        return Url::to([$action, 'id' => $model->id]);
                    }
                ],
            ],
        ]); ?>
    </div>
</div>