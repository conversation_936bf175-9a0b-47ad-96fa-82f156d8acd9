<?php

use common\helpers\DataHelper;
use common\models\ClpCollegeImagePanel;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model common\models\ClpCollegeImagePanel */

$this->title = $model->college->name;
$this->params['breadcrumbs'][] = ['label' => 'Custom Landing Page', 'url' => ['custom-landing-page/view', 'id' => $clp_id]];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="clp-college-image-panel-view box box-primary">
    <div class="box-header">
        <?= Html::a('Update', ['create', 'college_id' => $model->college_id, 'clp_id' => $clp_id], ['class' => 'btn btn-primary btn-flat']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?= DetailView::widget([
            'model' => $model,
            'attributes' => [
                [
                    'attribute' => 'logo_image',
                    'format' => 'html',
                    'value' => function ($model) {
                        if (!empty($model->logo_image)) {
                            return Html::img(\Yii::$aliases['@gmuAzureClpCollegeLogo'] . '/' . $model->logo_image, ['width' => '70', 'height' => '70']);
                        } else {
                            return '<span class="not-set">Logo Image is missing.</span>';
                        }
                    }
                ],
                [
                    'attribute' => 'banner_image',
                    'format' => 'html',
                    'value' => function ($model) {
                        if (!empty($model->banner_image)) {
                            return Html::img(\Yii::$aliases['@gmuAzureClpCollegeBanner'] . '/' . $model->banner_image, ['width' => '70', 'height' => '70']);
                        } else {
                            return '<span class="not-set">Banner Image is missing.</span>';
                        }
                    }
                ],
                [
                    'attribute' => 'status',
                    'label' => 'Status',
                    'value' => ArrayHelper::getValue(DataHelper::getConstantList('STATUS', ClpCollegeImagePanel::class), $model->status)
                ],
                'created_at:datetime',
                'updated_at:datetime',
            ],
        ]) ?>
    </div>
</div>
