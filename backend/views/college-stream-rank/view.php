<?php

use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model common\models\CollegeStreamRank */

$this->title = $college->name;
$this->params['breadcrumbs'][] = ['label' => 'College Index', 'url' => ['college/index']];
$this->params['breadcrumbs'][] = $college->name;
\yii\web\YiiAsset::register($this);
?>
<div class="college-stream-rank-view  box box-primary">
    <div class="box-header">
    <p>
        <?= Html::a('Back', ['college/view', 'id' => $college->id], ['class' => 'btn btn-primary']) ?>
    </p>
    </div>
    <div class="box-body table-responsive no-padding">


            <div class="card">
              <div class="card-header">
                <h3 class="card-title"></h3>
              </div>
              <!-- /.card-header -->
              <div class="card-body table-responsive p-0">
                <table class="table table-hover text-nowrap">
                  <thead>
                    <tr>
                      <th>Stream</th>
                      <th>Rank</th>
                     
                    </tr>
                  </thead>
                  <tbody>
                    <?php foreach ($collegeRankDetail as $rank) { ?>
                    <tr>
                      <td><?=  ucfirst($rank->stream) ?></td>
                      <td><?= round($rank->rank, 2);?></td>
                    </tr>
                    <?php } ?>
                  </tbody>
                </table>
              </div>
              <!-- /.card-body -->
            </div>
            <!-- /.card -->
          
    </div>
</div>
