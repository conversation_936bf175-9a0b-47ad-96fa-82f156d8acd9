<?php

use common\helpers\DataHelper;
use common\models\Brochure;
use common\models\College;
use common\models\CourseExamCollege;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\widgets\DetailView;
use common\helpers\CollegeHelper;
use common\models\CutoffDetailH1Description;

/* @var $this yii\web\View */
/* @var $model common\models\College */

$this->title = $model->name;
$this->params['breadcrumbs'][] = ['label' => 'Colleges', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
$brochure = Brochure::find()->where(['college_id' => $model->id])->andWhere(['entity' => 'college'])->one();
?>
<div class="college-view box box-primary">
    <div class="box-header">
        <?= Html::a('Update', ['update', 'id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
        <?= Html::a('Add Features', ['feature-value/create', 'college-id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
        <?= Html::a('Add Exams', ['exam', 'id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
        <?= Html::a('Courses', ['college-program/home', 'id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
        <?= Html::a('Gallery', ['gallery/index', 'entityId' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
        <?= Html::a('Cut Off', ['cut-off/home', 'id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
        <?php if (!empty($brochure)): ?>
            <?= Html::a('Update Brochure', ['brochure/update', 'id' => $brochure->id], ['class' => 'btn btn-primary btn-flat']) ?>
        <?php else: ?>
            <?= Html::a('Create Brochure', ['brochure/create', 'college_id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
        <?php endif; ?>
        <?= Html::a('Add CI Exams', ['college-program/exam', 'college_id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
        <?php if (!empty($model->is_sponsored == College::STATUS_ACTIVE)): ?>
            <?= Html::a('Sponsored Tagging', ['sponsor-college/sponsor', 'college-id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
        <?php endif; ?>
        <?= Html::a('Courses Dates', ['course-program-dates/home', 'entity_type' => 1, 'college_id' => $model->id], ['class' => 'btn btn-success btn-flat']) ?>
        <?= Html::a('Program Dates', ['course-program-dates/home', 'entity_type' => 2, 'college_id' => $model->id], ['class' => 'btn btn-success btn-flat']) ?>
        <?php if (!empty(($model->collgeStreamRank))) {  ?>
            <?= Html::a('College Stream Rank', ['college-stream-rank/view',  'id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
        <?php } ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?= DetailView::widget([
            'model' => $model,
            'attributes' => [
                // 'id',
                'name',
                'slug',
                'display_name',
                [
                    'attribute' => 'city_id',
                    'label' => 'Location',
                    'value' => function ($q) {
                        if (!empty($q->city)) {
                            return $q->city->name ?? '' . ', ' . !empty($q->city->state) ? $q->city->state->name : '-';
                        } else {
                            return '-';
                        }
                    }
                ],
                [
                    'attribute' => 'college_type',
                    'label' => 'College Type',
                    'value' => !empty(CollegeHelper::$collegeType[$model->college_type]) ? CollegeHelper::$collegeType[$model->college_type] : '',
                ],
                [
                    'attribute' => 'location_type',
                    'label' => 'Location Type',
                    'value' => !empty(CollegeHelper::$LocationType[$model->location_type]) ? CollegeHelper::$LocationType[$model->location_type] : '',
                ],
                [
                    'attribute' => 'is_evening',
                    'label' => 'Is Evening',
                    'value' => ArrayHelper::getValue(DataHelper::getConstantList('IS_EVENING', College::class), $model->is_evening)
                ],
                [
                    'attribute' => 'is_girl_exclusive',
                    'label' => 'Is Girl Exclusive',
                    'value' => ArrayHelper::getValue(DataHelper::getConstantList('IS_GIRL_EXCLUSIVE', College::class), $model->is_girl_exclusive)
                ],
                [
                    'label' => 'Cover Image',
                    'attribute' => 'cover_image',
                    'format' => 'html',
                    'value' => function ($model) {
                        if (!empty($model->cover_image)) {
                            return Html::img(\Yii::$aliases['@gmuAzureCollegeImage'] . '/' . $model->cover_image, ['width' => '70', 'height' => '70']);
                        } else {
                            return '<span class="not-set">Cover Image is missing.</span>';
                        }
                    }
                ],
                // 'cover_image',
                // 'logo_image',
                [
                    'attribute' => 'logo_image',
                    'format' => 'html',
                    'value' => function ($model) {
                        if (!empty($model->logo_image)) {
                            return Html::img(\Yii::$aliases['@gmuAzureCollegeImageLogo'] . '/' . $model->logo_image, ['width' => '70', 'height' => '70']);
                        } else {
                            return '<span class="not-set">Logo Image is missing.</span>';
                        }
                    }
                ],
                'address:ntext',
                'phone',
                'email',
                'latitude',
                'longitude',
                'image',
                'is_sponsored',
                'parent_id',
                'url',
                [
                    'attribute' => 'type',
                    'label' => 'Type',
                    'value' => ArrayHelper::getValue(DataHelper::getConstantList('TYPE', College::class), $model->type)
                ],
                [
                    'attribute' => 'status',
                    'label' => 'Status',
                    'value' => ArrayHelper::getValue(DataHelper::getConstantList('STATUS', College::class), $model->status)
                ],
                'created_at:datetime',
                'updated_at:datetime',
            ],
        ]) ?>
    </div>
</div>