<?php

use common\helpers\DataHelper;
use common\models\College;
use yii\helpers\Html;
use yii\grid\GridView;
use yii\helpers\ArrayHelper;
use yii\widgets\Pjax;

/* @var $this yii\web\View */
/* @var $searchModel backend\models\CollegeSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Colleges';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="college-index box box-primary">
    <?php Pjax::begin(); ?>
    <div class="box-header with-border">
        <?= Html::a('Create College', ['create'], ['class' => 'btn btn-success btn-flat', 'style' => 'float: right; margin-right: 10px;']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?php // echo $this->render('_search', ['model' => $searchModel]);
        ?>
        <?= GridView::widget([
            'dataProvider' => $dataProvider,
            'filterModel' => $searchModel,
            'layout' => "{items}\n{summary}\n{pager}",
            'columns' => [
                ['class' => 'yii\grid\SerialColumn'],

                'id',
                'name',
                'slug',
                [
                    'attribute' => 'city_id',
                    'label' => 'City',
                    'value' => 'city.name'
                ],
                // 'address:ntext',
                // 'latitude',
                // 'longitude',
                // 'image',
                // 'parent_id',
                // 'type',
                [
                    'attribute' => 'is_popular',
                    'value' => function ($model) {
                        return ArrayHelper::getValue(DataHelper::getConstantList('POPULAR', College::class), $model->is_popular);
                    },
                    'filter' => DataHelper::getConstantList('POPULAR', College::class)
                ],
                'position',
                [
                    'attribute' => 'status',
                    'value' => function ($model) {
                        return ArrayHelper::getValue(DataHelper::getConstantList('STATUS', College::class), $model->status);
                    },
                    'filter' => DataHelper::getConstantList('STATUS', College::class)
                ],
                // 'created_at',
                // 'updated_at',

                ['class' => 'yii\grid\ActionColumn', 'template' => '{view}'],
            ],
        ]); ?>
    </div>
    <?php Pjax::end(); ?>
</div>