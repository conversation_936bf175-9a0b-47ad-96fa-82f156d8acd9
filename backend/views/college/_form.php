<?php

use common\helpers\DataHelper;
use common\models\Article;
use common\models\City;
use common\models\College;
use common\models\Country;
use common\models\News;
use kartik\select2\Select2;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\web\JsExpression;
use yii\widgets\ActiveForm;
use common\models\EducationBodyType;
use common\helpers\CollegeHelper;
use common\services\UserService;

$data =  ArrayHelper::map(College::find()->andWhere(['id' => $model->parent_id])->all(), 'id', 'name');
foreach ($model->article as $value) {
    $articleData[] = ArrayHelper::map(Article::find()->where(['id' => $value->id])->all(), 'id', 'title');
}
foreach ($model->news as $value) {
    $newsData[] = ArrayHelper::map(News::find()->where(['id' => $value->id])->all(), 'id', 'name');
}

/* @var $this yii\web\View */
/* @var $model common\models\College */
/* @var $form yii\widgets\ActiveForm */
?>

<div class="college-form box box-primary">
    <?php $form = ActiveForm::begin(); ?>
    <div class="box-body table-responsive">

        <div class="col-md-6">
            <?= $form->field($model, 'country_code')->widget(Select2::classname(), [
                'data' => ArrayHelper::map(Country::find()->all(), 'iso_code', 'name'),
                'language' => 'en',
                'options' => ['placeholder' => '--Select Country--'],
                'pluginOptions' => [
                    'allowClear' => true
                ],
            ])->label('Country'); ?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'city_id')->widget(Select2::classname(), [
                'data' => ArrayHelper::map(City::find()->all(), 'id', 'name'),
                'language' => 'en',
                'options' => ['placeholder' => '--Select City--'],
                'pluginOptions' => [
                    'allowClear' => true
                ],
            ])->label('City'); ?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'name')->textInput(['maxlength' => true]) ?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'display_name')->textInput(['maxlength' => true]) ?>
        </div>

        <div class="col-md-6">
            <?php if ((!$model->isNewRecord) && (UserService::hasCollegeSlugUpdateRole())) { ?>
                <?= $form->field($model, 'slug')->textInput(['maxlength' => true]) ?>
            <?php } else { ?>
                <?= $form->field($model, 'slug')->textInput(['maxlength' => true, 'disabled' => true]) ?>
            <?php } ?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'type')->dropDownList(DataHelper::getConstantList('TYPE', College::class), [
                'onchange' => 'var type = $("#college-type option:selected").text()
                        if ( type == "University") {

                            $("#college-parent_id").attr("disabled", true);
                        }                
                '
            ])->label('Education Body') ?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'parent_id')->widget(Select2::classname(), [
                'data' => $data, //ArrayHelper::map(College::find()->all(), 'id', 'name'),
                'language' => 'en',
                'options' => ['placeholder' => '--Select University/College--'],
                'pluginOptions' => [
                    'allowClear' => true,
                    'minimumInputLength' => 3,
                    'language' => [
                        'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                    ],
                    'ajax' => [
                        'url' => ['../ajax/college-list'],
                        'dataType' => 'json',
                        'data' => new JsExpression('function(params) {return {q:params.term}; }')
                    ],
                    'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                    'templateResult' => new JsExpression('function(data) { return data.text; }'),
                    'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                ],
            ])->label('College Affiliation'); ?>
        </div>
        <div class="col-md-6">
            <?= $form->field($model, 'education_body_type_id')->widget(Select2::classname(), [
                'data' => ArrayHelper::map(EducationBodyType::find()->all(), 'id', 'name'),
                'options' => [
                    'placeholder' => '--Select--'
                ]
            ])->label('Education Body Type');
?>
        </div>
        <div class="col-md-6">

            <?= $form->field($model, 'college_type')->widget(Select2::classname(), [
                'data' =>  CollegeHelper::$collegeType, //array of text to show in the field for the selected items
                'options' => [
                    'placeholder' => '--Select--'
                ]
            ])->label('College Type');
?>
        </div>
        <div class="col-md-4">

            <?= $form->field($model, 'location_type')->widget(Select2::classname(), [
                'data' =>  CollegeHelper::$LocationType, //array of text to show in the field for the selected items
                'options' => [
                    'placeholder' => '--Select--'
                ]
            ])->label('Location Type');
?>
        </div>
        <div class="col-md-4">
            <?= $form->field($model, 'is_evening')->dropDownList(array_reverse(DataHelper::getConstantList('IS_EVENING', College::class))) ?>
        </div>
        <div class="col-md-4">
            <?= $form->field($model, 'is_girl_exclusive')->dropDownList(array_reverse(DataHelper::getConstantList('IS_GIRL_EXCLUSIVE', College::class))) ?>
        </div>
        <div class="col-md-6">

            <?php
            if (!empty($model->cover_image)) {
                echo Html::img(\Yii::$aliases['@gmuAzureCollegeImage'] . '/' . $model->cover_image, ['width' => '50', 'height' => '50']);
            }
            ?>
            <?= $form->field($model, 'cover_image')->fileInput() ?>
        </div>
        <div class="col-md-6">

            <?php
            if (!empty($model->logo_image)) {
                echo Html::img(\Yii::$aliases['@gmuAzureCollegeImageLogo'] . '/' . $model->logo_image, ['width' => '50', 'height' => '50']);
            }
            ?>
            <?= $form->field($model, 'logo_image')->fileInput() ?>
        </div>

        <div class="col-md-4">
            <?= $form->field($model, 'latitude')->textInput(['maxlength' => true]) ?>
        </div>

        <div class="col-md-4">
            <?= $form->field($model, 'longitude')->textInput(['maxlength' => true]) ?>
        </div>

        <div class="col-md-4">
            <?= $form->field($model, 'url')->textInput(['maxlength' => true])->label('College Web Address') ?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'phone')->textInput(['maxlength' => true]) ?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'email')->textInput(['maxlength' => true]) ?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'pincode')->textInput(['type' => 'number']) ?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'position')->textInput(['type' => 'number']) ?>
        </div>

        <div class="col-md-12">
            <?= $form->field($model, 'address')->textarea(['rows' => 6]) ?>
        </div>

        <div class="col-md-4">
            <?= $form->field($model, 'is_featured')->dropDownList(DataHelper::getConstantList('IS_FEATURED', College::class)) ?>
        </div>

        <div class="col-md-4">
            <?= $form->field($model, 'is_popular')->dropDownList(DataHelper::getConstantList('POPULAR', College::class)) ?>
        </div>

        <div class="col-md-4">
            <?= $form->field($model, 'is_sponsored')->dropDownList(DataHelper::getConstantList('SPONSORED', College::class)) ?>
        </div>

        <div class="col-md-4">
            <?= $form->field($model, 'is_google_ads')->dropDownList(DataHelper::getConstantList('ADS', College::class)) ?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'rank')->textInput(['maxlength' => true, 'disabled' => true]) ?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'status')->dropDownList(array_reverse(DataHelper::getConstantList('STATUS', College::class))) ?>
        </div>
        <?php
        if (Yii::$app->user->can('Seo-Tag-Permission')):
            if (!$model->isNewRecord) {
                $selectedValue = [];
                if (!empty($newsData)) {
                    foreach ($newsData as $value) {
                        if (!empty($value)) {
                            $selectedValue[array_keys($value)[0]] = ['selected' => true];
                        }
                    }
                }
            }
            ?>
            <div class="col-md-12">

                <?= $form->field($model, 'news[]')->widget(Select2::classname(), [
                    'data' => $newsData ?? [], //array of text to show in the field for the selected items
                    'options' => [
                        'placeholder' => '--Select--',
                        'multiple' => true,
                        'options' => $selectedValue ?? [],
                    ],
                    'pluginOptions' => [
                        'allowClear' => true,
                        'minimumInputLength' => 3,
                        'language' => [
                            'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                        ],
                        'ajax' => [
                            'url' => ['../ajax/news-list'],
                            'dataType' => 'json',
                            'data' => new JsExpression('function(params) {return {q:params.term}; }')
                        ],
                        'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                        'templateResult' => new JsExpression('function(data) { return data.text; }'),
                        'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                    ],
                ])->label('News Name');
                ?>
            </div>
            <?php
            if (!$model->isNewRecord) {
                $selectedValueArticle = [];
                if (!empty($articleData)) {
                    foreach ($articleData as $value) {
                        if (!empty($value)) {
                            $selectedValueArticle[array_keys($value)[0]] = ['selected' => true];
                        }
                    }
                }
            }
            ?>
            <div class="col-md-12">

                <?= $form->field($model, 'article[]')->widget(Select2::classname(), [
                    'data' => $articleData ?? [], //array of text to show in the field for the selected items
                    'options' => [
                        'placeholder' => '--Select--',
                        'multiple' => true,
                        'options' => $selectedValueArticle ?? [],
                    ],
                    'pluginOptions' => [
                        'allowClear' => true,
                        'minimumInputLength' => 3,
                        'language' => [
                            'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                        ],
                        'ajax' => [
                            'url' => ['../ajax/article-list'],
                            'dataType' => 'json',
                            'data' => new JsExpression('function(params) {return {q:params.term}; }')
                        ],
                        'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                        'templateResult' => new JsExpression('function(data) { return data.text; }'),
                        'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                    ],
                ])->label('Article Name');
                ?>
            </div>
        <?php endif; ?>
    </div>
    <div class="box-footer">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success btn-flat', 'style' => 'float: right; margin-right: 10px; width: 8%;']) ?>
    </div>
    <?php ActiveForm::end(); ?>
</div>