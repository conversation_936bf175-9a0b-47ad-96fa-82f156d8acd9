<?php

use common\helpers\DataHelper;
use common\models\Article;
use common\models\Board;
use common\models\College;
use common\models\Course;
use common\models\Degree;
use common\models\Exam;
use common\models\News;
use common\models\Stream;
use common\models\NewsCategory;
use common\models\Tags;
use common\models\LeadBucket;
use common\models\LeadBucketTagging;
use common\models\NewsSubdomain;
use common\models\NewsContentSubdomain;
use kartik\date\DatePicker;
use kartik\datetime\DateTimePicker;
use kartik\select2\Select2;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\web\JsExpression;
use yii\widgets\ActiveForm;
use common\models\User;
use common\services\UserService;

/* @var $this yii\web\View */
/* @var $model common\models\News */
/* @var $form yii\widgets\ActiveForm */

$model->banner_caption = html_entity_decode(stripslashes($model->banner_caption));
$model->name = html_entity_decode(stripslashes($model->name));
foreach ($model->college as $value) {
    $collegeData[] = ArrayHelper::map(College::find()->where(['id' => $value->id])->all(), 'id', 'name');
}

foreach ($model->exam as $value) {
    $examData[] = ArrayHelper::map(Exam::find()->where(['id' => $value->id])->all(), 'id', 'name');
}

foreach ($model->board as $value) {
    $boardData[] = ArrayHelper::map(Board::find()->where(['id' => $value->id])->all(), 'id', 'name');
}

foreach ($model->article as $value) {
    $articleData[] = ArrayHelper::map(Article::find()->where(['id' => $value->id])->all(), 'id', 'title');
}

foreach ($model->news as $value) {
    $newsData[] = ArrayHelper::map(News::find()->where(['id' => $value->id])->all(), 'id', 'name');
}

foreach ($model->translation as $value) {
    $translationData[] = ArrayHelper::map(News::find()->where(['id' => $value->id])->all(), 'id', 'name');
}
foreach ($model->course as $value) {
    $courseData[] = ArrayHelper::map(Course::find()->where(['id' => $value->id])->all(), 'id', 'name');
}

foreach ($model->tags as $value) {
    $tagData[] = ArrayHelper::map(Tags::find()->where(['id' => $value->id])->all(), 'id', 'name');
}

if (isset($model->stream_id) && !empty($model->stream->id)) {
    $streamData[] = ArrayHelper::map(Stream::find()->where(['id' => $model->stream->id])->all(), 'id', 'name');
}

if (!empty($model->id)) {
    $bucketname = ArrayHelper::map(LeadBucketTagging::find()->select(['id', 'bucket_id'])->where(['news_id' => $model->id, 'status' => LeadBucketTagging::STATUS_ACTIVE])->all(), 'id', 'bucket_id');
}

if (!empty($modelContent->author_id)) {
    $user =  ArrayHelper::map(User::find()->where(['id' => $modelContent->author_id])->all(), 'id', 'name');
}

?>

<div class="news-form box box-primary">
            <?=
            $this->render('/partials/_user-list');
            ?>
    <?php $form = ActiveForm::begin(); ?>
    <div class="box-body">
        <div class="row">
            <div class="col-md-4">
                <?= $form->field($model, 'name')->textInput(['maxlength' => true]) ?>
            </div>

            <?php
            if ((!$model->isNewRecord) && isset($model->status) && ($model->status == News::STATUS_INACTIVE || $model->status == News::STATUS_DRAFT) &&  empty($model->published_at)) { ?>
                <div class="col-md-4">
                    <?= $form->field($model, 'slug')->textInput(['maxlength' => true]) ?>
                </div>
            <?php } elseif (isset($model->status) && $model->status == News::STATUS_ACTIVE) { ?>
                <div class="col-md-4">
                    <?= $form->field($model, 'slug')->textInput(['maxlength' => true, 'disabled' => true]) ?>
                </div>
            <?php  } ?>

            <div class="col-md-4">
                <?= $form->field($model, 'display_name')->textInput(['maxlength' => true]) ?>
            </div>
            
            <div class="col-md-4">
                <?= $form->field($model, 'news_category_id')->widget(Select2::class, [
                    'data' => ArrayHelper::map(NewsCategory::find()->active()->all(), 'id', 'name'),
                    'language' => 'en',
                    'options' => ['placeholder' => '--Select News--'],
                    'pluginOptions' => [
                        'allowClear' => true
                    ],
                ])->label('Category');
?>
            </div>
            
            <div class="col-md-4">
                <?= $form->field($model, 'expired_at')->widget(
                    DateTimePicker::class,
                    [
                        'options' => ['placeholder' => 'Select Expiry Date'],
                        'pluginOptions' => [
                            'timePicker' => true,
                            'locale' => [
                                'format' => 'yyyy-mm-dd HH:ii',
                            ],
                            'autoclose' => true,
                            'todayHighlight' => true,
                        ],
                    ]
                );
?>
            </div>

            <div class="col-md-4">
                <?= $form->field($model, 'scheduled_at')->widget(
                    DateTimePicker::class,
                    [
                        'options' => ['placeholder' => 'Select Date'],
                        'pluginOptions' => [
                            'timePicker' => true,
                            'locale' => [
                                'format' => 'yyyy-mm-dd HH:ii',
                            ],
                            'autoclose' => true,
                            'todayHighlight' => true,
                        ],
                    ]
                );
?>
            </div>

            <div class="col-md-4">
                <?= $form->field($model, 'published_at')->widget(
                    DateTimePicker::class,
                    [
                        'options' => ['placeholder' => 'Select Date', 'disabled' => true],
                        'pluginOptions' => [
                            'timePicker' => true,
                            'locale' => [
                                'format' => 'yyyy-mm-dd HH:ii',
                            ],
                            'autoclose' => true,
                            'todayHighlight' => true,
                        ],
                    ]
                );
?>
            </div>
        </div>
        <div class="row">
            <div class="col-md-4">
                <?= $form->field($model, 'is_popular')->dropDownList(DataHelper::getConstantList('POPULAR', News::class)) ?>
            </div>

            <div class="col-md-4">
                <?= $form->field($model, 'is_live')->dropDownList(DataHelper::getConstantList('IS_LIVE', News::class)) ?>
            </div>

            <div class="col-md-4">
                <?= $form->field($model, 'position')->textInput(['type' => 'number']) ?>
            </div>
        </div>
        <div class="row">
        <div class="col-md-4">
                <?php echo $form->field($model, 'is_freelancer')->dropDownList([NewsSubdomain::IS_FREELANCER_NO => 'No', NewsSubdomain::IS_FREELANCER_YES => 'Yes'], ['options' => [$model->is_freelancer => ['selected' => true]],'disabled'=> $model->is_freelancer ? true: false]); ?>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
                <?php
                if (!empty($model->banner_image)) { ?>
                    <?php echo Html::img(\Yii::$aliases['@newsImageFrontend'] . '/' . $model->banner_image, ['width' => '100', 'height' => '100', 'style' => 'margin:10px']); ?>
                <?php } ?>
                <?= $form->field($model, 'banner_image')->fileInput()->hint('Image size should not be greater than 100kb, Extension: webp, Dimension: maxWidth-1200, maxHeight-667'); ?>
            </div>
            <div class="col-md-6">
                <?php
                if (!empty($model->audio)) {
                    ?>
                    <audio controls id="myAudio" class="center" preload="none">
                        <source src="<?php echo $model->audio ?>" type="audio/mpeg">
                        Your browser does not support the audio element.
                    </audio>
                    <?php
                }
                ?>
                <?= $form->field($model, 'audio')->fileInput() ?>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
                <?= $form->field($model, 'banner_caption')->textInput(['maxlength' => true]) ?>
            </div>
            <div class="col-md-6">
                <?= $form->field($model, 'lang_code')->dropDownList(
                    array_flip(DataHelper::$languageCode)
                )->label('Language code') ?>
            </div>
            <?php
            if (!$model->isNewRecord) {
                $selectedValueStream = [];
                if (!empty($streamData)) {
                    foreach ($streamData as $value) {
                        if (!empty($value)) {
                            $selectedValueStream[array_keys($value)[0]] = ['selected' => true];
                        }
                    }
                }
            }
            ?>
            <div class="col-md-6">
                <?= $form->field($model, 'stream_id')->widget(Select2::classname(), [
                    'data' => $streamData ?? [], //array of text to show in the field for the selected items
                    'options' => [
                        'placeholder' => '--Select--',
                        'multiple' => false,
                        'options' => $selectedValueStream ?? [],
                    ],
                    'pluginOptions' => [
                        'allowClear' => true,
                        'maximumInputLength' => 3,
                        'language' => [
                            'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                        ],
                        'ajax' => [
                            'url' => ['../ajax/stream-list'],
                            'dataType' => 'json',
                            'data' => new JsExpression('function(params) {return {query:params.term}; }')
                        ],
                        'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                        'templateResult' => new JsExpression('function(data) { return data.text; }'),
                        'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                    ],
                ])->label('Stream');
?>

            </div>

            <div class="col-md-6">
                <?= $form->field($model, 'highest_qualification')->dropDownList(
                    ArrayHelper::map(Degree::find()->all(), 'id', 'name'),
                    [
                        'prompt' => '-- Select Level --'
                    ]
                )->label('Level') ?>
            </div>
            <div class="col-md-12">

                <?php
                if (!$model->isNewRecord) {
                    $selectedValueTag = [];
                    if (!empty($tagData)) {
                        foreach ($tagData as $value) {
                            if (!empty($value)) {
                                $selectedValueTag[array_keys($value)[0]] = ['selected' => true];
                            }
                        }
                    }
                }
                ?>
                <?= $form->field($model, 'tags[]')->widget(Select2::classname(), [
                    'data' => ArrayHelper::map(Tags::find()->active()->all(), 'id', 'name'),
                    'language' => 'en',
                    'options' => [
                        'placeholder' => '--Select Tags--',
                        'multiple' => true,
                        'options' => $selectedValueTag ?? [],
                    ],
                    'pluginOptions' => [
                        'allowClear' => true,
                    ],
                ])->label('News Tags');
?>

                <?php
                if (!$model->isNewRecord) {
                    $selectedValueCollege = [];
                    if (!empty($collegeData)) {
                        foreach ($collegeData as $value) {
                            if (!empty($value)) {
                                $selectedValueCollege[array_keys($value)[0]] = ['selected' => true];
                            }
                        }
                    }
                }
                ?>

                <?= $form->field($model, 'college[]')->widget(Select2::classname(), [
                    'data' => $collegeData ?? [], //array of text to show in the field for the selected items
                    'options' => [
                        'placeholder' => '--Select--',
                        'multiple' => true,
                        'options' => $selectedValueCollege ?? [],
                    ],
                    'pluginOptions' => [
                        'allowClear' => true,
                        'minimumInputLength' => 3,
                        'language' => [
                            'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                        ],
                        'ajax' => [
                            'url' => ['../ajax/college-list'],
                            'dataType' => 'json',
                            'data' => new JsExpression('function(params) {return {q:params.term}; }')
                        ],
                        'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                        'templateResult' => new JsExpression('function(data) { return data.text; }'),
                        'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                    ],
                ])->label('Tag College');
?>
                <?php
                if (!$model->isNewRecord) {
                    $selectedValueExam = [];
                    if (!empty($examData)) {
                        foreach ($examData as $value) {
                            if (!empty($value)) {
                                $selectedValueExam[array_keys($value)[0]] = ['selected' => true];
                            }
                        }
                    }
                }
                ?>
                <?= $form->field($model, 'exam[]')->widget(Select2::classname(), [
                    'data' => $examData ?? [], //array of text to show in the field for the selected items
                    'options' => [
                        'placeholder' => '--Select--',
                        'multiple' => true,
                        'options' => $selectedValueExam ?? [],
                    ],
                    'pluginOptions' => [
                        'allowClear' => true,
                        'minimumInputLength' => 3,
                        'language' => [
                            'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                        ],
                        'ajax' => [
                            'url' => ['../ajax/exam-list'],
                            'dataType' => 'json',
                            'data' => new JsExpression('function(params) {return {query:params.term}; }')
                        ],
                        'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                        'templateResult' => new JsExpression('function(data) { return data.text; }'),
                        'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                    ],
                ])->label('Tag Exam');
?>
                <?php
                if (!$model->isNewRecord) {
                    $selectedValueBoard = [];
                    if (!empty($boardData)) {
                        foreach ($boardData as $value) {
                            if (!empty($value)) {
                                $selectedValueBoard[array_keys($value)[0]] = ['selected' => true];
                            }
                        }
                    }
                }
                ?>
                <?= $form->field($model, 'board[]')->widget(Select2::classname(), [
                    'data' => $boardData ?? [], //array of text to show in the field for the selected items
                    'options' => [
                        'placeholder' => '--Select--',
                        'multiple' => true,
                        'options' => $selectedValueBoard ?? [],
                    ],
                    'pluginOptions' => [
                        'allowClear' => true,
                        'minimumInputLength' => 3,
                        'language' => [
                            'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                        ],
                        'ajax' => [
                            'url' => ['../ajax/board-list'],
                            'dataType' => 'json',
                            'data' => new JsExpression('function(params) {return {q:params.term}; }')
                        ],
                        'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                        'templateResult' => new JsExpression('function(data) { return data.text; }'),
                        'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                    ],
                ])->label('Tag Board');
?>
                <?php
                if (!$model->isNewRecord) {
                    $selectedArticleValue = [];
                    if (!empty($articleData)) {
                        foreach ($articleData as $value) {
                            if (!empty($value)) {
                                $selectedArticleValue[array_keys($value)[0]] = ['selected' => true];
                            }
                        }
                    }
                }
                ?>
                <?= $form->field($model, 'article[]')->widget(Select2::classname(), [
                    'data' => $articleData ?? [], //array of text to show in the field for the selected items
                    'options' => [
                        'placeholder' => '--Select--',
                        'multiple' => true,
                        'options' => $selectedArticleValue ?? [],
                    ],
                    'pluginOptions' => [
                        'allowClear' => true,
                        'minimumInputLength' => 3,
                        'language' => [
                            'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                        ],
                        'ajax' => [
                            'url' => ['../ajax/article-list'],
                            'dataType' => 'json',
                            'data' => new JsExpression('function(params) {return {q:params.term}; }')
                        ],
                        'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                        'templateResult' => new JsExpression('function(data) { return data.text; }'),
                        'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                    ],
                ])->label('Tag Article');
?>
                <?php
                if (!$model->isNewRecord) {
                    $selectedValueCourse = [];
                    if (!empty($courseData)) {
                        foreach ($courseData as $value) {
                            if (!empty($value)) {
                                $selectedValueCourse[array_keys($value)[0]] = ['selected' => true];
                            }
                        }
                    }
                }
                ?>
                <?= $form->field($model, 'course[]')->widget(Select2::classname(), [
                    'data' => $courseData ?? [], //array of text to show in the field for the selected items
                    'options' => [
                        'placeholder' => '--Select--',
                        'multiple' => true,
                        'options' => $selectedValueCourse ?? [],
                    ],
                    'pluginOptions' => [
                        'tags' => true,
                        'allowClear' => true,
                        'maximumInputLength' => 10,
                        'language' => [
                            'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                        ],
                        'ajax' => [
                            'url' => ['../ajax/all-course'],
                            'dataType' => 'json',
                            'data' => new JsExpression('function(params) {return {q:params.term}; }')
                        ],
                        'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                        'templateResult' => new JsExpression('function(data) { return data.text; }'),
                        'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                    ],
                ])->label('Tag Course');
?>
                <?php
                if (!$model->isNewRecord) {
                    $selectedValue = [];
                    if (!empty($newsData)) {
                        foreach ($newsData as $value) {
                            if (!empty($value)) {
                                $selectedValue[array_keys($value)[0]] = ['selected' => true];
                            }
                        }
                    }
                }
                ?>
                <?= $form->field($model, 'news[]')->widget(Select2::classname(), [
                    'data' => $newsData ?? [], //array of text to show in the field for the selected items
                    'options' => [
                        'placeholder' => '--Select--',
                        'multiple' => true,
                        'options' => $selectedValue ?? [],
                    ],
                    'pluginOptions' => [
                        'allowClear' => true,
                        'minimumInputLength' => 3,
                        'language' => [
                            'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                        ],
                        'ajax' => [
                            'url' => ['../ajax/news-subdomain-list'],
                            'dataType' => 'json',
                            'data' => new JsExpression('function(params) {return {q:params.term}; }')
                        ],
                        'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                        'templateResult' => new JsExpression('function(data) { return data.text; }'),
                        'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                    ],
                ])->label('Tag Other News');
?>

                <?= $form->field($model, 'translation[]')->widget(Select2::classname(), [
                    'data' => $translationData ?? [], //array of text to show in the field for the selected items
                    'options' => [
                        'placeholder' => '--Select--',
                        'multiple' => true,
                        'options' => $selectedValueTranslation ?? [],
                    ],
                    'pluginOptions' => [
                        'allowClear' => true,
                        'minimumInputLength' => 3,
                        'language' => [
                            'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                        ],
                        'ajax' => [
                            'url' => ['../ajax/news-subdomain-list'],
                            'dataType' => 'json',
                            'data' => new JsExpression('function(params) {return {q:params.term}; }')
                        ],
                        'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                        'templateResult' => new JsExpression('function(data) { return data.text; }'),
                        'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                    ],
                ])->label('News Translation');
?>
                <?= $form->field($model, 'bucket_id')->widget(Select2::class, [
                    'data' => ArrayHelper::map(LeadBucket::find()->active()->where(['entity_id' => LeadBucket::LEAD_ENTITY_NEWS_AND_ARTICLE])->all(), 'id', 'bucket'),
                    'language' => 'en',
                    'options' => ['placeholder' => '--Select Bucket--', 'value' => $bucketname ?? []],
                    'pluginOptions' => [
                        'allowClear' => true
                    ],
                ])->label('CTA Bucket'); ?>
                <?php /*if ($model->isNewRecord) {
                    if (UserService::hasNewsArticleInactiveRole()) {
                        echo $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', News::class));
                    } else {
                        $articleStatus[News::STATUS_DRAFT] = 'Draft';
                        $articleStatus[News::STATUS_ACTIVE] = 'Active';
                        echo $form->field($model, 'status')->dropDownList($articleStatus, ['options' => [0 => ['selected' => true]]]);
                    }
                } else {
                    if (UserService::hasNewsArticleInactiveRole()) {
                        echo $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', News::class));
                    } else {
                        if ($model->status == News::STATUS_INACTIVE) {
                            echo $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', News::class));
                        } else {
                            echo  $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', News::class), ['options' => [0 => ['disabled' => true]]]);
                        }
                    }
                } */?>
                <?= $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', News::class)) ?>
                <?php
                if (!$model->isNewRecord) {
                    $selectedValueTranslation = [];
                    if (!empty($translationData)) {
                        foreach ($translationData as $value) {
                            if (!empty($value)) {
                                $selectedValueTranslation[array_keys($value)[0]] = ['selected' => true];
                            }
                        }
                    }
                }
                ?>
            </div>
        </div>
    <div class="box-body">
    <div class="row">
      <div class="col-md-6">
            <?= $form->field($modelContent, 'author_id')->widget(Select2::classname(), [
                'data' => $user ?? [], //array of text to show in the field for the selected items
                'options' => [
                    'placeholder' => '--Select--',
                    'options' => [],
                ],
                'pluginOptions' => [
                    'allowClear' => true,
                    'minimumInputLength' => 3,
                    'maximumInputLength' => 10,
                    'language' => [
                        'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                    ],
                    'ajax' => [
                        'url' => ['../ajax/all-users'],
                        'dataType' => 'json',
                        'data' => new JsExpression('function(params) {return {q:params.term}; }')
                    ],
                    'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                    'templateResult' => new JsExpression('function(data) { return data.text; }'),
                    'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                ],
            ])->label('Author');
?>
        </div>
            </div>
        <div class="row">
        <div class="col-md-12">
            <?= $form->field($modelContent, 'h1')->textInput(['maxlength' => true])->hint('You can add upto 100 characters.'); ?>
        </div>
        </div>
        <div class="row">
        <div class="col-md-12">
            <?= $form->field($modelContent, 'meta_title')->textInput(['maxlength' => true])->hint('You can add upto 100 characters.'); ?>
        </div>
            </div>
        <div class="row">
        <div class="col-md-12">
            <?= $form->field($modelContent, 'meta_keywords')->textInput(['maxlength' => true])->hint('You can add upto 400 characters.'); ?>
        </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                <?= $form->field($modelContent, 'meta_description')->textInput(['maxlength' => true]) ?>
            </div>
        </div>
        <div class="row">
        <div class="col-md-12">
            <?=
            $this->render('/widget/tinymce', [
                'form' => $form,
                'model' => $modelContent,
                'entity' => 'content',
                'type' => 'news'
            ])
            ?>
        </div>
            </div>
       <div class="row">
        <div class="col-md-12">
            <?=
            $this->render('/widget/tinymce', [
                'form' => $form,
                'model' => $modelContent,
                'entity' => 'editor_remark',
                'type' => 'news'
            ])
            ?>
        </div>
            </div>
    </div>
    </div>
    <div class="box-footer">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <?php ActiveForm::end(); ?>
</div>
       <?=
        $this->render('../partials/_socket-io-client', [
            'model' => $modelContent,
            'entity' => 'news-subdomain'
        ])
        ?>
