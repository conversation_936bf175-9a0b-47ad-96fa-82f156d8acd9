<?php

use common\helpers\DataHelper;
use common\models\SaCollegeDetail;
use yii\helpers\Html;
use yii\grid\GridView;
use yii\helpers\ArrayHelper;

/* @var $this yii\web\View */
/* @var $searchModel backend\models\SaCollegeDetailSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Sa College Details';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="sa-college-detail-index box box-primary">
    <div class="box-header with-border">
        <?= Html::a('Create Sa College Detail', ['create'], ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?php // echo $this->render('_search', ['model' => $searchModel]); ?>
        <?= GridView::widget([
            'dataProvider' => $dataProvider,
            'filterModel' => $searchModel,
            'layout' => "{items}\n{summary}\n{pager}",
            'columns' => [
                ['class' => 'yii\grid\SerialColumn'],

                [
                    'attribute' => 'sa_college_name',
                    'label' => 'College Name',
                    'value' => function ($m) {
                        return !empty($m->saCollege->name) ? $m->saCollege->name : '';
                    }
                ],
                [
                    'attribute' => 'status',
                    'value' => function ($model) {
                        return ArrayHelper::getValue(DataHelper::getConstantList('STATUS', SaCollegeDetail::class), $model->status);
                    },
                    'filter' => DataHelper::getConstantList('STATUS', SaCollegeDetail::class)
                ],

                ['class' => 'yii\grid\ActionColumn','template' => '{view} {update}',],
            ],
        ]); ?>
    </div>
</div>
