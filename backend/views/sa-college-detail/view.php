<?php

use common\helpers\DataHelper;
use common\models\SaCollegeDetail;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model common\models\SaCollegeDetail */

$this->title = $model->saCollege->name;
$this->params['breadcrumbs'][] = ['label' => 'Sa College Details', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="sa-college-detail-view box box-primary">
    <div class="box-header">
        <?= Html::a('Update', ['update', 'id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?= DetailView::widget([
            'model' => $model,
            'attributes' => [
                [
                    'attribute' => 'sa_college_id',
                    'value' => function ($model) {
                        return !empty($model->saCollege) ? $model->saCollege->name : '';
                    }
                ],
                'student_faculty_ratio',
                'international_students',
                'total_enrollment',
                'acceptance_rate',
                'minimum_toefl',
                'minimum_ielts',
                'accepts_direct_application',
                'accepts_common_application',
                'total_applications',
                [
                    'label' => 'Status',
                    'attribute' => 'status',
                    'value' => ArrayHelper::getValue(DataHelper::getConstantList('STATUS', SaCollegeDetail::class), $model->status)
                ],
                [
                    'attribute' => 'created_by',
                    'value' => function ($model) {
                        return !empty($model->author) ? $model->author->name : '';
                    }
                ],
                [
                    'attribute' => 'updated_by',
                    'value' => function ($model) {
                        return $model->updater->name ?? '';
                    }
                ],
                'application_process_startdate:datetime',
                'application_process_enddate:datetime',
                'application_process_deadline:datetime',
                'created_at:datetime',
                'updated_at:datetime',
            ],
        ]) ?>
    </div>
</div>
