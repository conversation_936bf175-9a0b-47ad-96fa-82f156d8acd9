<?php

use common\helpers\DataHelper;
use common\models\SaCollege;
use common\models\SaCollegeDetail;
use common\models\User;
use kartik\datetime\DateTimePicker;
use kartik\select2\Select2;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\web\JsExpression;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model common\models\SaCollegeDetail */
/* @var $form yii\widgets\ActiveForm */

$collegeList = ArrayHelper::map(SaCollege::find()->andWhere(['id' => $model->sa_college_id])->all(), 'id', 'name');
$disabled = false;

if (isset($_GET['college_id'])) {
    $selectedCollege = SaCollege::findOne($_GET['college_id']); // Fetch the country by ID
    if ($selectedCollege) {
        $collegeList[$selectedCollege->id] = $selectedCollege->name;
        $model->sa_college_id = $selectedCollege->id; // Set the model's value
    }
}

if (isset($_GET['college_id']) || !$model->isNewRecord) {
    $disabled = true;
}
?>

<div class="sa-college-detail-form box box-primary">
    <?php $form = ActiveForm::begin(); ?>
    <div class="box-body table-responsive">

        <div class="col-md-6">
            <?= $form->field($model, 'sa_college_id')->widget(Select2::classname(), [
                'disabled' => $disabled,
                'data' => $collegeList, // array of text to show in the tag for the selected items
                'options' => [
                    'placeholder' => '--Select--',
                    'multiple' => false,
                ],
                'pluginOptions' => [
                    'allowClear' => true,
                    'minimumInputLength' => 3,
                    'language' => [
                        'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                    ],
                    'ajax' => [
                        'url' => ['../ajax/sa-college'],
                        'dataType' => 'json',
                        'data' => new JsExpression('function(params) {return {q:params.term}; }')
                    ],
                    'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                    'templateResult' => new JsExpression('function(data) { return data.text; }'),
                    'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                ],
            ])->label('SA College Name');
?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'created_by')->dropDownList(
                ArrayHelper::map(User::find()->all(), 'id', 'name'),
                [
                    'options' => [
                        Yii::$app->user->identity->id => ['Selected' => true], // Set the selected value
                    ],
                    'disabled' => true // Disable the entire dropdown
                ]
            )->label('Author'); ?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'student_faculty_ratio')->textInput(['maxlength' => true]) ?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'male_female_ratio')->textInput(['maxlength' => true]) ?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'international_students')->textInput() ?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'total_enrollment')->textInput() ?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'acceptance_rate')->textInput(['maxlength' => true]) ?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'minimum_toefl_type')->dropDownList(DataHelper::getConstantList('TOFEL_IELTS', SaCollegeDetail::class)) ?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'minimum_ielts_type')->dropDownList(DataHelper::getConstantList('TOFEL_IELTS', SaCollegeDetail::class)) ?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'minimum_toefl')->textInput() ?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'minimum_ielts')->textInput() ?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'application_website')->textInput(['maxlength' => true]) ?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'accepts_direct_application')->dropDownList(DataHelper::getConstantList('DIRECT_APPLICATION', SaCollegeDetail::class)) ?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'accepts_common_application')->dropDownList(DataHelper::getConstantList('COMMON_APPLICATION', SaCollegeDetail::class)) ?>
        </div>

        <div class="col-md-4">
            <?= $form->field($model, 'application_process_startdate')->widget(
                DateTimePicker::class,
                [
                    'options' => ['placeholder' => 'Select Date'],
                    'pluginOptions' => [
                        'timePicker' => true,
                        'locale' => [
                            'format' => 'yyyy-mm-dd HH:ii',
                        ],
                        'autoclose' => true,
                        'todayHighlight' => true,
                    ],
                ]
            );
?>
        </div>

        <div class="col-md-4">
            <?= $form->field($model, 'application_process_deadline')->widget(
                DateTimePicker::class,
                [
                    'options' => ['placeholder' => 'Select Date'],
                    'pluginOptions' => [
                        'timePicker' => true,
                        'locale' => [
                            'format' => 'yyyy-mm-dd HH:ii',
                        ],
                        'autoclose' => true,
                        'todayHighlight' => true,
                    ],
                ]
            );
?>
        </div>


        <div class="col-md-4">
            <?= $form->field($model, 'application_process_enddate')->widget(
                DateTimePicker::class,
                [
                    'options' => ['placeholder' => 'Select Date'],
                    'pluginOptions' => [
                        'timePicker' => true,
                        'locale' => [
                            'format' => 'yyyy-mm-dd HH:ii',
                        ],
                        'autoclose' => true,
                        'todayHighlight' => true,
                    ],
                ]
            );
?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'total_applications')->textInput() ?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', SaCollegeDetail::class)) ?>
        </div>

    </div>
    <div class="box-footer">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <?php ActiveForm::end(); ?>
</div>