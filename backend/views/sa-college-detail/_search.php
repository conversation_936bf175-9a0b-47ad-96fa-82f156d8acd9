<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model backend\models\SaCollegeDetailSearch */
/* @var $form yii\widgets\ActiveForm */
?>

<div class="sa-college-detail-search">

    <?php $form = ActiveForm::begin([
        'action' => ['index'],
        'method' => 'get',
    ]); ?>

    <?= $form->field($model, 'id') ?>

    <?= $form->field($model, 'sa_college_id') ?>

    <?= $form->field($model, 'student_faculty_ratio') ?>

    <?= $form->field($model, 'international_students') ?>

    <?= $form->field($model, 'total_enrollment') ?>

    <?php // echo $form->field($model, 'acceptance_rate') ?>

    <?php // echo $form->field($model, 'minimum_toefl') ?>

    <?php // echo $form->field($model, 'minimum_ielts') ?>

    <?php // echo $form->field($model, 'accepts_direct_application') ?>

    <?php // echo $form->field($model, 'accepts_common_application') ?>

    <?php // echo $form->field($model, 'total_applications') ?>

    <?php // echo $form->field($model, 'status') ?>

    <?php // echo $form->field($model, 'created_at') ?>

    <?php // echo $form->field($model, 'updated_at') ?>

    <div class="form-group">
        <?= Html::submitButton('Search', ['class' => 'btn btn-primary']) ?>
        <?= Html::resetButton('Reset', ['class' => 'btn btn-default']) ?>
    </div>

    <?php ActiveForm::end(); ?>

</div>
