<?php

use common\helpers\DataHelper;
use kartik\datetime\DateTimePicker;
use yii\helpers\Html;
use yii\widgets\ActiveForm;
use common\models\TrendingPage;
use frontend\helpers\Url;
use kartik\select2\Select2;
use yii\helpers\ArrayHelper;
use yii\web\JsExpression;

/* @var $this yii\web\View */
/* @var $model common\models\TrendingPage */
/* @var $form yii\widgets\ActiveForm */
?>

<div class="trending-page-form box box-primary">
    <?php $form = ActiveForm::begin(); ?>
    <div class="box-body table-responsive">
        <?= $form->field($model, 'entity')->dropDownList(TrendingPage::getEntityTypes(), [
            'id' => 'entity',
            'options' => [
                'others' => ['Selected' => true],
            ],
            'disabled' => !$model->isNewRecord,
        ])->label('Select Entity') ?>

        <?= $form->field($model, 'entity_id')->widget(Select2::classname(), [
            'data' => !empty($model->pageName) ? ArrayHelper::map($model->pageName, 'id', 'name') : [],
            'language' => 'en',
            'options' => [
                'placeholder' => '--Select--',
                'multiple' => false,
            ],
            'pluginOptions' => [
                'allowClear' => true,
                'placeholder' => '--Select--',
                'disabled' => !$model->isNewRecord,
                'minimumInputLength' => 3,
                'language' => [
                    'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                ],
                'ajax' => [
                    'url' => Url::to(['/faq/get-list']),
                    'dataType' => 'json',
                    'data' => new JsExpression("function(params) {return {q:params.term,depdrop_parents:$('#entity').val()}; }")
                ],
                'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                'templateResult' => new JsExpression('function(data) { return data.text; }'),
                'templateSelection' => new JsExpression('function (data) { return data.text; }'),
            ],
        ])->label('Select Page');
?>

        <?= $form->field($model, 'display_name')->textInput(['maxlength' => true]) ?>

        <?= $form->field($model, 'url')->textInput(['maxlength' => true]) ?>

        <?php if ($model->isNewRecord) {
            echo $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', TrendingPage::class), ['options' => [2 => ['selected' => true]]])->label('Status*');
        } else {
            echo $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', TrendingPage::class))->label('Status*');
        } ?>

        <?php /*
        <?= $form->field($model, 'expires_at')->widget(DateTimePicker::className(), [
            'name' => 'expires_at',
            'options' => [
                'placeholder' => 'Select Expiry Time'
            ],
            'convertFormat' => false,
            'pluginOptions' => [
                'autoclose' => true,
                'format' => 'yyyy-mm-dd hh:ii:ss',
                'todayHighlight' => true,
            ],
        ]); ?> */ ?>
    </div>
    <div class="box-footer">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <?php ActiveForm::end(); ?>
    <?php $this->registerJs('
        $(document).ready(function() {
            $(".form-group.field-trendingpage-entity_id").hide();
            $("#entity").change(function(event) {
                let entity = event.target.value;
                (entity === "others") ? $(".form-group.field-trendingpage-entity_id").hide(): $(".form-group.field-trendingpage-entity_id").show();
            })
        })
        ');
?>
</div>