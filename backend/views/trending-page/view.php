<?php

use common\helpers\DataHelper;
use yii\helpers\Html;
use yii\widgets\DetailView;
use common\models\TrendingPage;
use yii\helpers\ArrayHelper;

/* @var $this yii\web\View */
/* @var $model common\models\TrendingPage */

$this->title = $model->id;
$this->params['breadcrumbs'][] = ['label' => 'Trending Pages', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="trending-page-view box box-primary">
    <div class="box-header">
        <?= Html::a('Update', ['update', 'id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?= DetailView::widget([
            'model' => $model,
            'attributes' => [
                'id',
                [
                    'attribute' => 'entity',
                    'value' => function ($m) {
                        return TrendingPage::getEntityTypes()[$m->entity] ?? 'Unknown';
                    }
                ],
                'display_name',
                'url:url',
                [
                    'label' => 'Status',
                    'attribute' => 'status',
                    'value' => ArrayHelper::getValue(DataHelper::getConstantList('STATUS', TrendingPage::class), $model->status)
                ],
                'expires_at:datetime',
                'created_at:datetime',
                'updated_at:datetime',
            ],
        ]) ?>
    </div>
</div>