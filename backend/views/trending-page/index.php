<?php

use common\helpers\DataHelper;
use yii\helpers\Html;
use yii\grid\GridView;
use yii\widgets\Pjax;
use common\models\TrendingPage;
use yii\helpers\ArrayHelper;

/* @var $this yii\web\View */
/* @var $searchModel backend\models\TrendingPageSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Trending Pages';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="trending-page-index box box-primary">
    <?php Pjax::begin(); ?>
    <div class="box-header with-border">
        <?= Html::a('Create Trending Page', ['create'], ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?php // echo $this->render('_search', ['model' => $searchModel]); ?>
        <?= GridView::widget([
            'dataProvider' => $dataProvider,
            'filterModel' => $searchModel,
            'layout' => "{items}\n{summary}\n{pager}",
            'columns' => [
                ['class' => 'yii\grid\SerialColumn'],
                [
                    'attribute' => 'entity',
                    'value' => function ($m) {
                        return TrendingPage::getEntityTypes()[$m->entity] ?? 'Unknown';
                    },
                    'filter' => TrendingPage::getEntityTypes()
                ],
                'display_name',
                [
                    'attribute' => 'entity_id',
                    'label' => 'Title',
                    'value' => function ($model) {
                        return $model->getTitle();
                    }
                ],
                'url:url',
                [
                    'attribute' => 'status',
                    'value' => function ($model) {
                        return ArrayHelper::getValue(DataHelper::getConstantList('STATUS', TrendingPage::class), $model->status);
                    },
                    'filter' => DataHelper::getConstantList('STATUS', TrendingPage::class)
                ],

                ['class' => 'yii\grid\ActionColumn', 'template' => '{view} {update}'],
            ],
        ]); ?>
    </div>
    <?php Pjax::end(); ?>
</div>