<?php

use common\helpers\DataHelper;
use common\models\SaCountryDetail;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model common\models\SaCountryDetail */

$this->title = $model->saCountry->name;
$this->params['breadcrumbs'][] = ['label' => 'Sa Country Details', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="sa-country-detail-view box box-primary">
    <div class="box-header">
        <?= Html::a('Update', ['update', 'id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?= DetailView::widget([
            'model' => $model,
            'attributes' => [
                'title',
                [
                    'label' => 'Description',
                    'format' => 'raw',
                    'value' => html_entity_decode($model->description),
                ],
                'h1',
                'meta_title',
                'meta_description',
                'cover_image',
                [
                    'label' => 'Related Article H1',
                    'format' => 'raw',
                    'value' => html_entity_decode($model->related_article_h1),
                ],
                [
                    'label' => 'Content',
                    'format' => 'raw',
                    'value' => html_entity_decode($model->content),
                ],
                [
                    'label' => 'Status',
                    'attribute' => 'status',
                    'value' => ArrayHelper::getValue(DataHelper::getConstantList('STATUS', SaCountryDetail::class), $model->status)
                ],
                [
                    'attribute' => 'created_by',
                    'value' => function ($model) {
                        return !empty($model->author) ? $model->author->name : '';
                    }
                ],
                [
                    'attribute' => 'updated_by',
                    'value' => function ($model) {
                        return $model->updater->name ?? '';
                    }
                ],
                'created_at:datetime',
                'updated_at:datetime',
            ],
        ]) ?>
    </div>
</div>