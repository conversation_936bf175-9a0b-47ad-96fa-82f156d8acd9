<?php

use common\helpers\DataHelper;
use common\models\SaCountryDetail;
use yii\helpers\Html;
use yii\grid\GridView;
use yii\helpers\ArrayHelper;

/* @var $this yii\web\View */
/* @var $searchModel backend\models\SaCountryDetailSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Sa Country Details';
$this->params['breadcrumbs'][] = ['label' => 'Sa Country', 'url' => ['sa-country/index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="sa-country-detail-index box box-primary">
    <div class="box-header with-border">
        <?= Html::a('Create Sa Country Detail', ['create'], ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?php // echo $this->render('_search', ['model' => $searchModel]); ?>
        <?= GridView::widget([
            'dataProvider' => $dataProvider,
            'filterModel' => $searchModel,
            'layout' => "{items}\n{summary}\n{pager}",
            'columns' => [
                ['class' => 'yii\grid\SerialColumn'],

                [
                    'attribute' => 'sa_country_name',
                    'label' => 'Country',
                    'value' => function ($m) {
                        return !empty($m->saCountry->name) ? $m->saCountry->name : '';
                    }
                ],
                [
                    'attribute' => 'status',
                    'value' => function ($model) {
                        return ArrayHelper::getValue(DataHelper::getConstantList('STATUS', SaCountryDetail::class), $model->status);
                    },
                    'filter' => DataHelper::getConstantList('STATUS', SaCountryDetail::class)
                ],

                ['class' => 'yii\grid\ActionColumn','template' => '{view} {update}',],
            ],
        ]); ?>
    </div>
</div>
