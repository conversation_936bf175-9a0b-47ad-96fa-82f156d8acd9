<?php

use common\helpers\DataHelper;
use common\models\FilterPageSeo;
use yii\helpers\Html;
use yii\grid\GridView;
use yii\helpers\ArrayHelper;

/* @var $this yii\web\View */
/* @var $searchModel backend\models\FilterPageSeoSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Filter Page Seo';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="filter-page-seo-index box box-primary">
    <div class="box-header with-border">
        <?= Html::a('Create Filter Page Seo', ['create'], ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?php //echo $this->render('_search', ['model' => $searchModel]);
        ?>
        <?= GridView::widget([
            'dataProvider' => $dataProvider,
            'filterModel' => $searchModel,
            'layout' => "{items}\n{summary}\n{pager}",
            'columns' => [
                ['class' => 'yii\grid\SerialColumn'],

                'slug',
                'h1',
                'meta_title',
                // 'meta_description',
                // 'content:ntext',
                [
                    'attribute' => 'status',
                    'value' => function ($model) {
                        return ArrayHelper::getValue(DataHelper::getConstantList('STATUS', FilterPageSeo::class), $model->status);
                    },
                    'filter' => DataHelper::getConstantList('STATUS', FilterPageSeo::class)
                ],
                // 'created_at',
                // 'updated_at',

                ['class' => 'yii\grid\ActionColumn', 'template' => ' {view} {update}'],
            ],
        ]); ?>
    </div>
</div>