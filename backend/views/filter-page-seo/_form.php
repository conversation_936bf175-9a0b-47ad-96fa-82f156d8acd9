<?php

use common\helpers\DataHelper;
use common\models\FilterPageSeo;
use yii\helpers\Html;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model common\models\FilterPageSeo */
/* @var $form yii\widgets\ActiveForm */
?>

<div class="filter-page-seo-form box box-primary">
    <?php $form = ActiveForm::begin(); ?>
    <div class="box-body table-responsive">

        <?php if ($model->isNewRecord): ?>
            <?= $form->field($model, 'slug')->textInput(['maxlength' => true]) ?>
        <?php else: ?>
            <?= $form->field($model, 'slug')->textInput(['maxlength' => true, 'disabled' => true]) ?>
        <?php endif; ?>

        <?= $form->field($model, 'h1')->textInput(['maxlength' => true]) ?>

        <?= $form->field($model, 'meta_title')->textInput(['maxlength' => true]) ?>

        <?= $form->field($model, 'meta_description')->textInput(['maxlength' => true]) ?>

        <?= $form->field($model, 'localize_year')->textInput(['maxlength' => true]) ?>

        <?=
            $this->render('/widget/tinymce', [
                'form' => $form,
                'model' => $model,
                'entity' => 'content',
                'type' => '',
            ])
            ?>
        
        <?=
            $this->render('/widget/tinymce', [
                'form' => $form,
                'model' => $model,
                'entity' => 'bottom_content',
                'type' => '',
            ])
            ?>
        <?= $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', FilterPageSeo::class)) ?>

    </div>
    <div class="box-footer">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <?php ActiveForm::end(); ?>
</div>