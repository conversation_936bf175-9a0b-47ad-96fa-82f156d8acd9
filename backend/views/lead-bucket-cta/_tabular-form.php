<?php

use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use common\helpers\DataHelper;
use common\models\AlternateCtaText;
use common\models\LeadBucketCta;
use yii\bootstrap\ActiveForm;
use unclead\multipleinput\TabularInput;
use unclead\multipleinput\TabularColumn;
use kartik\datetime\DateTimePicker;
use kartik\select2\Select2;

/* @var $this \yii\web\View */
/* @var $models Item[] */

$alternateText[] = ArrayHelper::map(AlternateCtaText::find()->all(), 'id', 'cta_text');
?>
<style>
    .multiple-input {
        width: 100%;
        overflow-x: scroll;
    }

    .multiple-input-list {
        width: 120% !important;
        max-width: 120% !important;
    }
</style>
<div class="exam-date-form box box-primary">
    <?php $form = \yii\bootstrap\ActiveForm::begin([
        'id' => 'tabular-form',
        'options' => [
            'enctype' => 'multipart/form-data'
        ]
    ]) ?>
    <div class="box-body">

        <?= TabularInput::widget([
            'models' => $models,
            'modelClass' => LeadBucketCta::class,
            'allowEmptyList' => 'true',
            // 'min' => 1,
            'min' => ($model->isNewRecord) ? DataHelper::$entity_cta_count[$model->entity_id] : count($models),
            'max' => ($model->isNewRecord) ? DataHelper::$entity_cta_count[$model->entity_id] : count($models),
            'addButtonPosition' => $model->isNewRecord ? [TabularInput::POS_HEADER] : null,
            'layoutConfig' => [
                'offsetClass'   => 'col-sm-offset-4',
                'labelClass'    => 'col-sm-2',
                'wrapperClass'  => 'col-sm-3',
                'errorClass'    => 'col-sm-4'
            ],
            'attributeOptions' => [
                'enableAjaxValidation'   => false,
                'enableClientValidation' => true,
                'validateOnChange'       => true,
                'validateOnSubmit'       => true,
                'validateOnBlur'         => false,
            ],
            'form' => $form,
            'columns' => [
                [
                    'name' => 'id',
                    'type' => TabularColumn::TYPE_HIDDEN_INPUT
                ],
                [
                    'name' => 'bucket_id',
                    'title' => 'Bucket',
                    'type' => TabularColumn::TYPE_HIDDEN_INPUT,
                ],
                [
                    'name' => 'cta_text',
                    'title' => 'CTA Text',
                    'type' => TabularColumn::TYPE_TEXT_INPUT,
                ],
                [
                    'name' => 'alternate_cta_text_id',
                    'title' => 'Alternate CTA Text',
                    'type' => Select2::class,
                    'options' => [
                        'data' => ['' => '--Select--'] + ($alternateText[0] ?? []),
                        'options' => ['placeholder' => 'Search Alternate CTA...', 'class' => 'select2'],
                        'pluginOptions' => [
                            'allowClear' => true
                        ],
                    ]
                ],
                [
                    'name' => 'lead_form_title',
                    'title' => 'Lead Form Title',
                    'type' => 'textarea',
                    'options' => ['cols' => 30],
                ],
                [
                    'name' => 'lead_form_description',
                    'title' => 'Lead Form Description',
                    'type' => 'textarea',
                    'options' => ['cols' => 30],
                ],
                [
                    'name'  => 'page_event',
                    'title' => 'Page Event',
                    'type' => TabularColumn::TYPE_DROPDOWN,
                    'items' => DataHelper::getConstantList('LEAD_PAGE', LeadBucketCta::class),
                ],
                [
                    'name' => 'page_link',
                    'title' => 'Page Link',
                    'type' => TabularColumn::TYPE_TEXT_INPUT,
                ],
                [
                    'name'  => 'cta_position',
                    'type' => TabularColumn::TYPE_HIDDEN_INPUT,
                ],
                [
                    'name'  => 'cta_position',
                    'title' => 'CTA Position',
                    'type' => TabularColumn::TYPE_DROPDOWN,
                    'items' => $model->cta_position ?? DataHelper::$cta_positions,
                    // 'options' => function ($model) {
                    //     return $model->isNewRecord ? [] : ['disabled' => true];
                    // },
                    'options' => ['disabled' => true]
                ],
                [
                    'name'  => 'cta_slug',
                    'title' => 'CTA Nomenclature',
                    'type' => 'textarea',
                    // 'options' => ['disabled' => true]
                    'options' => function ($model) {
                        return $model->isNewRecord ? [] : ['disabled' => true];
                    },
                ],
                [
                    'name'  => 'platform',
                    'title' => 'Platform',
                    'type' => TabularColumn::TYPE_DROPDOWN,
                    'items' => DataHelper::getConstantList('PLATFORM', LeadBucketCta::class),
                ]
                // [
                //     'name'  => 'status',
                //     'title' => 'Status',
                //     'type' => TabularColumn::TYPE_DROPDOWN,
                //     'items' => DataHelper::getConstantList('STATUS', LeadBucketCta::class),
                // ]
            ]
        ]) ?>

    </div>
    <?php if (!$model->isNewRecord): ?>
        <div class="box-body">
            <?php echo $this->render('../lead-bucket-cta/_tabular-form-cta_thank_you', [
                'model' => $model,
                'models' => $models
            ]) ?>
        </div>
    <?php endif; ?>
    <div class="box-footer">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <?php ActiveForm::end(); ?>
    <?php echo $this->registerJs(
        $this->registerJs('
        $(document).ready(function () {
            $("td.list-cell__button").hide();
            var rows = $(".multiple-input-list").find(".multiple-input-list__item"); // Get all rows
            rows.each(function(index) {
                let valueHidden = $(".list-cell__cta_text #leadbucketcta-"+index+"-cta_position").val();
                let valueDropdown = $(".list-cell__cta_position #leadbucketcta-"+index+"-cta_position");
                let entityId = $("#leadbucket-entity_id").val();
                if($("#leadbucketcta-"+index+"-page_event").val() == 1){
                    $("#leadbucketcta-"+index+"-page_link").prop("disabled", false);
                } else {
                    $("#leadbucketcta-"+index+"-page_link").prop("disabled", true).val("");
                }
                valueDropdown.find("option").each(function() {
                    let arr = [1,6,7,8,9,10,12];
                    if(valueHidden === ""){
                        if (arr.indexOf(Number(entityId)) !== -1) {
                            $("#leadbucketcta-0-cta_position option").eq(2).prop("selected", true);
                            $(".list-cell__cta_text #leadbucketcta-0-cta_position").val(2);
                            $("#leadbucketcta-1-cta_position option").eq(3).prop("selected", true);
                            $(".list-cell__cta_text #leadbucketcta-1-cta_position").val(3);
                        } else if ($(this).index() === index){
                            $(this).prop("selected", true);
                            $(".list-cell__cta_text #leadbucketcta-"+index+"-cta_position").val(index);
                        }
                    } else if ($(this).text() === valueHidden) {
                        $(this).prop("selected", true);
                        return false;
                    }
                });
            });      
        });
        $(document).on("change", ".multiple-input-list__item .list-cell__page_event .form-control", function(e) {
            let indexVal = $(this).closest(".multiple-input-list__item").data("index");
            var pageEventSelect = $("#leadbucketcta-"+indexVal+"-page_event");
            var pageLinkInput = $("#leadbucketcta-"+indexVal+"-page_link");
            pageLinkInput.prop("disabled", true).val("");
            if(pageEventSelect.val() == 1) {
                pageLinkInput.prop("disabled", false);
            }
        });
        ')
    );
?>
</div>