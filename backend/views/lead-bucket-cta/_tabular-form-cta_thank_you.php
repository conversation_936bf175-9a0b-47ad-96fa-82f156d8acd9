<?php

use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use common\helpers\DataHelper;
use common\models\AlternateCtaText;
use common\models\CtaThankYou;
use common\models\LeadBucket;
use common\models\LeadBucketCta;
use common\models\LeadBucketCtaDetail;
use yii\bootstrap\ActiveForm;
use unclead\multipleinput\TabularInput;
use unclead\multipleinput\TabularColumn;
use kartik\datetime\DateTimePicker;

/* @var $this \yii\web\View */
/* @var $models Item[] */
// Get the selected CTA IDs from the models

$selectedCtaIds = array_filter(ArrayHelper::getColumn($models, 'cta_text')); // Remove empty values

if (!empty($selectedCtaIds)) {
    $ctaThankModel = CtaThankYou::find()
    ->where(['cta_text' => $selectedCtaIds])
    ->andWhere(['entity_type' => $_GET['entity_id']])
    ->all();
   
    // Filter CtaThankYou based on the selected CTA IDs
    $ctaData = ArrayHelper::map(
        CtaThankYou::find()->where(['cta_text' => $selectedCtaIds])->all(),
        'cta_text',
        'cta_text'
    );
} else {
    $ctaThankModel = [];
    $ctaData = [];
}

$entity = $_GET['entity_id'];

?>
<style>
    .multiple-input {
        width: 100%;
        overflow-x: scroll;
    }

    .multiple-input-list {
        width: 120% !important;
        max-width: 120% !important;
    }

    .list-cell__button {
        display: none;
    }
</style>

<div class="exam-date-form box box-primary">
    <?php $form = \yii\bootstrap\ActiveForm::begin([
        'id' => 'tabular-form',
        'options' => [
            'enctype' => 'multipart/form-data'
        ]
    ]) ?>
    <div class="box-body">

        <?= TabularInput::widget([
            'models' => $ctaThankModel,
            'modelClass' => CtaThankYou::class,
            'allowEmptyList' => 'true',
            'layoutConfig' => [
                'offsetClass'   => 'col-sm-offset-4',
                'labelClass'    => 'col-sm-2',
                'wrapperClass'  => 'col-sm-3',
                'errorClass'    => 'col-sm-4'
            ],
            'attributeOptions' => [
                'enableAjaxValidation'   => false,
                'enableClientValidation' => true,
                'validateOnChange'       => true,
                'validateOnSubmit'       => true,
                'validateOnBlur'         => false,
            ],
            'form' => $form,
            'columns' => [
                [
                    'name' => 'id',
                    'type' => TabularColumn::TYPE_HIDDEN_INPUT
                ],
                [
                    'name' => 'entity_type',
                    'title' => 'Entity',
                    'type' => TabularColumn::TYPE_HIDDEN_INPUT,
                    'defaultValue' => $_GET['entity_id']
                ],
                [
                    'name' => 'cta_text',
                    'title' => empty($ctaThankModel) ? '' : 'CTA Text',
                    'type' => TabularColumn::TYPE_DROPDOWN,
                    'items' => $ctaData ?? [],
                    'options' => ['readonly' => true]
                ],
                [
                    'name' => 'thank_you_text',
                    'title' => empty($ctaThankModel) ? '' : 'Thank You Message',
                    'type' => 'textarea',
                ],
            ]
        ]) ?>

    </div>
    <?php ActiveForm::end(); ?>
</div>