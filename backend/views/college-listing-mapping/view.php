<?php

use common\helpers\DataHelper;
use common\models\CollegeListingMapping;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model common\models\CollegeListingMapping */

$this->title = $model->id;
$this->params['breadcrumbs'][] = ['label' => 'College Listing Mappings', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="college-listing-mapping-view box box-primary">
    <div class="box-header">
        <?= Html::a('Update', ['update', 'id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?= DetailView::widget([
            'model' => $model,
            'attributes' => [
                'id',
                [
                    'attribute' => 'grouping_id',
                    'label' => 'Grouping Page',
                    'value' => function ($model) {
                        return implode(', ', array_map(function ($group) {
                            return $group->slug;
                        }, $model->grouping));
                    }
                ],
                [
                    'attribute' => 'listing_page_id',
                    'label' => 'Listing Page',
                    'value' => function ($model) {
                        return $model->listing->slug ?? '';  // Directly access the `slug`
                    }
                ],
                [
                    'label' => 'Status',
                    'attribute' => 'status',
                    'value' => ArrayHelper::getValue(DataHelper::getConstantList('STATUS', CollegeListingMapping::class), $model->status)
                ],
                'created_at:datetime',
                'updated_at:datetime',
            ],
        ]) ?>
    </div>
</div>