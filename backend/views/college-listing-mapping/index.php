<?php

use common\helpers\DataHelper;
use common\models\CollegeListingMapping;
use yii\helpers\Html;
use yii\grid\GridView;
use yii\helpers\ArrayHelper;

/* @var $this yii\web\View */
/* @var $searchModel backend\models\CollegeListingMappingSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'College Listing Mappings';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="college-listing-mapping-index box box-primary">
    <div class="box-header with-border">
        <?= Html::a('Create College Listing Mapping', ['create'], ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?php // echo $this->render('_search', ['model' => $searchModel]);
        ?>
        <?= GridView::widget([
            'dataProvider' => $dataProvider,
            'filterModel' => $searchModel,
            'layout' => "{items}\n{summary}\n{pager}",
            'columns' => [
                ['class' => 'yii\grid\SerialColumn'],
                'id',
                [
                    'attribute' => 'listing_page_id',
                    'label' => 'Listing Page',
                    'value' => function ($model) {
                        return $model->listing->slug ?? '';
                    }
                ],
                // [
                //     'attribute' => 'grouping_id',
                //     'label' => 'Grouping Page',
                //     'value' => function ($model) {
                //         return $model->grouping->slug ?? '';
                //     }
                // ],
                [
                    'attribute' => 'status',
                    'value' => function ($model) {
                        return ArrayHelper::getValue(DataHelper::getConstantList('STATUS', CollegeListingMapping::class), $model->status);
                    },
                    'filter' => DataHelper::getConstantList('STATUS', CollegeListingMapping::class)
                ],
                ['class' => 'yii\grid\ActionColumn', 'template' => '{view} {update}'],
            ],
        ]); ?>
    </div>
</div>