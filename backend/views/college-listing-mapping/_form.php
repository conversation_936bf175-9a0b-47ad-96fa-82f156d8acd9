<?php

use common\helpers\DataHelper;
use common\models\CollegeListingMapping;
use common\models\FilterPageSeo;
use kartik\select2\Select2;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\web\JsExpression;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model common\models\CollegeListingMapping */
/* @var $form yii\widgets\ActiveForm */

$usedListingIds = CollegeListingMapping::find()->select('listing_page_id')->column();

if (!$model->isNewRecord) {
    $listingData[] = ArrayHelper::map(FilterPageSeo::find()->where(['id' => $model->listing_page_id])->all(), 'id', 'slug');
} else {
    $listingData[] = ArrayHelper::map(FilterPageSeo::find()->where(['NOT IN', 'id', $usedListingIds])->all(), 'id', 'slug');
}

foreach ($model->grouping as $value) {
    $groupingData[] = ArrayHelper::map(FilterPageSeo::find()->where(['id' => $value->id])->all(), 'id', 'slug');
}

?>

<div class="college-listing-mapping-form box box-primary">
    <?php $form = ActiveForm::begin(); ?>
    <div class="box-body table-responsive">
        <?php
        if (!$model->isNewRecord) {
            $selectedListingValue = [];
            if (!empty($listingData)) {
                foreach ($listingData as $value) {
                    if (!empty($value)) {
                        $selectedListingValue[array_keys($value)[0]] = ['selected' => true];
                    }
                }
            }
        }
        ?>
        <div class="col-md-6">
            <?= $form->field($model, 'listing_page_id')->widget(Select2::classname(), [
                'data' => $listingData ?? [], //array of text to show in the field for the selected items
                'options' => [
                    'placeholder' => '--Select--',
                    'multiple' => false,
                    'options' => $selectedListingValue ?? [],
                ],
                'pluginOptions' => [
                    'allowClear' => true,
                    'minimumInputLength' => 3,
                    'disabled' => !$model->isNewRecord,
                    'language' => [
                        'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                    ],
                    'ajax' => [
                        'url' => ['../ajax/filter-page-seo-list'],
                        'dataType' => 'json',
                        'data' => new JsExpression('function(params) {return {q:params.term}; }')
                    ],
                    'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                    'templateResult' => new JsExpression('function(data) { return data.text; }'),
                    'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                ],
            ])->label('Listing Pages');
?>
        </div>
        <?php
        if (!$model->isNewRecord) {
            $selectedGroupingValue = [];
            if (!empty($groupingData)) {
                foreach ($groupingData as $value) {
                    if (!empty($value)) {
                        $selectedGroupingValue[array_keys($value)[0]] = ['selected' => true];
                    }
                }
            }
        }
        ?>

        <div class="col-md-6">
            <?= $form->field($model, 'grouping_id')->widget(Select2::classname(), [
                'data' => $groupingData ?? [], //array of text to show in the field for the selected items
                'options' => [
                    'placeholder' => '--Select--',
                    'multiple' => true,
                    'options' => $selectedGroupingValue ?? [],
                ],
                'pluginOptions' => [
                    'allowClear' => true,
                    'minimumInputLength' => 3,
                    'language' => [
                        'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                    ],
                    'ajax' => [
                        'url' => ['../ajax/filter-page-seo-list'],
                        'dataType' => 'json',
                        'data' => new JsExpression('function(params) {return {q:params.term}; }')
                    ],
                    'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                    'templateResult' => new JsExpression('function(data) { return data.text; }'),
                    'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                ],
            ])->label('Grouping Pages');
?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', CollegeListingMapping::class)) ?>
        </div>

    </div>
    <div class="col-md-12 box-footer">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <?php ActiveForm::end(); ?>
</div>