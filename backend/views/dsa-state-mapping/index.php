<?php

use common\helpers\DataHelper;
use common\models\DsaStateMapping;
use yii\helpers\Html;
use yii\grid\GridView;
use yii\helpers\ArrayHelper;

/* @var $this yii\web\View */
/* @var $searchModel backend\models\DsaStateMappingSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Dsa State Mappings';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="dsa-state-mapping-index box box-primary">
    <div class="box-header with-border">
        <?= Html::a('Create Dsa State Mapping', ['create'], ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?php // echo $this->render('_search', ['model' => $searchModel]);
        ?>
        <?= GridView::widget([
            'dataProvider' => $dataProvider,
            'filterModel' => $searchModel,
            'layout' => "{items}\n{summary}\n{pager}",
            'columns' => [
                ['class' => 'yii\grid\SerialColumn'],

                'campaign_id',
                [
                    'attribute' => 'state_name',
                    'label' => 'State',
                    'value' => 'state.name'
                ],
                [
                    'attribute' => 'stream_name',
                    'label' => 'Stream',
                    'value' => 'stream.name'
                ],
                [
                    'attribute' => 'degree_name',
                    'label' => 'Level',
                    'value' => 'degree.display_name'
                ],
                [
                    'attribute' => 'status',
                    'value' => function ($model) {
                        return ArrayHelper::getValue(DataHelper::getConstantList('STATUS', DsaStateMapping::class), $model->status);
                    },
                    'filter' => DataHelper::getConstantList('STATUS', DsaStateMapping::class)
                ],

                ['class' => 'yii\grid\ActionColumn', 'template' => '{view} {update}'],
            ],
        ]); ?>
    </div>
</div>