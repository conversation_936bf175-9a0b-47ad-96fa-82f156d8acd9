<?php

use common\helpers\DataHelper;
use common\models\Degree;
use common\models\DsaStateMapping;
use common\models\State;
use common\models\Stream;
use kartik\select2\Select2;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\web\JsExpression;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model common\models\DsaStateMapping */
/* @var $form yii\widgets\ActiveForm */

if (!empty($model->state_id)) {
    $state =  ArrayHelper::map(State::find()->where(['id' => $model->state_id])->all(), 'id', 'name');
}
if (!empty($model->stream_id)) {
    $stream =  ArrayHelper::map(Stream::find()->where(['id' => $model->stream_id])->all(), 'id', 'name');
}
if (!empty($model->degree_id)) {
    $level =  ArrayHelper::map(Degree::find()->where(['id' => $model->degree_id])->all(), 'id', 'display_name');
}
?>

<div class="dsa-state-mapping-form box box-primary">
    <?php $form = ActiveForm::begin(); ?>
    <div class="box-body table-responsive">

        <?= $form->field($model, 'campaign_id')->textInput([
            'disabled' => !$model->isNewRecord
        ]) ?>

        <?= $form->field($model, 'type')->dropDownList(
            ['' => '--Select--'] +
                DataHelper::getConstantList('TYPE', DsaStateMapping::class),
            [
                'id' => 'type-selector',
                'placeholder' => '--Select--',
                'onchange' => ' console.log(this.value);
                if (this.value == 1) {
                    $("#dsastatemapping-state_id").attr("disabled", false);
                    $("#dsastatemapping-stream_id").attr("disabled", true);
                    $("#dsastatemapping-degree_id").attr("disabled", true);
                } else if (this.value == 2) {
                    $("#dsastatemapping-state_id").attr("disabled", true);
                    $("#dsastatemapping-stream_id").attr("disabled", false);
                    $("#dsastatemapping-degree_id").attr("disabled", false);
                } else {
                    $("#dsastatemapping-state_id").attr("disabled", true);
                    $("#dsastatemapping-stream_id").attr("disabled", true);
                    $("#dsastatemapping-degree_id").attr("disabled", true);
                }'
            ]
        )->label('Type') ?>

        <?= $form->field($model, 'state_id')->widget(Select2::classname(), [
            'data' => $state ?? [], //array of text to show in the field for the selected items
            'options' => [
                'placeholder' => '--Select--',
                'multiple' => false,
                'disabled' => $model->type == DsaStateMapping::TYPE_STATE ? false : true
            ],
            'pluginOptions' => [
                'tags' => false,
                'allowClear' => true,
                'minimumInputLength' => 3,
                'maximumInputLength' => 20,
                'language' => [
                    'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                ],
                'ajax' => [
                    'url' => ['../ajax/state-list'],
                    'dataType' => 'json',
                    'data' => new JsExpression('function(params) {return {q:params.term}; }')
                ],
                'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                'templateResult' => new JsExpression('function(data) { return data.text; }'),
                'templateSelection' => new JsExpression('function (data) { return data.text; }'),
            ],
        ])->label('State');
?>

        <?= $form->field($model, 'stream_id')->widget(Select2::classname(), [
            'data' => $stream ?? [], //array of text to show in the field for the selected items
            'options' => [
                'placeholder' => '--Select--',
                'multiple' => false,
                'disabled' => $model->type == DsaStateMapping::TYPE_STREAM_LEVEL ? false : true
            ],
            'pluginOptions' => [
                'tags' => false,
                'allowClear' => true,
                'minimumInputLength' => 0,
                'maximumInputLength' => 20,
                'language' => [
                    'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                ],
                'ajax' => [
                    'url' => ['../ajax/stream-list'],
                    'dataType' => 'json',
                    'data' => new JsExpression('function(params) {return {query:params.term}; }')
                ],
                'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                'templateResult' => new JsExpression('function(data) { return data.text; }'),
                'templateSelection' => new JsExpression('function (data) { return data.text; }'),
            ],
        ])->label('Stream');
?>

        <?= $form->field($model, 'degree_id')->widget(Select2::classname(), [
            'data' => $level ?? [], //array of text to show in the field for the selected items
            'options' => [
                'placeholder' => '--Select--',
                'multiple' => false,
                'disabled' => $model->type == DsaStateMapping::TYPE_STREAM_LEVEL ? false : true
            ],
            'pluginOptions' => [
                'tags' => false,
                'allowClear' => true,
                'minimumInputLength' => 0,
                'maximumInputLength' => 20,
                'language' => [
                    'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                ],
                'ajax' => [
                    'url' => ['../ajax/degree-list'],
                    'dataType' => 'json',
                    'data' => new JsExpression('function(params) {return {query:params.term}; }')
                ],
                'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                'templateResult' => new JsExpression('function(data) { return data.text; }'),
                'templateSelection' => new JsExpression('function (data) { return data.text; }'),
            ],
        ])->label('Level');
?>

        <?= $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', DsaStateMapping::class)) ?>

    </div>
    <div class="box-footer">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <?php ActiveForm::end(); ?>
</div>