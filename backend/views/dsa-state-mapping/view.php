<?php

use common\helpers\DataHelper;
use common\models\DsaStateMapping;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model common\models\DsaStateMapping */

$this->title = !empty($model->state) ? $model->state->name : $model->stream->name;
$this->params['breadcrumbs'][] = ['label' => 'Dsa State Mappings', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="dsa-state-mapping-view box box-primary">
    <div class="box-header">
        <?= Html::a('Update', ['update', 'id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
        <?= Html::a('Back', ['index'], ['class' => 'btn btn-primary btn-flat']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?= DetailView::widget([
            'model' => $model,
            'attributes' => [
                'campaign_id',
                [
                    'attribute' => 'state_id',
                    'label' => 'State',
                    'format' => 'raw',
                    'value' => $model->state->name ?? '-',
                ],
                [
                    'attribute' => 'stream_id',
                    'label' => 'Stream',
                    'format' => 'raw',
                    'value' => $model->stream->name ?? '-',
                ],
                [
                    'attribute' => 'degree_id',
                    'label' => 'Degree',
                    'format' => 'raw',
                    'value' => $model->degree->display_name ?? '-',
                ],
                [
                    'attribute' => 'status',
                    'label' => 'Status',
                    'value' => ArrayHelper::getValue(DataHelper::getConstantList('STATUS', DsaStateMapping::class), $model->status),
                ],
                'created_at:datetime',
                'updated_at:datetime',
            ],
        ]) ?>
    </div>
</div>