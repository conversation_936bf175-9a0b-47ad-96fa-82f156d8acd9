<?php

use common\helpers\DataHelper;
use common\models\Article;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\widgets\DetailView;
use common\models\Degree;
use common\models\User;

/* @var $this yii\web\View */
/* @var $model common\models\Article */

$this->title = $model['id'];
$this->params['breadcrumbs'][] = ['label' => 'CMS Logs', 'url' => ['view?id=' . $model->model_id . '&entity=' . $_GET['entity']]];
$this->params['breadcrumbs'][] = $this->title;

$entity = $_GET['entity'] ?? '';
$camelCaseEntity = ucfirst(str_replace(' ', '', ucwords(str_replace(['_', '-'], ' ', $entity)))) . ' ID';
?>
<div class="article-view box box-primary">
    <div class="box-header">
        <h3>Log Detail</h3>
    </div>
    <div class="box-body table-responsive no-padding">
        <?= DetailView::widget([
            'model' => $model,
            'attributes' => [
                'id',
                'entry_id',
                [
                    'label' => $camelCaseEntity,
                    'attribute' => 'model_id'
                ],
                [
                    'label' => 'Modified By ID',
                    'attribute' => 'user_id'
                ],
                [
                    'label' => 'Modified By Name',
                    'value' => $stats['user_name']
                ],
                'action',
                'model',
                'field',
                'new_value',
                'old_value',
                [
                    'label' => 'Added Words',
                    'value' => isset($stats['added_words']) && is_array($stats['added_words']) && count($stats['added_words']) > 0
                        ? "[ '" . implode("', '", $stats['added_words']) . "' ]"
                        : '[ ]',
                ],
                [
                    'label' => 'Removed Words',
                    'value' => isset($stats['removed_words']) && is_array($stats['removed_words']) && count($stats['removed_words']) > 0
                        ? "['" . implode("', '", $stats['removed_words']) . "']"
                        : '[ ]',
                ],
                [
                    'label' => 'Added Word Count',
                    'value' => $stats['added_count'] ?? 0,
                ],
                [
                    'label' => 'Removed Word Count',
                    'value' => $stats['removed_count'] ?? 0,
                ],
                [
                    'label' => 'Total Word Count',
                    'value' => $stats['new_word_total'] ?? 0,
                ],
                'created'
            ],
        ]) ?>
    </div>
</div>