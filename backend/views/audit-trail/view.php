<?php

use frontend\helpers\Html;
use yii\grid\GridView;

$this->title = ucwords(str_replace('_', ' ', $model_info)) . ' : ' . $entity_name . ' Logs';
$pageIndex = isset($_GET['page']) && $_GET['page'] !== '' ? '&page=' . $_GET['page'] : '';
$this->params['breadcrumbs'][] = ['label' => 'CMS Activity', 'url' => ['index?entity=' . $model_info . $pageIndex]];
$this->params['breadcrumbs'][] = 'Timeline View';
?>

<?php $hideTimeline = empty($model->model); ?>
<div class="audit-trail-index <?= !empty($dataProvider->allModels) ? ' box box-primary' : ''; ?>">
    <?php

    // Sanitize $_GET['id']
    $id = $_GET['id'] ?? null;
    if (is_array($id)) {
        $id = reset($id); // or set to null if preferred
    }

    // Sanitize $_GET['page']
    $page = $_GET['page'] ?? null;
    if (is_array($page)) {
        $page = reset($page);
    }

    // Sanitize $model_info
    $entity = $model_info;
    if (is_array($model_info)) {
        $entity = reset($model_info); // or implode(',', $model_info) if needed
    }

    // Build params safely
    $params = [
        'diff',
        'id' => $id,
        'entity' => $entity,
    ];

    if (!empty($page)) {
        $params['page'] = $page;
    }

    // Render button
    echo Html::a('Compare Logs', $params, [
        'class' => 'btn btn-success btn-flat',
        'style' => 'margin:10px;',
    ]);
    ?>

    <div class="box-body table-responsive no-padding">
        <?= GridView::widget([
            'dataProvider' => $dataProvider,
            'layout' => "{items}\n{summary}\n{pager}",
            'columns' => [
                ['class' => 'yii\grid\SerialColumn'],
                'id',
                [
                    'attribute' => 'audit',
                    'label' => 'Last Modified At',
                    'value' => 'created'
                ],
                [
                    'attribute' => 'log_detail',
                    'format' => 'raw',
                    'value' => function ($model) {
                        return '<a href="' . Yii::$app->urlManager->createUrl(['/audit-trail/log-detail', 'id' => $model['id'], 'entity' => $_GET['entity']]) . '">
                                    <span class="glyphicon glyphicon-eye-open"></span>
                                </a>';
                    },
                ],
            ],
        ]); ?>
    </div>
</div>

<style>
    #w0.grid-view thead {
        background-color: lightgray;
    }

    .timeline {
        margin-top: 10px;
    }
</style>