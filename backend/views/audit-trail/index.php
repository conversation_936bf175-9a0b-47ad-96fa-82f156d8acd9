<?php

use common\helpers\DataHelper;
use frontend\helpers\Html;
use yii\bootstrap\ActiveForm;
use yii\grid\GridView;
use yii\helpers\ArrayHelper;
use yii\helpers\Url;

$this->title = 'Timeline View: ' . ucwords(str_replace('_', ' ', $model_info));
$this->params['breadcrumbs'][] = ['label' => 'CMS Activity', 'url' => ['index']];
$this->params['breadcrumbs'][] = 'Timeline View';
$model->model = $model_info;

$entityOptions = ['' => '--SELECT--'] + ArrayHelper::map(
    array_filter(array_keys(DataHelper::$audit_entity), function ($key) {
        return $key !== '';
    }),
    function ($key) {
        return $key; // Option value
    },
    function ($key) {
        return strtoupper(str_replace('_', ' ', $key)); // Option label
    }
);
?>

<div class="audit-trail-form box box-primary">
    <?php $form = ActiveForm::begin(['method' => 'get', 'action' => ['audit-trail/index']]); ?>
    <div class="box-body table-responsive">
        <div class="col-md-6">
            <div class="form-group">
                <label for="entry_id">Entity</label>
                <?= Html::dropDownList(
                    'entity',           // name attribute
                    $model_info,        // selected value
                    $entityOptions,     // options
                    [
                        'class' => 'form-control',
                        'id' => 'entry_id',
                        'required' => true,
                    ]
                ) ?>
            </div>
        </div>

        <div class="box-footer">
            <?= Html::submitButton('View', ['class' => 'btn btn-success btn-flat', 'style' => 'margin-top: 14px; width: 8%;']) ?>
        </div>
    </div>
    <?php ActiveForm::end(); ?>
</div>

<?php $hideTimeline = empty($model->model); ?>

<div class="audit-trail-index <?= !empty($dataProvider['arrayDataProvider']->allModels) ? ' box box-primary' : ''; ?>">
    <div class="box-body table-responsive no-padding">
        <?php if (!empty($dataProvider['arrayDataProvider']->allModels) && ($dataProvider['arrayDataProvider']->getCount() > 0)) { ?>
            <?= GridView::widget([
                'dataProvider' => $dataProvider['arrayDataProvider'],
                // 'filterModel' => $searchModel,
                'layout' => "{items}\n{summary}\n{pager}",
                'columns' => [
                    ['class' => 'yii\grid\SerialColumn'],
                    'entity',
                    [
                        'attribute' => 'entity_id',
                        'label' => 'Entity Id',
                    ],
                    'name',
                    [
                        'attribute' => 'page',
                        'label' => 'Page',
                        'filter' => false
                    ],
                    [
                        'attribute' => 'user_name',
                        'label' => 'Last Modified By',
                    ],
                    [
                        'attribute' => 'audit',
                        'label' => 'Last Modified At',
                        'format' => 'raw',
                        'value' => function ($model) {
                            if (!empty($model['audit'])) {
                                usort($model['audit'], function ($a, $b) {
                                    return strtotime($b['created']) <=> strtotime($a['created']);
                                });
                                return $model['audit'][0]['created'];
                            }
                            return '—';
                        },
                    ],
                    [
                        'class' => 'yii\grid\ActionColumn',
                        'template' => '{view}',
                        'urlCreator' => function ($action, $model, $key, $index) {
                            if ($action === 'view') {
                                $params = [
                                    'audit-trail/view',
                                    'id' => $model['entity_id'],
                                    'entity' => $model['entity'],
                                ];

                                if (!empty($_GET['page'])) {
                                    $params['page'] = $_GET['page'];
                                }

                                return Url::to($params);
                            }
                            return '#';
                        },
                    ],

                ],
            ]); ?>
        <?php } else { ?>
            <ul class="timeline" style="margin-top: 10px; <?= $hideTimeline ? 'display:none;' : '' ?>">
                <li>
                    <!-- timeline icon -->
                    <i class="fa fa-envelope bg-blue"></i>
                    <div class="timeline-item">
                        <h3 class="timeline-header text-red">
                            Sorry Data is unavailable!
                        </h3>
                    </div>
                </li>
            </ul>
        <?php } ?>
    </div>
</div>

<style>
    #w1.grid-view thead {
        background-color: lightgray;
    }
</style>