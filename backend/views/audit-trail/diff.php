<?php

use backend\controllers\AuditTrailController;
use common\helpers\DataHelper;
use yii\bootstrap\ActiveForm;
use yii\db\Query;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;

$this->title = 'Compare Logs';
$this->params['breadcrumbs'][] = ['label' => 'CMS Logs', 'url' => ['view?id=' . $id . '&entity=' . $_GET['entity']]];
$this->params['breadcrumbs'][] = 'Compare Logs';

$entity = $_GET['entity'];

$dbName = Yii::$app->db->createCommand('SELECT DATABASE()')->queryScalar();
$model_id = DataHelper::$audit_entity[$entity];
$model_name = str_replace(' ', '', ucwords(str_replace('_', ' ', $model_id)));

$result = new Query();

$result->select(['created'])
    ->from('gmu_log.audit_trail')
    ->innerJoin($dbName . '.' . $model_id, $model_id . '.id = audit_trail.model_id');

if ($model_id == 'college') {
    $result->innerJoin($dbName . '.sponsor_college', 'sponsor_college.college_id = audit_trail.model_id');
}

if (strpos($model_id, 'content') !== false) {
    $model_clm = AuditTrailController::$model_column[$model_id];
    $result->innerJoin($dbName . '.' . $entity, $entity . '.id = ' . $model_id . '.' . $model_clm);
}

$res = $result->where(['like', 'model', $model_name])
    ->andWhere(['model_id' => $id])
    ->andWhere(['or', ['field' => 'content'], ['field' => 'description']])
    ->orderBy(['created' => SORT_DESC])
    ->all();

$createdList = ArrayHelper::map($res, 'created', 'created');
?>

<!-- Diff2Html CDN -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/diff2html/bundles/css/diff2html.min.css" />
<script src="https://cdn.jsdelivr.net/npm/diff2html/bundles/js/diff2html.min.js"></script>

<?php if (isset($res) && count($res) > 1): ?>
    <div class="audit-trail-form box box-primary">
        <?php $form = ActiveForm::begin(['action' => ['audit-trail/diff', 'id' => $id, 'entity' => $entity]]); ?>
        <?= Html::hiddenInput('id', $id) ?>

        <div class="box-body table-responsive">

            <div class="col-md-6">
                <?= $form->field($model, 'start_date')->dropDownList(
                    $createdList,
                    ['prompt' => 'Select a timestamp']
                ) ?>
            </div>
            <div class="col-md-6">
                <?= $form->field($model, 'end_date')->dropDownList(
                    $createdList,
                    ['prompt' => 'Select a timestamp']
                ) ?>
            </div>

            <div class="box-footer">
                <?= Html::submitButton('Compare Logs', ['class' => 'btn btn-success btn-flat', 'style' => 'margin-top: 14px; width: 8%;']) ?>
            </div>

        </div>
        <?php ActiveForm::end(); ?>
    </div>
<?php endif; ?>

<div id="diff-view"></div>

<script>
    // Get the diff from PHP and encode safely for JS
    const diffText = <?= json_encode($diff, JSON_HEX_TAG | JSON_HEX_AMP | JSON_HEX_APOS | JSON_HEX_QUOT); ?>;

    const target = document.getElementById('diff-view');
    const config = {
        drawFileList: false,
        outputFormat: 'side-by-side', // or 'line-by-line'
        matching: 'lines'
    };

    const diffHtml = Diff2Html.html(diffText, config);
    target.innerHTML = diffHtml;

    // Helper: Calculate wrapped line count of .d2h-code-line-ctn span
    function getWrappedLineCount(el) {
        if (!el) return 0;
        const style = window.getComputedStyle(el);
        const rect = el.getBoundingClientRect();

        let lineHeight = style.lineHeight;
        if (lineHeight === 'normal') {
            lineHeight = parseFloat(style.fontSize) * 1.2;
        } else {
            lineHeight = parseFloat(lineHeight);
        }

        return Math.round(rect.height / lineHeight);
    }

    // Helper: get wrapped line count for given rowNumber and type ('del' or 'ins')
    function getLinesForRowNumber(rowNumber, type) {
        const classSelector = type === 'ins' ? 'd2h-ins' : 'd2h-del';
        const lineTd = Array.from(
            document.querySelectorAll(`td.d2h-code-side-linenumber.${classSelector}`)
        ).find(td => td.textContent.trim() === String(rowNumber));

        if (!lineTd) return null;

        const tr = lineTd.closest('tr');
        if (!tr) return null;

        const codeTd = tr.querySelector(`td.${classSelector}:nth-of-type(2)`);
        if (!codeTd) return null;

        const span = codeTd.querySelector('span.d2h-code-line-ctn');
        if (!span) return null;

        return getWrappedLineCount(span);
    }

    // Add empty lines to the inserted or deleted code cell to match target count
    function addEmptyLines(rowNumber, type, countToAdd) {
        const classSelector = type === 'ins' ? 'd2h-ins' : 'd2h-del';

        const lineTd = Array.from(
            document.querySelectorAll(`td.d2h-code-side-linenumber.${classSelector}`)
        ).find(td => td.textContent.trim() === String(rowNumber));

        if (!lineTd) {
            console.log(`No ${type} line with number ${rowNumber} found.`);
            return;
        }

        const tr = lineTd.closest('tr');
        if (!tr) {
            console.log('No parent <tr> found.');
            return;
        }

        const codeTd = tr.querySelector(`td.${classSelector}:nth-of-type(2)`);
        if (!codeTd) {
            console.log(`No code cell found for ${type} at row ${rowNumber}.`);
            return;
        }

        for (let i = 0; i < countToAdd; i++) {
            const emptySpan = document.createElement('span');
            emptySpan.className = 'd2h-code-line-ctn';
            emptySpan.textContent = '\u00A0'; // non-breaking space
            codeTd.appendChild(emptySpan);
        }
        console.log(`Added ${countToAdd} empty lines to ${type} at row ${rowNumber}`);
    }

    // Main function: process all line numbers present in either del or ins and match their heights
    function balanceDeletedInsertedLines() {
        // Collect all unique line numbers from both del and ins
        const delLines = Array.from(document.querySelectorAll('td.d2h-code-side-linenumber.d2h-del.d2h-change'))
            .map(td => td.textContent.trim());

        const insLines = Array.from(document.querySelectorAll('td.d2h-code-side-linenumber.d2h-ins.d2h-change'))
            .map(td => td.textContent.trim());

        // Get unique line numbers combined
        const allLineNumbers = Array.from(new Set([...delLines, ...insLines]));

        allLineNumbers.forEach(lineNumber => {
            const delCount = getLinesForRowNumber(lineNumber, 'del') || 0;
            const insCount = getLinesForRowNumber(lineNumber, 'ins') || 0;

            if (delCount > insCount) {
                addEmptyLines(lineNumber, 'ins', delCount - insCount);
            } else if (insCount > delCount) {
                addEmptyLines(lineNumber, 'del', insCount - delCount);
            } else {
                // counts are equal, no action needed
            }
        });

        console.log('Balancing complete for all lines.');
    }

    // Run after page fully loaded / layout stable
    window.onload = () => {
        balanceDeletedInsertedLines();
    };

    document.querySelectorAll('td.d2h-ins').forEach(td => {
        if (td.classList.length === 1) {
            const span = td.querySelector('span.d2h-code-line-ctn');
            if (span) {
                span.style.backgroundColor = '#97f295'; // darker green
            }
        }
    });
</script>

<style>
    .d2h-cntx,
    .d2h-code-side-line.d2h-code-side-emptyplaceholder {
        background-color: white;
    }

    .d2h-code-side-line {
        padding: 0 1.5em 1em 4.5em !important;
        width: unset !important;
    }

    .d2h-code-line,
    .d2h-code-side-line,
    .d2h-code-line-ctn {
        text-align: justify !important;
        word-wrap: unset !important;
        white-space: unset !important;
        font-size: 13px;
    }

    .d2h-code-side-line {
        display: flex;
        gap: 7px;
    }

    .d2h-code-line del,
    .d2h-code-line ins,
    .d2h-code-side-line del,
    .d2h-code-side-line ins {
        display: initial;
    }

    .d2h-cntx,
    .d2h-code-side-line.d2h-code-side-emptyplaceholder {
        height: auto !important;
    }
</style>

<?php
$this->registerJs("

    function updateEndDateOptions() {
        var selected = $('#audittrail-start_date').val();

        $('#audittrail-end_date option').each(function() {
            if ($(this).val() === selected && selected !== '') {
                $(this).prop('disabled', true);
            } else {
                $(this).prop('disabled', false);
            }
        });
    }
    $('document').ready(function(){
        $('#audittrail-start_date').on('change', updateEndDateOptions);
        updateEndDateOptions(); 
     });");
?>