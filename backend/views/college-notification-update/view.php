<?php

use common\models\CollegeNotificationUpdate;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\widgets\DetailView;
use common\helpers\DataHelper;

/* @var $this yii\web\View */
/* @var $model common\models\CollegeNotificationUpdate */

$this->title = $model->college->display_name;
$this->params['breadcrumbs'][] = ['label' => 'College Notification Updates', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
\yii\web\YiiAsset::register($this);
?>
<div class="college-notification-update-view  box box-primary">
   <div class="box-header">
    <p>
        <?= Html::a('Update', ['update', 'id' => $model->id], ['class' => 'btn btn-primary']) ?>
    </p>

    <?= DetailView::widget([
        'model' => $model,
        'attributes' => [
            [
                'attribute' => 'college_id',
                'value' => function ($model) {

                    return $model->college_id ? $model->college->display_name : '';
                }
            ],
            'text',
            [
                'label' => 'Content',
                'format' => 'raw',
                'value' => html_entity_decode($model->content),
            ],
            [
                'label' => 'Status',
                'attribute' => 'status',
                'value' => ArrayHelper::getValue(DataHelper::getConstantList('STATUS', CollegeNotificationUpdate::class), $model->status)
            ],
            'created_at:datetime',
            'updated_at:datetime',
            'start_date:datetime',
            'end_date:datetime',
            'publish_at:datetime',
        ],
    ]) ?>
   </div>
</div>
