<?php

use common\helpers\CollegeHelper;
use frontend\helpers\Html;
use frontend\helpers\Url;
use kartik\depdrop\DepDrop;
use kartik\select2\Select2;
use yii\web\JsExpression;

$condition = ($type == 'college-notification-update' ? $model->isNewRecord : true);
$examNotificationId = ($type == 'college-notification-update' ? null : $examNotification->id);
?>

<style>
    .chunk-section {
        margin-bottom: 15px;
    }

    .chunk-section .panel-heading {
        background-color: #f5f5f5;
        border-bottom: 1px solid #ddd;
    }

    .chunk-section .panel-title {
        font-size: 16px;
        margin: 0;
    }

    .chunk-section .btn-link {
        text-decoration: none;
        color: #337ab7;
        padding: 0;
        border: none;
        background: none;
        font-weight: bold;
    }

    .chunk-section .btn-link:hover {
        color: #23527c;
        text-decoration: none;
    }

    .chunk-college-checkbox {
        margin-right: 8px;
    }

    .save-chunk-btn {
        margin-top: 10px;
    }

    .select-all-chunk,
    .deselect-all-chunk {
        margin-left: 5px;
    }

    #chunk-loader {
        text-align: center;
        padding: 20px;
        color: #666;
    }

    .alert-info {
        background-color: #d9edf7;
        border-color: #bce8f1;
        color: #31708f;
    }
</style>

<?php if ($condition): ?>
    <!-- Upload Mode Toggle -->
    <div class="form-group">
        <label class="control-label">Upload Mode</label>
        <div class="btn-group btn-group-toggle" data-toggle="buttons">
            <label class="btn btn-outline-primary active" id="single-mode-btn">
                <input type="radio" name="CollegeNotificationUpdate[upload_type]" id="single_mode" value="single" checked> Manual Selection
            </label>
            <label class="btn btn-outline-primary" id="bulk-mode-btn">
                <input type="radio" name="CollegeNotificationUpdate[upload_type]" id="bulk_mode" value="bulk"> Bulk Upload
            </label>
        </div>
    </div>
<?php endif; ?>

<!-- Single Upload Fields -->
<div id="single-upload-fields">
    <?php if ($type == 'exam-notification'): ?>
        <!-- Directly show Chunk Selection for Exam Notifications -->
        <div id="chunk-college-selection">
            <div class="alert alert-info">
                <strong>Chunk Selection:</strong> Large college lists will be divided into chunks of 50 colleges each. Select a chunk to work with.
            </div>

            <input type="hidden" id="sub_page" name="CollegeNotificationUpdate[sub_page]" value="">

            <div id="chunk-loader">
                <div class="text-center">
                    <i class="fa fa-spinner fa-spin"></i> Loading college chunks...
                </div>
            </div>

            <div id="chunk-container" style="display: none;">
                <!-- Chunk selection will be loaded here -->
            </div>
        </div>
    <?php else: ?>
        <!-- Regular form for college-notification-update -->
        <?=
        $form->field($model, 'college_id')->widget(Select2::class, [
            'disabled' => !$model->isNewRecord,
            'data' => $data,
            'options' => [
                'placeholder' => '--Select--',
                'multiple' => false,
            ],
            'pluginOptions' => [
                'allowClear' => true,
                'minimumInputLength' => 3,
                'language' => [
                    'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                ],
                'ajax' => [
                    'url' => [$ajax],
                    'dataType' => 'json',
                    'data' => new JsExpression('function(params) {return {q:params.term,  examNotificationId: ' . (int)$examNotificationId . '}; }'),
                ],
                'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                'templateResult' => new JsExpression('function(data) { return data.text; }'),
                'templateSelection' => new JsExpression('function (data) { return data.text; }'),
            ],
            'pluginEvents' => [
                'change' => 'function(data) {
                        $.post( "' . Url::toRoute('college-content/check-college-page-combination') . '", { college: $(this).val(),page:$("#sub_page").find(":selected").val()}).done(function( data ) { if(data){
                            $(".sub-page-drpdown").show();
                        }else{
                            $(".sub-page-drpdown").hide();
                            $("#parent_id").val("").trigger("change");
                        }});
                    }',
            ]
        ])->label('College Name');
        ?>
    <?php endif; ?>

    <?php if ($type == 'college-notification-update'): ?>
        <!-- Sub Page Selection only for college-notification-update -->
        <?= $form->field($model, 'sub_page')->widget(DepDrop::class, [
            'type' => DepDrop::TYPE_SELECT2,
            'options' => [
                'id' => 'sub_page',
                'placeholder' => '--Select Sub Page--',
                'multiple' => false,
            ],
            'select2Options' => ['pluginOptions' => [
                'allowClear' => true,
                'disabled' => !$model->isNewRecord,
            ]],
            'pluginOptions' => [
                'depends' => ['collegenotificationupdate-college_id'],
                'placeholder' => 'Select Sub Page...',
                'url' => Url::to(['/college-content/all-active-subpage'])
            ],
        ])->label('Sub Page'); ?>
    <?php endif; ?>
</div>


<!-- Bulk Upload Fields -->
<div id="bulk-upload-fields" style="display: none;">

    <div class="card-body">
        <p>Please ensure your CSV file follows the exact format below:</p>

        <div class="table-responsive">
            <table class="table table-bordered table-hover table-sm text-nowrap">
                <thead class="thead-light">
                    <tr>
                        <th>college_id</th>
                        <th>sub_page</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>520</td>
                        <td>info</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="my-3 alert well">
            <strong>College Sub Pages:</strong>
            <?php
            $subPages = array_keys(CollegeHelper::$subPages);
            ?>
            <div>
                <?= Html::encode(implode(', ', $subPages)) ?>
            </div>
            <div style="margin-top: 10px;">
                <b>
                    Important:
                    Maximum 200 records allowed per upload. Files with more than 200 records will be rejected.
                </b>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label">CSV File (College ID & Sub Page)</label>
        <?php if ($type == 'exam-notification'): ?>
            <div class="alert alert-warning" style="margin-bottom: 10px;">
                <strong>Important:</strong>
                <ul style="margin-bottom: 0; padding-left: 20px;">
                    <li>Maximum 200 records allowed per upload. Files with more than 200 records will be rejected.</li>
                    <?php if (isset($examNotification)): ?>
                        <li>Only colleges tagged with the selected
                            <?php if ($examNotification->exam_id && $examNotification->course_id): ?>
                                exam and course combination
                            <?php elseif ($examNotification->course_id): ?>
                                course
                            <?php elseif ($examNotification->exam_id): ?>
                                exam
                            <?php endif; ?>
                            will be processed. Untagged colleges will be marked as failed records.
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        <?php endif; ?>
        <div class="dropzone-wrapper">
            <div class="dropzone-desc">
                <i class="glyphicon glyphicon-download-alt"></i>
                <p>Choose a CSV file or drag it here.</p>
                <p><small>CSV should contain: college_id, sub_page</small></p>
            </div>
            <input type="file" name="bulk_csv" class="dropzone" accept=".csv" />
        </div>
    </div>
</div>

<?php
$jsType = json_encode($type);
$subPagesJson = json_encode(\common\helpers\CollegeHelper::$subPages);
$examNotificationId = $examNotification->id ?? 0;
$script = <<<JS
$(document).ready(function() {
    // Toggle between single and bulk upload modes
    $('input[name="CollegeNotificationUpdate[upload_type]"]').change(function () {
        var mode = $(this).val();
       
        if (mode === 'single') {
            $('#single-upload-fields').show();
            $('#bulk-upload-fields').hide();
            $('#single-mode-btn').addClass('active');
            $('#bulk-mode-btn').removeClass('active');
        } else {
            $('#main-submit-button').show();
            $('#single-upload-fields').hide();
            $('#bulk-upload-fields').show();
            $('#bulk-mode-btn').addClass('active');
            $('#single-mode-btn').removeClass('active');
        }
    });

    // File drop functionality
    $('.dropzone').on('change', function() {
        var fileName = $(this)[0].files[0] ? $(this)[0].files[0].name : '';
        if (fileName) {
            $(this).siblings('.dropzone-desc').html('<i class="glyphicon glyphicon-ok"></i><p>File selected: ' + fileName + '</p>');
        }
    });

    // Drag and drop events
    $('.dropzone-wrapper').on('dragover', function(e) {
        e.preventDefault();
        e.stopPropagation();
        $(this).addClass('dragover');
    });

    $('.dropzone-wrapper').on('dragleave', function(e) {
        e.preventDefault();
        e.stopPropagation();
        $(this).removeClass('dragover');
    });

    $('.dropzone-wrapper').on('drop', function(e) {
        e.preventDefault();
        e.stopPropagation();
        $(this).removeClass('dragover');

        var files = e.originalEvent.dataTransfer.files;
        if (files.length > 0) {
            $(this).find('.dropzone')[0].files = files;
            $(this).find('.dropzone').trigger('change');
        }
    });

    document.getElementById('csvUploadForm').addEventListener('submit', function() {
    const progressBar = document.getElementById('uploadProgressBar');
    const progress = document.getElementById('uploadProgress');
    progressBar.classList.remove('d-none');
    let width = 0;
    const interval = setInterval(function() {
        if (width >= 100) {
            clearInterval(interval);
        } else {
            width += 10;
            progress.style.width = width + '%';
        }
    }, 300);
    });

if ($jsType === 'exam-notification') {
     if($('input[name="CollegeNotificationUpdate[upload_type]"]').val() == 'single') {
        $('#chunk-college-selection').show();
        $('#chunk-selection-btn').addClass('active');
        $('#main-submit-button').hide();
        loadCollegeChunks();
     }

    // Function to load college chunks
    function loadCollegeChunks() {
        $('#chunk-loader').show();
        $('#chunk-container').hide();

        $.ajax({
            url: '../ajax/get-college-chunks',
            type: 'GET',
            data: {
                examNotificationId: {$examNotificationId}
            },
            dataType: 'json',
            success: function (response) {
                $('#chunk-loader').hide();
                if (response.success && response.chunks.length > 0) {
                    renderCollegeChunks(response.chunks);
                    $('#chunk-container').show();
                } else {
                    $('#chunk-container').html('<div class="alert alert-warning">No colleges found for the selected exam/course combination.</div>').show();
                }
            },
            error: function () {
                $('#chunk-loader').hide();
                $('#chunk-container').html('<div class="alert alert-danger">Error loading college chunks. Please try again.</div>').show();
            }
        });
    }

    // Function to render college chunks
    function renderCollegeChunks(chunks) {
        var html = '';

        chunks.forEach(function (chunk, index) {
            html += '<div class="chunk-section" data-chunk="' + index + '" style="border: 1px solid #ddd; margin-bottom: 20px; border-radius: 5px;">';

            // Chunk header - clickable to expand/collapse
            html += '<div class="chunk-header" style="background-color: #f5f5f5; padding: 15px; cursor: pointer; border-bottom: 1px solid #ddd;" data-toggle="collapse" data-target="#chunk-content-' + index + '">';
            html += '<h4 style="margin: 0; display: inline-block;">Chunk ' + (index + 1) + ' (' + chunk.length + ' colleges)</h4>';
            html += '<span class="pull-right"><i class="fa fa-chevron-down"></i></span>';
            html += '</div>';

            // Collapsible content (closed by default)
            html += '<div id="chunk-content-' + index + '" class="collapse" style="padding: 15px;">';

            // Colleges section
            html += '<div class="form-group">';
            html += '<label class="control-label">Colleges:</label>';
            html += '<div style="margin-top: 10px;">';
            html += '<button type="button" class="btn btn-sm btn-primary select-all-chunk" data-chunk="' + index + '" style="margin-right: 10px;">Select All</button>';
            html += '<button type="button" class="btn btn-sm btn-default deselect-all-chunk" data-chunk="' + index + '">Deselect All</button>';
            html += '</div>';
            html += '<div class="row" style="margin-top: 15px;">';

            chunk.forEach(function (college) {
                html += '<div class="col-md-4" style="margin-bottom: 5px;">';
                html += '<label class="checkbox-inline">';
                html += '<input type="checkbox" name="chunk_colleges[' + index + '][]" value="' + college.id + '" class="chunk-college-checkbox" data-chunk="' + index + '" checked> ';
                html += college.name;
                html += '</label>';
                html += '</div>';
            });

            html += '</div>'; // End colleges row
            html += '</div>'; // End colleges form-group

            // Sub-page selection
            html += '<div class="form-group">';
            html += '<label class="control-label">Sub Page:</label>';
            html += '<select id="chunk-sub-page-' + index + '" class="form-control chunk-subpage-select" name="chunk_sub_pages[' + index + '][]" multiple="multiple" data-chunk="' + index + '" style="width: 100%;">';
            html += '</select>';
            html += '<div class="mt-2">';
            html += '<button type="button" class="btn btn-sm btn-primary select-all-subpage" data-chunk="' + index + '">Select All</button>';
            html += '<button type="button" class="btn btn-sm btn-secondary deselect-all-subpage" data-chunk="' + index + '">Clear All</button>';
            html += '</div>';
            html += '</div>';


            // Save button
            html += '<div class="form-group" style="margin-top: 20px;">';
            html += '<button type="submit" class="btn btn-success save-chunk-btn" data-chunk="' + index + '">Create College Notifications</button>';
            html += '</div>';

            html += '</div>'; // End collapsible content
            html += '</div>'; // End chunk-section
        });

        $('#chunk-container').html(html);

        // Initialize sub-page dropdowns for each chunk
        chunks.forEach(function (chunk, index) {
            loadSubPagesForChunk(index);
        });

        // Handle chevron icon rotation for collapsible chunks
        $(document).on('show.bs.collapse', '[id^="chunk-content-"]', function () {
            var chunkId = $(this).attr('id');
            var chunkIndex = chunkId.replace('chunk-content-', '');
            $('.chunk-header[data-target="#' + chunkId + '"] i').removeClass('fa-chevron-down').addClass('fa-chevron-up');
        });

        $(document).on('hide.bs.collapse', '[id^="chunk-content-"]', function () {
            var chunkId = $(this).attr('id');
            var chunkIndex = chunkId.replace('chunk-content-', '');
            $('.chunk-header[data-target="#' + chunkId + '"] i').removeClass('fa-chevron-up').addClass('fa-chevron-down');
        });
    }

    // Function to load sub-pages for a specific chunk based on selected colleges
    function loadSubPagesForChunk(chunkIndex) {
        var selectedColleges = [];

        $('.chunk-college-checkbox[data-chunk="' + chunkIndex + '"]:checked').each(function () {
            selectedColleges.push($(this).val());
        });

        // Load all sub-pages from the helper
        var subPages = $subPagesJson;
        var options = '<option value=""></option>';

        // Add all sub-pages to the dropdown
        for (var key in subPages) {
            if (subPages.hasOwnProperty(key)) {
                options += '<option value="' + key + '">' + key + '</option>';
            }
        }

        $('#chunk-sub-page-' + chunkIndex).html(options).select2({
            placeholder: '--Select Sub Pages--',
            allowClear: true,
            width: 'resolve'
        });
    }

    // Handle select all for chunk
    $(document).on('click', '.select-all-chunk', function () {
        var chunkIndex = $(this).data('chunk');
        $('input[data-chunk="' + chunkIndex + '"]').prop('checked', true);
    });

    // Handle deselect all for chunk
    $(document).on('click', '.deselect-all-chunk', function () {
        var chunkIndex = $(this).data('chunk');
        $('input[data-chunk="' + chunkIndex + '"]').prop('checked', false);
    });

    // Handle chunk save button click
    $(document).on('click', '.save-chunk-btn', function (e) {
        e.preventDefault();
        var chunkIndex = $(this).data('chunk');
        var selectedColleges = [];
        var selectedSubPages = [];

        // Get selected colleges for this chunk
        $('input[data-chunk="' + chunkIndex + '"]:checked').each(function () {
            selectedColleges.push($(this).val());
        });

        // Get selected sub-pages for this chunk
        selectedSubPages = $('#chunk-sub-page-' + chunkIndex).val() || [];

        if (selectedColleges.length === 0) {
            alert('Please select at least one college from this chunk.');
            return;
        }

        if (selectedSubPages.length === 0) {
            alert('Please select at least one sub-page for this chunk.');
            return;
        }

        // Clear previous hidden inputs (in case user clicks multiple times)
        $('#csvUploadForm input[name^="CollegeNotificationUpdate[college_id]"]').remove();
        $('#csvUploadForm input[name^="CollegeNotificationUpdate[sub_page]"]').remove();

        // Append selected colleges as hidden inputs
        selectedColleges.forEach(function (collegeId) {
            $('<input>').attr({
                type: 'hidden',
                name: 'CollegeNotificationUpdate[college_id][]',
                value: collegeId
            }).appendTo('#csvUploadForm');
        });

        // Append selected sub-pages as hidden inputs
        selectedSubPages.forEach(function (subPage) {
            $('<input>').attr({
                type: 'hidden',
                name: 'CollegeNotificationUpdate[sub_page][]',
                value: subPage
            }).appendTo('#csvUploadForm');
        });

        // Add the save_chunk index
        if ($('input[name="save_chunk"]').length) {
            $('input[name="save_chunk"]').val(chunkIndex);
        } else {
            $('<input>').attr({
                type: 'hidden',
                name: 'save_chunk',
                value: chunkIndex
            }).appendTo('#csvUploadForm');
        }

        // Submit the form
        $('#csvUploadForm').submit();
    });

    // Select All Sub Pages
    $(document).on('click', '.select-all-subpage', function () {
        var chunkIndex = $(this).data('chunk');
        var select = $('#chunk-sub-page-' + chunkIndex);
        var allValues = [];

        select.find('option').each(function () {
            if ($(this).val()) {
                allValues.push($(this).val());
            }
        });

        select.val(allValues).trigger('change');
    });

    // Deselect All Sub Pages
    $(document).on('click', '.deselect-all-subpage', function () {
        var chunkIndex = $(this).data('chunk');
        var select = $('#chunk-sub-page-' + chunkIndex);
        select.val(null).trigger('change');
    });
}
});
JS;
$this->registerJs($script);
?>