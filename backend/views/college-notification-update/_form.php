<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;
use kartik\select2\Select2;
use yii\helpers\ArrayHelper;
use yii\web\JsExpression;
use yii\helpers\Url;
use kartik\depdrop\DepDrop;
use common\models\College;
use common\helpers\CollegeHelper;
use common\helpers\DataHelper;
use common\models\CollegeNotificationUpdate;
use kartik\date\DatePicker;
use kartik\datetime\DateTimePicker;

/* @var $this yii\web\View */
/* @var $model common\models\CollegeNotificationUpdate */
/* @var $form yii\widgets\ActiveForm */

$data =  ArrayHelper::map(College::find()->andWhere(['id' => $model->college_id])->all(), 'id', 'name');

?>

<style>
    .dropzone-wrapper {
        border: 2px dashed #91b0b3;
        color: #92b0b3;
        position: relative;
        height: 150px;
        border-radius: 5px;
    }

    .dropzone-desc {
        position: absolute;
        margin: 0 auto;
        left: 0;
        right: 0;
        text-align: center;
        width: 40%;
        top: 50px;
        font-size: 16px;
    }

    .dropzone-desc p {
        margin: 5px 0;
    }

    .dropzone {
        position: absolute;
        top: 0px;
        right: 0;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0;
        cursor: pointer;
    }

    .dropzone-wrapper:hover {
        background: #f8f9fa;
        border-color: #007bff;
    }

    .dropzone-wrapper:hover .dropzone-desc {
        color: #007bff;
    }

    .btn.active {
        box-shadow: inset 0 11px 33px rgba(0, 0, 0, 0.125);
    }
</style>


<div class="college-notification-update-form  box box-primary">
    <div class="box-body">

        <?php
        $form = ActiveForm::begin([
            'options' => ['enctype' => 'multipart/form-data', 'id' => 'csvUploadForm']
        ]); ?>

        <?= $this->render('_manual_bulk_upload_form', [
            'model' => $model,
            'type' => 'college-notification-update',
            'form' => $form,
            'data' => $data,
            'ajax' => '../ajax/college-list'
        ]); ?>

        <!-- Common Fields (always visible) -->
        <?=
        $this->render('/widget/tinymce', [
            'form' => $form,
            'model' => $model,
            'type' => College::ENTITY_COLLEGE,
            'entity' => 'content'
        ])
        ?>
        <?= $form->field($model, 'text')->textInput()->label('Heading Text') ?>

        <?=
        $form->field($model, 'start_date')->widget(DatePicker::class, [
            'value' => '',
            'options' => ['placeholder' => 'Select date ...', 'class' => 'start_date'],
            'type' => DatePicker::TYPE_COMPONENT_APPEND,
            'pluginOptions' => [
                'format' => 'yyyy-mm-dd',
                'minViewMode' => 'month',
                'startDate' => date('Y-m-d'),
                'todayHighlight' => true,
            ],
        ])->label('Start Date');
        ?>
        <?=
        $form->field($model, 'end_date')->widget(DatePicker::class, [
            'value' => '',
            'options' => ['placeholder' => 'Select date ...', 'class' => 'start_date'],
            'type' => DatePicker::TYPE_COMPONENT_APPEND,
            'pluginOptions' => [
                'format' => 'yyyy-mm-dd',
                'minViewMode' => 'month',
                'startDate' => date('Y-m-d'),
                'todayHighlight' => true,
            ],
        ])->label('End Date');
        ?>

        <?= $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', CollegeNotificationUpdate::class)) ?>

        <div class="progress mt-3 d-none" id="uploadProgressBar">
            <div class="progress-bar progress-bar-striped progress-bar-animated bg-success" role="progressbar" style="width: 0%;" id="uploadProgress"></div>
        </div>

        <div class="form-group">
            <?= Html::submitButton('Save', ['class' => 'btn btn-success']) ?>
        </div>

        <?php ActiveForm::end(); ?>
    </div>

</div>