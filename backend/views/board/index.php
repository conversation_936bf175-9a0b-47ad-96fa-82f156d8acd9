<?php

use yii\helpers\Html;
use yii\grid\GridView;
use common\models\Board;
use yii\helpers\ArrayHelper;
use common\helpers\DataHelper;

/* @var $this yii\web\View */
/* @var $searchModel backend\models\BoardSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Boards';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="board-index box box-primary">
    <div class="box-header with-border">
        <?= Html::a('Create Board', ['create'], ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?php // echo $this->render('_search', ['model' => $searchModel]); ?>
        <?= GridView::widget([
            'dataProvider' => $dataProvider,
            'filterModel' => $searchModel,
            'layout' => "{items}\n{summary}\n{pager}",
            'columns' => [
                ['class' => 'yii\grid\SerialColumn'],

                'id',
                'slug',
                'name',
                'display_name',
                [
                    'attribute' => 'lang_code',
                    'value' => function ($model) {
                        if (!empty($model->lang_code)) {
                            return array_search($model->lang_code, DataHelper::$languageCode);
                        }
                    },
                    'filter' => array_flip(DataHelper::$languageCode)
                ],
                [
                    'attribute' => 'type',
                    'value' => function ($model) {
                        return ArrayHelper::getValue(DataHelper::getConstantList('TYPE', Board::class), $model->type);
                    },
                    'filter' => DataHelper::getConstantList('TYPE', Board::class)
                ],
                [
                    'attribute' => 'level',
                    'value' => function ($model) {
                        return ArrayHelper::getValue(DataHelper::getConstantList('LEVEL', Board::class), $model->level);
                    },
                    'filter' => DataHelper::getConstantList('LEVEL', Board::class)
                ],
                [
                    'attribute' => 'status',
                    'value' => function ($model) {
                        return ArrayHelper::getValue(DataHelper::getConstantList('STATUS', Board::class), $model->status);
                    },
                    'filter' => DataHelper::getConstantList('STATUS', Board::class)
                ],
                // 'state_id',
                // 'created_at:datetime',
                // 'updated_at:datetime',

                ['class' => 'yii\grid\ActionColumn', 'template' => '{view} {update}'],
            ],
        ]); ?>
    </div>
</div>
