<?php

use common\helpers\DataHelper;
use yii\helpers\Html;
use yii\grid\GridView;
use yii\widgets\Pjax;
use common\models\CollegeContent;
use common\models\User;
use yii\helpers\ArrayHelper;

/* @var $this yii\web\View */
/* @var $searchModel backend\models\CollegeContentSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'College Contents';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="college-content-index box box-primary">
    <?php /*Pjax::begin();*/ ?>
    <div class="box-header with-border">
        <?= Html::a('Create College Content', ['create'], ['class' => 'btn btn-success btn-flat', 'style' => 'float: right; margin-right: 10px;']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?php // echo $this->render('_search', ['model' => $searchModel]);
        ?>
        <?= GridView::widget([
            'dataProvider' => $dataProvider,
            'filterModel' => $searchModel,
            'layout' => "{items}\n{summary}\n{pager}",
            'columns' => [
                ['class' => 'yii\grid\SerialColumn'],

                'sub_page',

                [
                    'attribute' => 'entity_id',
                    'label' => 'College Name',
                    'value' => function ($m) {
                        return $m->entityName->name ?? '';
                    }
                ],
                [
                    'attribute' => 'college_slug',
                    'label' => 'College Slug',
                    'value' => function ($model) {
                        return $model->college->slug ?? '';
                    }
                ],
                [
                    'attribute' => 'status',
                    'value' => function ($model) {
                        return ArrayHelper::getValue(DataHelper::getConstantList('STATUS', CollegeContent::class), $model->status);
                    },
                    'filter' => DataHelper::getConstantList('STATUS', CollegeContent::class)
                ],

                [
                    'class' => 'yii\grid\ActionColumn','template' => '{view} {update}',
                ],
                [
                    'attribute' => 'preview',
                    'format' => 'raw',
                    'value' => function ($model) {
                        return '<a href="' . Yii::$app->urlManager->createUrl(['/college-content/preview', 'id' => $model->id]) . '" target="_blank">Preview</a>';
                        //return Html::a('Preview', ['preview', 'id' => $model->id], ['target' => '_blank']);
                    },
                ],
            ],
        ]); ?>
    </div>
    <?php /*Pjax::end();*/ ?>
</div>