<?php

use common\helpers\CollegeHelper;
use common\helpers\DataHelper;
use common\models\College;
use common\models\CollegeContent;
use common\models\ContentTemplate;
use common\models\User;
use kartik\select2\Select2;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\web\JsExpression;
use yii\widgets\ActiveForm;
use yii\helpers\Url;
use kartik\depdrop\DepDrop;
use common\models\Course;

/* @var $this yii\web\View */
/* @var $model common\models\CollegeContent */
/* @var $form yii\widgets\ActiveForm */

$model->content = html_entity_decode(stripslashes($model->content));

$data =  ArrayHelper::map(College::find()->andWhere(['id' => $model->entity_id])->all(), 'id', 'name');
if (!empty($model->template_id)) {
    $templateData =  ArrayHelper::map(ContentTemplate::find()->andWhere(['id' => $model->template_id])->all(), 'id', 'name');
}


if (!($model->isNewRecord) && $model->parent_id != null) {
    $subPage = explode('||', $model->sub_page);
    $model->sub_page = $subPage['0'];
    $childSubpage = $subPage['1'];
}

if (!empty($model->author_id)) {
    $user =  ArrayHelper::map(User::find()->where(['id' => $model->author_id])->all(), 'id', 'name');
}
?>

<div class="college-content-form box box-primary">
    <?=
    $this->render('/partials/_user-list');
    ?>
    <?php $form = ActiveForm::begin(); ?>
    <div class="box-body table-responsive">
        <?= Html::activeInput('hidden', $model, 'entity', ['value' => College::ENTITY_COLLEGE]) ?>
        <?= Html::activeInput('hidden', $model, 'category_id', ['value' => 1]) ?>
        <?php /*<div class="col-md-6">
            <?= $form->field($model, 'entity')->dropDownList(CollegeHelper::$allEntities, [
                'disabled' => !$model->isNewRecord,
            ])->label('Category') ?>
        </div>*/ ?>

        <div class="col-md-12">

            <?= $form->field($model, 'entity_id')->widget(Select2::classname(), [
                'disabled' => !$model->isNewRecord,
                'data' => $data, // array of text to show in the tag for the selected items
                'options' => [
                    'placeholder' => '--Select--',
                    'multiple' => false,
                ],
                'pluginOptions' => [
                    'allowClear' => true,
                    'minimumInputLength' => 3,
                    'language' => [
                        'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                    ],
                    'ajax' => [
                        'url' => ['../ajax/college-list'],
                        'dataType' => 'json',
                        'data' => new JsExpression('function(params) {return {q:params.term}; }')
                    ],
                    'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                    'templateResult' => new JsExpression('function(data) { return data.text; }'),
                    'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                ],
                'pluginEvents' => [
                    'change' => 'function(data) { 
                        $.post( "' . Url::toRoute('college-content/check-college-page-combination') . '", { college: $(this).val(),page:$("#sub_page").find(":selected").val()}).done(function( data ) { if(data){
                            $(".sub-page-drpdown").show();
                        }else{
                            $(".sub-page-drpdown").hide();
                            $("#parent_id").val("").trigger("change");
                        }});
        }',
                ]
            ])->label('College Name');
?>
        </div>

        <div class="col-md-12">
            <?= $form->field($model, 'author_id')->widget(Select2::classname(), [
                'data' => $user ?? [], //array of text to show in the field for the selected items
                'options' => [
                    'placeholder' => '--Select--',
                    'options' => [],
                ],
                'pluginOptions' => [
                    'allowClear' => true,
                    'minimumInputLength' => 3,
                    'maximumInputLength' => 10,
                    'language' => [
                        'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                    ],
                    'ajax' => [
                        'url' => ['../ajax/all-users'],
                        'dataType' => 'json',
                        'data' => new JsExpression('function(params) {return {q:params.term}; }')
                    ],
                    'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                    'templateResult' => new JsExpression('function(data) { return data.text; }'),
                    'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                ],
            ])->label('Author');
?>
        </div>

        <div class="col-md-12">
            <?= $form->field($model, 'sub_page')->dropDownList(CollegeHelper::$subPages, [
                'id' => 'sub_page',
                'disabled' => !$model->isNewRecord,
                'onchange' => '$.post( "' . Url::toRoute('college-content/check-college-page-combination') . '", { page: $(this).val(),college:$("#collegecontent-entity_id").find(":selected").val()}).done(function( data ) { if(data){
                    $(".sub-page-drpdown").show();
                }else{
                    $(".sub-page-drpdown").hide();
                    $("#parent_id").val("").trigger("change");
                }})',
            ]) ?>
        </div>
        <div class="col-md-12">
            <?= $form->field($model, 'template_id')->widget(Select2::classname(), [
                'data' => $templateData ?? [], // array of text to show in the tag for the selected items
                'options' => [
                    'placeholder' => '--Select--',
                    'multiple' => false,
                ],
                'pluginOptions' => [
                    'allowClear' => true,
                    'minimumInputLength' => 3,
                    'language' => [
                        'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                    ],
                    'ajax' => [
                        'url' => ['../ajax/template-list'],
                        'dataType' => 'json',
                        'data' => new JsExpression('function(params) {return {q:params.term,sub_page:$("#sub_page").val(),entity:"college"}; }')
                    ],
                    'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                    'templateResult' => new JsExpression('function(data) { return data.text; }'),
                    'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                ]
            ])->label('Content Template');
?>
        </div>
        <!-- to do -->
        <?php if ($model->isNewRecord) { ?>
            <div class="col-md-12 sub-page-drpdown">
                <?= $form->field($model, 'parent_id')->widget(DepDrop::class, [
                    'type' => DepDrop::TYPE_SELECT2,
                    'options' => [
                        'id' => 'parent_id',
                        'placeholder' => '--Select--',
                        'multiple' => false,
                    ],
                    'data' => !empty($model->subPage) ? ArrayHelper::map($model->subPage, 'id', 'name') : [],
                    'select2Options' => ['pluginOptions' => [
                        'allowClear' => true,
                        'disabled' => !$model->isNewRecord ?? (strtolower($model->entity) == 'article') ? true : false
                    ]],
                    'pluginOptions' => [
                        'depends' => ['sub_page'],
                        'placeholder' => 'Select...',
                        'url' => Url::to(['/college-content/get-sub-page'])
                    ],

                ])->label('Sub Page');
                ?>
            </div>
        <?php } elseif ($model->parent_id != null) { ?>
            <div class="col-md-12 sub-page-drpdown">
                <?= $form->field($model, 'parent_id')->widget(DepDrop::class, [
                    'type' => DepDrop::TYPE_SELECT2,
                    'options' => [
                        'id' => 'parent_id',
                        'placeholder' => '--Select--',
                        'multiple' => false,
                    ],
                    'data' => [$model->parent_id => $childSubpage],
                    'select2Options' => ['pluginOptions' => [
                        'allowClear' => true,
                        'disabled' => !$model->isNewRecord ?? (strtolower($model->entity) == 'article') ? true : false
                    ]],
                    'pluginOptions' => [
                        'depends' => ['sub_page'],
                        'placeholder' => 'Select...',
                        'url' => Url::to(['/college-content/get-sub-page'])
                    ],

                ])->label('Sub Page');
                ?>
            </div>
        <?php } ?>



        <div class="col-md-12">
            <?= $form->field($model, 'meta_title')->textInput() ?>
        </div>
        <div class="col-md-12">
            <?= $form->field($model, 'meta_description')->textarea(['maxlength' => 160])->hint('You can add upto 160 characters.') ?>
        </div>
        <div class="col-md-12">
            <?= $form->field($model, 'h1')->textInput() ?>
        </div>

        <div class="col-md-12">
            <?= $form->field($model, 'is_main')
                ->dropDownList(array_reverse(CollegeContent::getIsMainList(), true)) ?>
        </div>

        <div class="col-md-12">
            <?=
            $this->render('/widget/tinymce', [
                'form' => $form,
                'model' => $model,
                'type' => College::ENTITY_COLLEGE,
                'entity' => 'content'
            ])
            ?>
        </div>
        <?php if (Yii::$app->user->can('Seo-Tag-Permission')): ?>
            <div class="col-md-12">
                <?= $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', CollegeContent::class)) ?>
            </div>
        <?php endif; ?>
        <div class="col-md-12">
            <?=
            $this->render('/widget/tinymce', [
                'form' => $form,
                'model' => $model,
                'type' => College::ENTITY_COLLEGE,
                'entity' => 'editor_remark'
            ])
            ?>
        </div>
    </div>
    <div class="box-footer">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success btn-flat', 'style' => 'float: right; margin-right: 10px; width: 8%;']) ?>
    </div>
    <?php ActiveForm::end(); ?>
</div>
<?=
$this->render('../partials/_socket-io-client', [
    'model' => $model,
    'entity' => 'college-content'
])
?>