<?php

use common\helpers\CollegeHelper;
use common\helpers\ContentHelper;
use yii\helpers\Html;
use yii\widgets\DetailView;
use kartik\select2\Select2;
use kartik\depdrop\DepDrop;
use yii\widgets\ActiveForm;
use yii\helpers\ArrayHelper;
use yii\web\JsExpression;
use yii\helpers\Url;
use common\models\College;
use common\models\CollegeNotificationUpdate;

/* @var $this yii\web\View */
/* @var $model common\models\ExamNotification */

$this->title = 'Exam Notification';
$this->params['breadcrumbs'][] = ['label' => 'Exam Notifications', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;

$this->registerCssFile('https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css');
$this->registerJsFile('https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js', [
    'depends' => \yii\web\JqueryAsset::class,
]);
?>
<style>
    .select2-search__field {
        height: 27px !important;
    }

    .college-notification-index {
        float: right;
    }

    .dropzone-wrapper {
        border: 2px dashed #91b0b3;
        color: #92b0b3;
        position: relative;
        height: 150px;
        border-radius: 5px;
    }

    .dropzone-desc {
        position: absolute;
        margin: 0 auto;
        left: 0;
        right: 0;
        text-align: center;
        width: 40%;
        top: 50px;
        font-size: 16px;
    }

    .dropzone-desc p {
        margin: 5px 0;
    }

    .dropzone {
        position: absolute;
        top: 0px;
        right: 0;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0;
        cursor: pointer;
    }

    .dropzone-wrapper:hover {
        background: #f8f9fa;
        border-color: #007bff;
    }

    .dropzone-wrapper:hover .dropzone-desc {
        color: #007bff;
    }
</style>
<div class="exam-notification-view box box-primary">
    <div class="box-header">
        <?= Html::a('Update', ['update', 'id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?= DetailView::widget([
            'model' => $model,
            'attributes' => [
                [
                    'attribute' => 'exam_id',
                    'value' => $model->exam ? $model->exam->name : 'N/A',
                ],
                [
                    'attribute' => 'stream_id',
                    'value' => $model->stream ? $model->stream->name : 'N/A',
                ],
                [
                    'attribute' => 'course_id',
                    'value' => $model->course ? $model->course->name : ($model->course_id ? 'Course ID: ' . $model->course_id : 'Not Selected'),
                ],
                [
                    'attribute' => 'program_id',
                    'value' => $model->program ? $model->program->name : ($model->program_id ? 'Program ID: ' . $model->program_id : 'Not Selected'),
                ],
                'text',
                [
                    'attribute' => 'content',
                    'format' => 'raw',
                    'value' => ContentHelper::removeStyleTag(stripslashes(html_entity_decode($model->content)))
                ],
                [
                    'attribute' => 'status',
                    'value' => $model->status ? 'Active' : 'Inactive',
                ],
                'start_date',
                'end_date',
                'publish_at',
                'created_at:datetime',
                'updated_at:datetime',
            ],
        ]) ?>
    </div>
</div>

<!-- College Notification Form -->
<div class="college-notification-form box box-info">
    <div class="box-header with-border">
        <!-- breadcrumb to naviate to index page of college notification -->
        <?= Html::a('Index', ['college-notification-update/index'], ['class' => 'college-notification-index btn btn-success btn-flat back-button']) ?>

        <h3 class="box-title">Create College Notifications</h3>
        <p class="help-block">Select colleges and sub-pages to create college notifications using this exam notification data.</p>
    </div>
    <div class="box-body">
        <?php
        $collegeNotificationModel = new CollegeNotificationUpdate();
        $form = ActiveForm::begin([
            'action' => ['create-college-notifications', 'id' => $model->id],
            'options' => ['enctype' => 'multipart/form-data', 'id' => 'csvUploadForm']
        ]);
        ?>

        <?= $this->render('/college-notification-update/_manual_bulk_upload_form', [
            'model' => $collegeNotificationModel,
            'type' => 'exam-notification',
            'form' => $form,
            'data' => [],
            'ajax' => '../ajax/exam-notification-colleges',
            'examNotification' => $model,
        ]); ?>

        <div class="form-group" id="main-submit-button">
            <?= Html::submitButton('Create College Notifications', ['class' => 'btn btn-success']) ?>
        </div>

        <?php ActiveForm::end(); ?>
    </div>
</div>