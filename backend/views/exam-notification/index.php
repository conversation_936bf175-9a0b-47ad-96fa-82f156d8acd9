<?php

use common\helpers\DataHelper;
use common\models\ExamNotification;
use yii\helpers\Html;
use yii\grid\GridView;
use yii\helpers\ArrayHelper;

/* @var $this yii\web\View */
/* @var $searchModel backend\models\ExamNotificationSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Exam Notifications';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="exam-notification-index box box-primary">
    <div class="box-header with-border">
        <?= Html::a('Create Exam Notification', ['create'], ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?php // echo $this->render('_search', ['model' => $searchModel]);
        ?>
        <?= GridView::widget([
            'dataProvider' => $dataProvider,
            'filterModel' => $searchModel,
            'layout' => "{items}\n{summary}\n{pager}",
            'columns' => [
                ['class' => 'yii\grid\SerialColumn'],

                [
                    'attribute' => 'exam_id',
                    'value' => function ($model) {
                        return $model->exam->name ?? '';
                    }
                ],

                [
                    'attribute' => 'stream_id',
                    'value' => function ($model) {
                        return $model->stream->name ?? '';
                    }
                ],
                [
                    'attribute' => 'course_id',
                    'value' => function ($model) {
                        return $model->course->name ?? '';
                    }
                ],
                [
                    'attribute' => 'program_id',
                    'value' => function ($model) {
                        return $model->program->name ?? '';
                    }
                ],
                [
                    'attribute' => 'text',
                    'value' => function ($model) {
                        return $model->text ?? '';
                    }
                ],
                [
                    'attribute' => 'status',
                    'value' => function ($model) {
                        return ArrayHelper::getValue(DataHelper::getConstantList('STATUS', ExamNotification::class), $model->status);
                    },
                    'filter' => DataHelper::getConstantList('STATUS', ExamNotification::class)
                ],

                ['class' => 'yii\grid\ActionColumn', 'template' => '{view} {update}'],
            ],
        ]); ?>
    </div>
</div>