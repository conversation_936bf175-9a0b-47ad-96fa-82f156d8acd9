<?php

use yii\helpers\Html;
use yii\grid\GridView;

/* @var $this yii\web\View */
/* @var $searchModel backend\models\ClpCollegeUspSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Clp College Usps';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="clp-college-usp-index box box-primary">
    <div class="box-header with-border">
        <?= Html::a('Create Clp College Usp', ['create'], ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?php // echo $this->render('_search', ['model' => $searchModel]); ?>
        <?= GridView::widget([
            'dataProvider' => $dataProvider,
            'filterModel' => $searchModel,
            'layout' => "{items}\n{summary}\n{pager}",
            'columns' => [
                ['class' => 'yii\grid\SerialColumn'],

                'id',
                'college_id',
                'usp_title:ntext',
                'usp_sub_title:ntext',
                'status',
                // 'created_at',
                // 'updated_at',

                ['class' => 'yii\grid\ActionColumn'],
            ],
        ]); ?>
    </div>
</div>
