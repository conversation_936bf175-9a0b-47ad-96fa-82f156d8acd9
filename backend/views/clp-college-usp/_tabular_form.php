<?php

use common\helpers\DataHelper;
use common\models\ClpCollegeUsp;
use kartik\select2\Select2;
use yii\bootstrap\ActiveForm;
use unclead\multipleinput\TabularInput;
use yii\helpers\Html;
use unclead\multipleinput\TabularColumn;

/* @var $this \yii\web\View */
/* @var $models ClpCollegeUsp[] */

$this->title = 'College Usp';
if (!empty($clp_id)) {
    $this->params['breadcrumbs'][] = ['label' => 'Custom Landing Page', 'url' => ['custom-landing-page/view', 'id' => $clp_id]];
}
$this->params['breadcrumbs'][] = $this->title;

$this->registerCssFile('https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css');
$this->registerJsFile('https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js', [
    'depends' => \yii\web\JqueryAsset::class,
]);

$backgroundColor = empty($collegeId) ? 'background-color: #fff;' : 'rgb(233 207 207 / 30%)';
$pointerEvents = empty($collegeId) ? '' : 'pointer-events: none;';

?>
<style>
    .list-cell__college_id {
        width: 352px !important;
    }

    .form-control-static {
        min-height: 0px;
        padding: 0px;
    }

    table.multiple-input-list.table-renderer tr>td:last-child {
        display: none;
    }

    .flash-message {
        padding: 10px;
        background: #ffe0e0;
        border: 1px solid #ffb3b3;
        color: #d8000c;
        font-weight: bold;
        border-radius: 4px;
        margin-bottom: 10px;
    }

    .select2-selection--single {
        background-color: <?= $backgroundColor ?> !important;
        <?= $pointerEvents ?>
    }
</style>

<div class="exam-date-form box box-primary">
    <?php $form = ActiveForm::begin([
        'id' => 'tabular-form',
        'options' => ['enctype' => 'multipart/form-data']
    ]) ?>

    <div id="flash-message-container"></div>
    <div class="box-body">
        <?= TabularInput::widget([
            'models' => $models,
            'modelClass' => ClpCollegeUsp::class,
            'form' => $form,
            'min' => 0,
            'max' => 10,
            'addButtonPosition' => [TabularInput::POS_HEADER],
            'allowEmptyList' => false,
            'columns' => [
                [
                    'name' => 'id',
                    'type' => TabularColumn::TYPE_HIDDEN_INPUT,
                ],
                [
                    'name' => 'college_id',
                    'title' => 'College',
                    'type' => TabularColumn::TYPE_TEXT_INPUT,
                    'options' => function ($model) use ($collegeId, $collegeName) {
                        $value = $model->college_id ?: $collegeId;
                        $text = $model->college->name ?? $collegeName;

                        return [
                            'class' => 'form-control college-autocomplete',
                            'data-init-text' => $text,
                            'data-index' => 0,
                            'value' => $value,
                            'autocomplete' => 'off',
                            'placeholder' => 'Search for a college...',
                            'readonly' => true,
                        ];
                    }
                ],
                [
                    'name' => 'usp_title',
                    'title' => 'Title',
                    'type' => TabularColumn::TYPE_TEXT_INPUT,
                ],
                [
                    'name' => 'usp_sub_title',
                    'title' => 'Sub Title',
                    'type' => TabularColumn::TYPE_TEXT_INPUT,
                ],
                [
                    'name'  => 'status',
                    'title' => 'Status',
                    'type' => TabularColumn::TYPE_DROPDOWN,
                    'items' => DataHelper::getConstantList('STATUS', ClpCollegeUsp::class),
                ],
            ]
        ]) ?>
    </div>

    <div class="box-footer">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success btn-flat']) ?>
    </div>

    <?php ActiveForm::end(); ?>
</div>

<?php
$js = <<<JS

function initializeSelect2(context) {
    context.find('.college-autocomplete').each(function() {
        if ($(this).hasClass("select2-hidden-accessible")) return;

        var \$input = $(this);
        var index = \$input.data('index');

        // Create hidden input to store actual college_id
        var hiddenInputName = 'ClpCollegeUsp[' + index + '][college_id]';
        var hiddenInputSelector = 'input[name="' + hiddenInputName + '"]';

        if (!\$input.siblings(hiddenInputSelector).length) {
            $('<input>', {
                type: 'hidden',
                name: hiddenInputName,
                class: 'college-hidden-input',
                'data-index': index,
                value: \$input.val()
            }).insertAfter(\$input);
        }

        var hiddenInput = \$input.siblings(hiddenInputSelector);

        \$input.select2({
            placeholder: 'Search for a college...',
            minimumInputLength: 2,
            ajax: {
                url: '/ajax/college-list',
                dataType: 'json',
                delay: 250,
                data: function (params) {
                    return { q: params.term };
                },
                processResults: function (data) {
                    return { results: data.results };
                },
            },
            templateResult: function (data) { return data.text; },
            templateSelection: function (data) { return data.text || ''; }
        }).on('select2:select', function (e) {
            var selected = e.params.data;
            hiddenInput.val(selected.id); // Store selected value in hidden input
           
            // Clear previous option (prevent duplicates)
            \$input.find('option').remove();

            // Add new one
            var option = new Option(selected.text, selected.id, true, true);
            \$input.append(option).trigger('change');
        }).on('select2:clear', function () {
            hiddenInput.val('');
        });

        // Pre-fill with data-init-text and value
        var initText = \$input.data('init-text');
        var initVal = \$input.val();

        if (initVal && initText) {
            var option = new Option(initText, initVal, true, true);
            \$input.append(option).trigger('change');
        }
    });
}

$(document).ready(function() {
    initializeSelect2($(document));
});

$('.js-input-plus').on('click', function(e) {
    var lastIndex = $('.college-autocomplete').length - 1;
    var lastTitle = $('#clpcollegeusp-' + lastIndex + '-usp_title').val();
    var lastCollege = $('input[name="ClpCollegeUsp[' + 0 + '][college_id]"]').val();

    $('#flash-message-container').empty();

    if(lastCollege) {
        var lastRowCollegeSelect = $('input[name="ClpCollegeUsp[' + lastIndex + '][college_id]"]');
        lastRowCollegeSelect.prop('readonly', true);
        $('.select2-selection--single').attr('style', 'background-color: rgb(233 207 207 / 30%) !important;');
    }

    if (!lastCollege) {
        $('#flash-message-container').html('<div class="flash-message">Please select a college for row #' + (lastIndex + 1) + '</div>');
        e.stopImmediatePropagation();
        return false;
    }

    if (!lastTitle) {
        $('#flash-message-container').html('<div class="flash-message">Please enter a title for row #' + (lastIndex + 1) + '</div>');
        e.stopImmediatePropagation();
        return false;
    }
});

$('#tabular-form').on('afterAddRow', function(e, row, index) {
    initializeSelect2(row);

    var nextIndex = index;

    var newVisibleInput = row.find('input.college-autocomplete');
    var newHiddenInput = row.find('input.college-hidden-input');

    newVisibleInput.attr('data-index', nextIndex);
    newHiddenInput.attr('data-index', nextIndex);
    newHiddenInput.attr('name', 'ClpCollegeUsp[' + nextIndex + '][college_id]');
    newVisibleInput.attr('name', 'ClpCollegeUsp[' + nextIndex + '][college_id]');

    // Pre-fill new college with first row value if present
    let firstHidden = $('input[name="ClpCollegeUsp[0][college_id]"]').val();
    let firstText = $('#clpcollegeusp-0-college_id').select2('data')[0]?.text || '';

    if (firstHidden && firstText) {
        let input = row.find('.college-autocomplete');
        let hidden = row.find('.college-hidden-input');

        let option = new Option(firstText, firstHidden, true, true);
        input.append(option).trigger('change').prop('disabled', true);
        hidden.val(firstHidden);
    }
});
JS;

$this->registerJs($js);
?>