<?php

use common\helpers\DataHelper;
use common\models\ExamContent;
use common\models\Exam;
use common\models\User;
use common\services\UserService;
use kartik\select2\Select2;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\widgets\ActiveForm;
use kartik\depdrop\DepDrop;
use yii\helpers\Url;
use yii\web\JsExpression;

/* @var $this yii\web\View */
/* @var $model common\models\ExamContent */
/* @var $seoModel common\models\SeoInfo */
/* @var $form yii\widgets\ActiveForm */
/*if (!empty($model->author_id)) {
    $user =  ArrayHelper::map(User::find()->where(['entity_id' => $model->author_id])->all(), 'entity_id', 'name');
}*/
?>

<?php $model->content = html_entity_decode($model->content); ?>

<div class="exam-content-form box box-primary">
    <?=
    $this->render('/partials/_user-list');
    ?>
    <?php $form = ActiveForm::begin(); ?>
    <div class="box-body table-responsive">

    <div class="col-md-6">
        <?php echo $form->field($model, 'is_freelancer')->dropDownList([ExamContent::IS_FREELANCER_NO => 'No', ExamContent::IS_FREELANCER_YES => 'Yes'], ['options' => [$model->is_freelancer => ['selected' => true]]]); ?>
    </div>
    <div class="col-md-6">
        <?= $form->field($model, 'lang_code')->dropDownList(
            array_flip(DataHelper::$languageCode)
        )->label('Language code')
        
?>
    </div>
        <?php /*$form->field($model, 'author_id')->widget(Select2::classname(), [
            'data' => $user ?? [], //array of text to show in the field for the selected items
            'options' => [
                'placeholder' => '--Select--',
                'options' => [],
            ],
            'pluginOptions' => [
                'allowClear' => true,
                'minimumInputLength' => 3,
                'maximumInputLength' => 10,
                'language' => [
                    'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                ],
                'ajax' => [
                    'url' => ['../ajax/all-users'],
                    'dataType' => 'json',
                    'data' => new JsExpression('function(params) {return {q:params.term}; }')
                ],
                'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                'templateResult' => new JsExpression('function(data) { return data.text; }'),
                'templateSelection' => new JsExpression('function (data) { return data.text; }'),
            ],
            ])->label('Author');
*/ ?>

        <?= $form->field($model, 'author_id')->widget(Select2::class, [
            'data' => ArrayHelper::map(User::find()->all(), 'id', 'name'),
            'language' => 'en',
            'options' => ['placeholder' => '--Select Author--'],
            'pluginOptions' => [
                'allowClear' => true
            ],
        ])->label('Author');
?>
        <?= $form->field($model, 'slug')->dropDownList(DataHelper::examContentList(), [
            'id' => 'sub_page', 'prompt' => '-- Writing Content For --', 'disabled' => !$model->isNewRecord,
            'onchange' => '$.post( "' . Url::toRoute('exam-content/check-college-page-combination') . '", { page: $(this).val(),exam:' . $model->exam_id . '}).done(function( data ) { if(data){
            $(".sub-page-drpdown").show();
        }else{
            $(".sub-page-drpdown").hide();
            $("#parent_id").val("").trigger("change");
        }})'
        ])->label('Sub Page') ?>

        <?php if ($model->isNewRecord) { ?>
            <div class="sub-page-drpdown">
                <?= $form->field($model, 'parent_id')->widget(DepDrop::class, [
                    'type' => DepDrop::TYPE_SELECT2,
                    'options' => [
                        'id' => 'parent_id',
                        'placeholder' => '--Select--',
                        'multiple' => false,
                    ],
                    'data' => [],
                    'select2Options' => ['pluginOptions' => [
                        'allowClear' => true,
                        'disabled' => !$model->isNewRecord
                    ]],
                    'pluginOptions' => [
                        'depends' => ['sub_page'],
                        'placeholder' => 'Select...',
                        'url' => Url::to(['/exam-content/get-sub-page'])
                    ],

                ])->label('Dropdown Sub Page');
                ?>
            </div>
        <?php } elseif ($model->parent_id != null) { ?>
            <div class="sub-page-drpdown">
                <?= $form->field($model, 'parent_id')->widget(DepDrop::class, [
                    'type' => DepDrop::TYPE_SELECT2,
                    'options' => [
                        'id' => 'parent_id',
                        'placeholder' => '--Select--',
                        'multiple' => false,
                    ],
                    'data' => [$model->parent_id => $model->name],
                    'select2Options' => ['pluginOptions' => [
                        'allowClear' => true,
                        'disabled' => !$model->isNewRecord
                    ]],
                    'pluginOptions' => [
                        'depends' => ['sub_page'],
                        'placeholder' => 'Select...',
                        'url' => Url::to(['/exam-content/get-sub-page'])
                    ],

                ])->label('Dropdown Sub Page');
                ?>
            </div>
        <?php } ?>


        <?=
        $this->render('/widget/tinymce', [
            'form' => $form,
            'model' => $model,
            'entity' => 'content',
            'type' => Exam::ENTITY_EXAM,
        ])
        ?>

        <div class="col-md-6">
            <?= $form->field($model, 'published_at')->textInput(['value' => !empty($model->published_at) ? Yii::$app->formatter->asDatetime($model->published_at, 'php:F j, Y H:i:s') : '', 'disabled' => true]); ?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'localize_year')->dropDownList(array_combine(range(date('Y') - 5, date('Y') + 5), range(date('Y') - 5, date('Y') + 5)), ['prompt' => '-- select year --']); ?>
        </div>

        <?php if (UserService::hasSeoRole()) {
            echo $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', ExamContent::class));
        } ?>

        <div class="well well-sm no-shadow">
            <h4 class="timeline-header">SEO Info</h4>

            <?= $form->field($seoModel, 'h1')->textInput() ?>

            <?= $form->field($seoModel, 'title')->textInput() ?>

            <?= $form->field($seoModel, 'description')->textarea(['maxlength' => 300])->hint('You can add upto 300 characters.') ?>
        </div>
        <?= $this->render('/widget/tinymce', [
            'form' => $form,
            'model' => $model,
            'entity' => 'editor_remark',
            'type' => Exam::ENTITY_EXAM,
        ])
?>
    </div>
    <div class="box-footer">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <?php ActiveForm::end(); ?>
</div>
<?=
$this->render('../partials/_socket-io-client', [
    'model' => $model,
    'entity' => 'exam-content'
])
?>