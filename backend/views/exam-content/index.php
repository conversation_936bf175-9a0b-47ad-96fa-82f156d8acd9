<?php

use common\helpers\DataHelper;
use common\models\ExamContent;
use common\models\User;
use kartik\date\DatePicker;
use yii\helpers\Html;
use yii\grid\GridView;
use yii\helpers\ArrayHelper;

/* @var $this yii\web\View */
/* @var $searchModel backend\models\ExamContentSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Exam Contents';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="exam-content-index box box-primary">
    <div class="box-header with-border">
        <!-- <?= Html::a('Create Exam Content', ['create'], ['class' => 'btn btn-success btn-flat']) ?> -->
    </div>
    <div class="box-body table-responsive no-padding">
        <?php // echo $this->render('_search', ['model' => $searchModel]);
        // dd($searchModel->attributes())
        ?>
        <?= GridView::widget([
            'dataProvider' => $dataProvider,
            'filterModel' => $searchModel,
            'layout' => "{items}\n{summary}\n{pager}",
            'columns' => [
                ['class' => 'yii\grid\SerialColumn'],
                'id',
                'exam' => [
                    'attribute' => 'examname',
                    'value' => 'exam.display_name',
                    'label' => 'Exam Name'
                ],
                'author_id' => [
                    'attribute' => 'author_id',
                    'value' => function ($model) {
                        return $model->user->name ?? '';
                    },
                    'label' => 'Author Name'
                ],
                'slug' => [
                    'attribute' => 'slug',
                    'value' => 'exam.slug',
                    'label' => 'Exam Slug'
                ],
                'name' => [
                    'attribute' => 'name',
                    'value' => 'name',
                    'label' => 'Subpage'
                ],
                [
                    'attribute' => 'status',
                    'value' => function ($model) {
                        return ArrayHelper::getValue(DataHelper::getConstantList('STATUS', ExamContent::class), $model->status);
                    },
                    'filter' => DataHelper::getConstantList('STATUS', ExamContent::class),
                ],
                [
                    'attribute' => 'created_at',
                    'format' => 'date',
                    'filter' => DatePicker::widget([
                        'model' => $searchModel,
                        'attribute' => 'created_at',
                        'type' => DatePicker::TYPE_COMPONENT_APPEND,
                        'pluginOptions' => [
                            'format' => 'yyyy-mm-dd',
                            'showMeridian' => true,
                            'todayBtn' => true,
                            'endDate' => '0d',
                        ]
                    ]),
                ],
                [
                    'attribute' => 'published_at',
                    'format' => 'date',
                    'filter' => DatePicker::widget([
                        'model' => $searchModel,
                        'attribute' => 'published_at',
                        'type' => DatePicker::TYPE_COMPONENT_APPEND,
                        'pluginOptions' => [
                            'format' => 'yyyy-mm-dd',
                            'showMeridian' => true,
                            'todayBtn' => true,
                            'endDate' => '0d',
                        ]
                    ]),
                ],
                [
                    'attribute' => 'updated_at',
                    'format' => 'date',
                    'filter' => DatePicker::widget([
                        'model' => $searchModel,
                        'attribute' => 'updated_at',
                        'type' => DatePicker::TYPE_COMPONENT_APPEND,
                        'pluginOptions' => [
                            'format' => 'yyyy-mm-dd',
                            'showMeridian' => true,
                            'todayBtn' => true,
                            'endDate' => '0d',
                        ]
                    ]),
                ],
                'updated_by' => [
                    'attribute' => 'updated_by',
                    'value' => function ($model) {
                        if (!empty($model->updated_by)) {
                            $user =  User::find()->select('name')->where(['id' => $model->updated_by])->one();
                            return $user->name ?? '';
                        } else {
                            return '-';
                        }
                    },
                    'label' => 'Updated By'
                ],
                [
                    'class' => 'yii\grid\ActionColumn','template' => '{view} {update}',
                    // 'buttons' => [
                    //     'preview' => function ($url) {
                    //         return Html::a('<span class="glyphicon glyphicon-check"></span>', $url, ['title' => 'Preview', 'target' => 'blank']);
                    //     },
                ],
                [
                    'attribute' => 'preview',
                    'format' => 'raw',
                    'value' => function ($model) {
                        return Html::a('Preview', ['preview', 'id' => $model->id], ['target' => '_blank']);
                    },
                ],
            ],
        ]); ?>
    </div>
</div>