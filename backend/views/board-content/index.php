<?php

use common\helpers\DataHelper;
use common\models\BoardContent;
use common\models\Faq;
use yii\helpers\Html;
use yii\grid\GridView;
use yii\helpers\ArrayHelper;

/* @var $this yii\web\View */
/* @var $searchModel backend\models\BoardContentSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Board Contents';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="board-content-index box box-primary">
    <div class="box-header with-border">
        <?= Html::a('Create Board Content', ['create'], ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?php // echo $this->render('_search', ['model' => $searchModel]);
        ?>
        <?= GridView::widget([
            'dataProvider' => $dataProvider,
            'filterModel' => $searchModel,
            'layout' => "{items}\n{summary}\n{pager}",
            'columns' => [

                [
                    'attribute'=>'id',
                    'filter' => false,
                    'label' => 'Board Content ID',
                    'value' => function ($model) {
                        return $model->id;
                    }
                    ],
                [
                    'attribute' => 'board_name',
                    'label' => 'Board',
                    'value' => function ($m) {
                        return !empty($m->board->display_name) ? $m->board->display_name : '';
                    }
                ],
                [
                    'attribute' => 'page',
                    'value' => 'page',
                    'filter' => ArrayHelper::map(BoardContent::find()->asArray()->all(), 'page', 'page'),
                ],
                // 'page_slug',
                'h1',
                [
                    'attribute' => 'lang_code',
                    'value' => function ($model) {
                        if (!empty($model->lang_code)) {
                            return array_search($model->lang_code, DataHelper::$languageCode);
                        }
                    },
                    'filter' => array_flip(DataHelper::$languageCode)
                ],

                // 'author_id',
                // 'meta_title',
                // 'meta_description',
                // 'content:ntext',
                // 'cover_image',
                [
                    'attribute' => 'status',
                    'value' => function ($model) {
                        return ArrayHelper::getValue(DataHelper::getConstantList('STATUS', BoardContent::class), $model->status);
                    },
                    'filter' => DataHelper::getConstantList('STATUS', BoardContent::class)
                ],
                [
                    'class' => 'yii\grid\ActionColumn','template' => '{view} {update}',
                    /*'faq' => function ($url, $model, $key) {
                        $faqId = Faq::find()->select('id')->where(['entity_id' => $model->board_id])
                            ->andWhere(['entity' => 'board'])->andWhere(['sub_page' => ])->one();
                        if (!empty($faqId)) {
                            $url = '/faq/update?id=' . $faqId->id;
                        } else {
                            $url = '/faq/create';
                        }
                        return Html::a('FAQ', $url, [
                            'title' => Yii::t('app', 'FAQ'),
                            'class' => 'btn btn-sm btn-primary',
                            'style' => 'margin-left:12px',
                            'target' => '_blank'
                        ]);
                    },*/
                    // 'buttons' => [
                    //     'preview' => function ($url) {
                    //         return Html::a('<span class="glyphicon glyphicon-check"></span>', $url, ['title' => 'Preview']);
                    //     },
                    // ],
                ],
                [
                    'attribute' => 'preview',
                    'value' => function ($model) {
                        return Html::a('Preview', ['preview', 'id' => $model->id], ['target' => '_blank']);
                    },
                    'format' => 'raw',
                ],
            ],
        ]); ?>
    </div>
</div>