<?php

use common\helpers\BoardHelper;
use common\helpers\ContentHelper;
use common\helpers\DataHelper;
use common\models\Board;
use common\models\BoardContent;
use common\models\BoardPages;
use common\models\User;
use common\services\UserService;
use kartik\select2\Select2;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\widgets\ActiveForm;
use yii\helpers\Url;
use yii\web\JsExpression;

/* @var $this yii\web\View */
/* @var $model common\models\BoardContent */
/* @var $form yii\widgets\ActiveForm */

$model->content = ContentHelper::removeStyleTag(stripslashes(html_entity_decode($model->content)));
if (!empty($model->author_id)) {
    $user =  ArrayHelper::map(User::find()->where(['id' => $model->author_id])->all(), 'id', 'name');
}
$allBoardSubpages =  ArrayHelper::map(BoardPages::find()->where(['status'=>BoardPages::STATUS_ACTIVE])->andWhere(['is', 'parent_id', new \yii\db\Expression('null')])->all(), 'slug', 'name');
?>

<div class="board-content-form box box-primary">
    <?=
    $this->render('/partials/_user-list');
    ?>
    <?php $form = ActiveForm::begin(); ?>
    <div class="box-body table-responsive">
        <?= $form->field($model, 'lang_code')->dropDownList(
            array_flip(DataHelper::$languageCode),
            [
                'id' => 'lang-code-dropdown',
            ]
        )->label('Language code') ?>

        <?php if ($model->isNewRecord): ?>
            <?= $form->field($model, 'board_id')->widget(Select2::class, [
                'data' => [],
                'language' => 'en',
                'options' => ['placeholder' => '--Select Board--'],
                'pluginOptions' => [
                    'allowClear' => true,
                    'ajax' => [
                        'url' => Url::to(['/board-content/board-list-by-lang']),
                        'dataType' => 'json',
                        'data' => new JsExpression("function(params) {
                            var lang_code = $('#lang-code-dropdown').val();
                            return {lang_code:lang_code, q:params.term}; }")
                    ],
                ],
                'pluginEvents' => [
                    'change' => 'function(data) {
                        $.post( "' . Url::toRoute('board-content/check-board-page-combination') . '", { board: $(this).val(),page:$("#boardcontent-page").find(":selected").val()}).done(function( data ) { if(data){
                            if($.inArray(("#boardcontent-page").find(":selected").val(), ["Syllabus", "Notes", "Answer Key","Question Paper","Books","Deleted Syllabus"]) != -1)
                            {
                                $("#supplementary-sub-page-dropdown").find("option:selected").prop("selected",false);
                                $(".supplementary-drop-down").hide()
                                $(".subject-drop-down").show()
                            }else{
                                $("#syllabus-sub-page-dropdown").find("option:selected").prop("selected",false);
                                $(".subject-drop-down").hide()
                                $(".supplementary-drop-down").show()
                            }
                        }else{
                            $("#syllabus-sub-page-dropdown").find("option:selected").prop("selected",false);
                            $(".subject-drop-down").hide()
                            $("#supplementary-sub-page-dropdown").find("option:selected").prop("selected",false);
                            $(".supplementary-drop-down").hide()
                        }});
        }',
                ]
            ])->label('Board');
            ?>
        <?php endif; ?>

        <?= $form->field($model, 'author_id')->widget(Select2::class, [
            'data' => $user ?? [], //array of text to show in the field for the selected items
            'options' => [
                'placeholder' => '--Select--',
                'options' => [],
            ],
            'pluginOptions' => [
                'allowClear' => true,
                'minimumInputLength' => 0,
                'maximumInputLength' => 10,
                'language' => [
                    'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                ],
                'ajax' => [
                    'url' => ['../ajax/all-users'],
                    'dataType' => 'json',
                    'data' => new JsExpression('function(params) {return {q:params.term}; }')
                ],
                'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                'templateResult' => new JsExpression('function(data) { return data.text; }'),
                'templateSelection' => new JsExpression('function (data) { return data.text; }'),
            ],
        ])->label('Author');
?>

        <?php echo $form->field($model, 'is_freelancer')->dropDownList([BoardContent::IS_FREELANCER_NO => 'No', BoardContent::IS_FREELANCER_YES => 'Yes'], ['options' => [$model->is_freelancer => ['selected' => true]]]); ?>
        <input type="hidden" value="<?= $model->is_freelancer; ?>" name="update-validation" id="update-validation">

        <?php if ($model->isNewRecord): ?>
            <?= $form->field($model, 'page')->widget(Select2::class, [
                'data' => $allBoardSubpages,
                'language' => 'en',
                'options' => [
                    'placeholder' => '--Select Page--',
                    'id' => 'boardcontent-page'
                ],
                'pluginOptions' => [
                    'allowClear' => true
                ],
                'pluginEvents' => [
                    'change' => 'function(data) {
                        $.post( "' . Url::toRoute('board-content/check-board-page-combination') . '", { page: $(this).val(),board:$("#boardcontent-board_id").find(":selected").val()}).done(function( data ) { if(data){
                            if(data){
                                console.log(data);
                                $("#supplementary-sub-page-dropdown").find("option:selected").prop("selected",false);
                                $(".supplementary-drop-down").hide()
                                $(".subject-drop-down").show()
                            }else{
                                $("#syllabus-sub-page-dropdown").find("option:selected").prop("selected",false);
                                $(".subject-drop-down").hide()
                                $(".supplementary-drop-down").show()
                            }
                        }else{
                            $("#syllabus-sub-page-dropdown").find("option:selected").prop("selected",false);
                            $(".subject-drop-down").hide()
                            $("#supplementary-sub-page-dropdown").find("option:selected").prop("selected",false);
                            $(".supplementary-drop-down").hide()
                        }});
                    }',
                ]
            ]) ?>
        <?php else: ?>
            <?= $form->field($model, 'page_slug')->widget(Select2::class, [
                'data' => $allBoardSubpages,
                'language' => 'en',
                'options' => [
                    'placeholder' => '-- Sub Page Content --',
                    'disabled' => !$model->isNewRecord
                ],
                'pluginOptions' => [
                    'allowClear' => true
                ]
            ])->label('Sub Page') ?>
        <?php endif; ?>

        <?php if (!$model->isNewRecord) {
            $allowed = ['syllabus', 'notes', 'answer-key', 'question-paper', 'books', 'deleted-syllabus'];
            if (in_array($model->page_slug, $allowed)) { ?>
                <div class="form-group subject-drop-down field-boardcontent-drop-down">
                    <label class="control-label">Sub Page</label>
                    <?php
                    echo Select2::widget([
                        'name' => 'BoardContent[drop_down_subject]',
                        'value' => $model->page,
                        'data' => \yii\helpers\ArrayHelper::map(BoardHelper::$syllabusSubPagesDropDown, 'name', 'value'),
                        'options' => [
                            'placeholder' => Yii::t('backend', 'Select Subjects'),
                            'id' => 'syllabus-sub-page-dropdown',
                            'disabled' => !$model->isNewRecord
                        ],
                        'pluginOptions' => [
                            'allowClear' => true
                        ]
                    ]);
                    ?>
                </div>
            <?php }
        } else { ?>
            <div class="form-group subject-drop-down field-boardcontent-drop-down">
                <label class="control-label">Subject Sub Page</label>
                <?php
                echo Select2::widget([
                    'name' => 'BoardContent[drop_down_subject]',
                    'value' => null,
                    'data' => \yii\helpers\ArrayHelper::map(BoardHelper::$syllabusSubPagesDropDown, 'name', 'value'),
                    'options' => [
                        'placeholder' => Yii::t('backend', 'Select Subjects'),
                        'id' => 'syllabus-sub-page-dropdown'
                    ],
                    'pluginOptions' => [
                        'allowClear' => true
                    ]
                ]);
                ?>
            </div>

        <?php } ?>

        <?php if (!$model->isNewRecord) {
            if ($model->page_slug == 'supplementary') { ?>
                <div class="form-group supplementary-drop-down field-boardcontent-drop-down">
                    <label class="control-label">Supplementary Sub Page</label>
                    <?php
                    echo Select2::widget([
                        'name' => 'BoardContent[drop_down_supplementary]',
                        'value' => $model->page,
                        'data' => \yii\helpers\ArrayHelper::map(BoardHelper::$supplementarySubPagesDropDown, 'name', 'value'),
                        'options' => [
                            'placeholder' => Yii::t('backend', 'Select Supplementary'),
                            'id' => 'supplementary-sub-page-dropdown',
                            'disabled' => !$model->isNewRecord
                        ],
                        'pluginOptions' => [
                            'allowClear' => true
                        ]
                    ]);
                    ?>
                </div>
            <?php }
        } else { ?>
            <div class="form-group supplementary-drop-down field-boardcontent-drop-down">
                <label class="control-label">Supplementary Sub Page</label>
                <?php
                echo Select2::widget([
                    'name' => 'BoardContent[drop_down_supplementary]',
                    'value' => null,
                    'data' => \yii\helpers\ArrayHelper::map(BoardHelper::$supplementarySubPagesDropDown, 'name', 'value'),
                    'options' => [
                        'placeholder' => Yii::t('backend', 'Select Supplementary'),
                        'id' => 'supplementary-sub-page-dropdown'
                    ],
                    'pluginOptions' => [
                        'allowClear' => true
                    ]
                ]);
                ?>
            </div>
        <?php } ?>
        <?= $form->field($model, 'h1')->textInput(['maxlength' => true]) ?>

        <?= $form->field($model, 'meta_title')->textInput(['maxlength' => true]) ?>

        <?= $form->field($model, 'meta_description')->textarea(['maxlength' => 350])->hint('You can add upto 350 characters.') ?>

        <?= $form->field($model, 'short_description')->textarea(['maxlength' => 300])->hint('You can add upto 300 characters.') ?>

        <?php
        if (!empty($model->cover_image)) {
            echo Html::img(\Yii::getAlias('@gmuAzerUploadBackend/') . $model->cover_image, ['width' => '100', 'height' => '100', 'style' => 'margin:10px']); ?>
        <?php } ?>

        <?= $form->field($model, 'cover_image')->fileInput(); ?>

        <?=
        $this->render('/widget/tinymce', [
            'form' => $form,
            'model' => $model,
            'entity' => 'content',
            'type' => 'board'
        ])
        ?>

        <?php if (UserService::hasSeoRole()) { ?>
            <?= $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', BoardContent::class)) ?>
        <?php } ?>
        <?=
        $this->render('/widget/tinymce', [
            'form' => $form,
            'model' => $model,
            'entity' => 'editor_remark',
            'type' => 'board'
        ])
        ?>

    </div>
    <div class="box-footer">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <?php ActiveForm::end(); ?>
</div>
<?=
$this->render('../partials/_socket-io-client', [
    'model' => $model,
    'entity' => 'board-content'
])
?>