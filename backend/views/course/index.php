<?php

use common\helpers\DataHelper;
use common\models\Course;
use yii\bootstrap\ButtonDropdown;
use yii\helpers\Html;
use yii\grid\GridView;
use yii\helpers\ArrayHelper;

/* @var $this yii\web\View */
/* @var $searchModel backend\models\CourseSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Courses';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="course-index box box-primary">
    <div class="box-header with-border">
        <?= Html::a('Create', ['create'], ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?php // echo $this->render('_search', ['model' => $searchModel]); ?>
        <?= GridView::widget([
            'dataProvider' => $dataProvider,
            'filterModel' => $searchModel,
            'layout' => "{items}\n{summary}\n{pager}",
            'columns' => [
                ['class' => 'yii\grid\SerialColumn'],

                'id',
                [
                    'attribute' => 'parent_id',
                    'value' => function ($model) {
                        $id = $model->parent_id;
                        return Course::findOne($id)->name ?? '';
                    },
                ],
                [
                    'attribute' => 'stream_id',
                    'label' => 'Stream',
                    'value' => 'stream.name'
                ],
                'name',
                'slug',
                [
                    'attribute' => 'status',
                    'value' => function ($model) {
                        return ArrayHelper::getValue(DataHelper::getConstantList('STATUS', Course::class), $model->status);
                    },
                    'filter' => DataHelper::getConstantList('STATUS', Course::class)
                ],
                // 'created_at',
                // 'updated_at',

                //['class' => 'yii\grid\ActionColumn', 'template' => '{view} {update}'],
                [
                    'label' => '',
                    'format' => 'raw',
                    'value' => function ($data) {
                        return ButtonDropdown::widget([
                            'label' => '<i class="fa fa-fw fa-cogs"></i> Actions',
                            'options' => ['class' => 'btn btn-xs btn-primary dropdown-toggle'],
                            'encodeLabel' => false,
                            'containerOptions' => ['class' => 'btn-group dropdown'],
                            'dropdown' => [
                                'options' => ['class' => 'dropdown-menu dropdown-menu-right'],
                                'encodeLabels' => false,
                                'items' => [
                                    ['label' => '<i class="fa fa-eye margin-r-5"></i>View Course', 'url' => ['course/view', 'id' => $data->id]],
                                    ['label' => '<i class="fa fa-pencil margin-r-5"></i>Update Course', 'url' => ['course/update', 'id' => $data->id],  'icon' => 'circle-o'],
                                    ['label' => '<i class="fa fa-pencil margin-r-5"></i>Course Eligibility', 'url' => ['course-eligibility/create', 'courseId' => $data->id]],
                                ],
                            ],
                        ]);
                    }
                ],
            ],
        ]); ?>
    </div>
</div>
