<?php

use common\helpers\DataHelper;
use common\models\GetgisArticleCategory;
use common\models\User;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model common\models\GetgisArticleCategory */

$this->title = $model->name;
$this->params['breadcrumbs'][] = ['label' => 'Getgis Article Categories', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="getgis-article-category-view box box-primary">
    <div class="box-header">
        <?= Html::a('Update', ['update', 'id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?= DetailView::widget([
            'model' => $model,
            'attributes' => [
                'id',
                'name',
                'slug',
                'description',
                'h1',
                'meta_title',
                'meta_description',
                'meta_keywords',
                [
                    'label' => 'Status',
                    'attribute' => 'status',
                    'value' => ArrayHelper::getValue(DataHelper::getConstantList('STATUS', GetgisArticleCategory::class), $model->status)
                ],
                'created_by' => [
                    'attribute' => 'created_by',
                    'value' => function ($model) {
                        if (!empty($model->created_by)) {
                            $user =  User::find()->select('name')->where(['id' => $model->created_by])->one();
                            return $user->name ?? '';
                        } else {
                            return '-';
                        }
                    },
                    'label' => 'Created By'
                ],
                'updated_by' => [
                    'attribute' => 'updated_by',
                    'value' => function ($model) {
                        if (!empty($model->updated_by)) {
                            $user =  User::find()->select('name')->where(['id' => $model->updated_by])->one();
                            return $user->name ?? '';
                        } else {
                            return '-';
                        }
                    },
                    'label' => 'Updated By'
                ],
                'created_at:datetime',
                'updated_at:datetime',
            ],
        ]) ?>
    </div>
</div>