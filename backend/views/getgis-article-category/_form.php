<?php

use backend\models\GetgisArticleSearch;
use common\helpers\DataHelper;
use yii\helpers\Html;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model common\models\GetgisArticleCategory */
/* @var $form yii\widgets\ActiveForm */

$model->description = html_entity_decode($model->description);
?>

<style>
    .form-group.has-error .help-block {
        position: absolute;
        bottom: -13px;
        font-size: 13px;
    }
</style>

<div class="getgis-article-category-form box box-primary">
    <?php $form = ActiveForm::begin(); ?>
    <div class="box-body table-responsive">
        <div class="row" style="margin: 0;">
            <div class="col-md-6">
                <?= $form->field($model, 'name')->textInput(['maxlength' => true]) ?>
            </div>
            <div class="col-md-6">
                <?= $form->field($model, 'slug')->textInput(['maxlength' => true, 'disabled' => true]) ?>
            </div>
            <div class="col-md-12">
                <?= $form->field($model, 'description')->textarea(['rows' => 8]) ?>
            </div>
            <div class="col-md-6">
                <?= $form->field($model, 'h1')->textInput(['maxlength' => true]) ?>
            </div>
            <div class="col-md-6">
                <?= $form->field($model, 'meta_title')->textInput(['maxlength' => true]) ?>
            </div>
            <div class="col-md-6">
                <?= $form->field($model, 'meta_description')->textInput(['maxlength' => true]) ?>
            </div>
            <div class="col-md-6">
                <?= $form->field($model, 'meta_keywords')->textInput(['maxlength' => true]) ?>
            </div>
            <div class="col-md-6">
                <?php if ($model->isNewRecord) {
                    echo $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', GetgisArticleSearch::class), ['options' => [0 => ['selected' => true]]]);
                } else {
                    echo $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', GetgisArticleSearch::class));
                } ?>
            </div>
        </div>
    </div>
    <div class="box-footer">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <?php ActiveForm::end(); ?>
</div>
<?php
$this->registerJs("
    $('#getgisarticlecategory-name').on('input', function() {
        var title = $(this).val();
        var slug = title.toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/^-|-$/g, '');
        $('#getgisarticlecategory-slug').val(slug);
    });
");
?>