<?php

use common\helpers\DataHelper;
use common\models\GetgisArticleCategory;
use yii\helpers\Html;
use yii\grid\GridView;
use yii\helpers\ArrayHelper;

/* @var $this yii\web\View */
/* @var $searchModel backend\models\GetgisArticleCategorySearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Getgis Article Categories';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="getgis-article-category-index box box-primary">
    <div class="box-header with-border">
        <?= Html::a('Create Getgis Article Category', ['create'], ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?php
        ?>
        <?= GridView::widget([
            'dataProvider' => $dataProvider,
            'filterModel' => $searchModel,
            'layout' => "{items}\n{summary}\n{pager}",
            'columns' => [
                ['class' => 'yii\grid\SerialColumn'],
                'name',
                'slug',
                'h1',
                'meta_title',
                [
                    'attribute' => 'status',
                    'value' => function ($model) {
                        return ArrayHelper::getValue(DataHelper::getConstantList('STATUS', GetgisArticleCategory::class), $model->status);
                    },
                    'filter' => DataHelper::getConstantList('STATUS', GetgisArticleCategory::class)
                ],
                [
                    'class' => 'yii\grid\ActionColumn',
                    'header' => '<a href="#">Actions</a>',
                    'template' => '{view}&nbsp;&nbsp;&nbsp;{update}',
                ],
            ]
        ]); ?>
    </div>
</div>