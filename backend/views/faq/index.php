<?php

use common\helpers\DataHelper;
use common\models\Faq;
use yii\helpers\Html;
use yii\grid\GridView;
use yii\helpers\ArrayHelper;

/* @var $this yii\web\View */
/* @var $searchModel backend\models\FaqSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Faqs';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="faq-index box box-primary">
    <div class="box-header with-border">
    <?= Html::a('Create Faq', ['create'], ['class' => 'btn btn-success btn-flat']) ?>
      <?php if (Yii::$app->user->can('BulkFaq')) { ?>
            <?= Html::a('Bulk Upload Faq', ['faq-bulk-upload'], ['class' => 'btn btn-info btn-flat']) ?>
      <?php } ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?php // echo $this->render('_search', ['model' => $searchModel]);
        ?>
        <?= GridView::widget([
            'dataProvider' => $dataProvider,
            'filterModel' => $searchModel,
            'layout' => "{items}\n{summary}\n{pager}",
            'columns' => [
                ['class' => 'yii\grid\SerialColumn'],

                // 'id',
                // 'old_id',
                'entity',
                // 'category_id',
                [
                    'attribute' => 'entity_id',
                    'label' => 'Title',
                    'value' => function ($model) {
                        return $model->getTitle();
                    }
                ],
                'page',
                'sub_page',
                'child_sub_page',
                // 'qnas:ntext',
                [
                    'attribute' => 'status',
                    'value' => function ($model) {
                        return ArrayHelper::getValue(DataHelper::getConstantList('STATUS', Faq::class), $model->status);
                    },
                    'filter' => DataHelper::getConstantList('STATUS', Faq::class)
                ],
                // 'created_at',
                // 'updated_at',

                ['class' => 'yii\grid\ActionColumn', 'template' => '{view} {update}'],
            ],
        ]); ?>
    </div>
</div>