<?php

use yii\helpers\Html;
use yii\widgets\DetailView;
use yii\helpers\ArrayHelper;
use common\helpers\DataHelper;
use common\models\SaDegree;

/* @var $this yii\web\View */
/* @var $model common\models\SaDegree */

$this->title = $model->name;
$this->params['breadcrumbs'][] = ['label' => 'Sa Degrees', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="sa-degree-view box box-primary">
    <div class="box-header">
        <?= Html::a('Update', ['update', 'id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?= DetailView::widget([
            'model' => $model,
            'attributes' => [
                'slug',
                [
                    'label' => 'Status',
                    'attribute' => 'status',
                    'value' => ArrayHelper::getValue(DataHelper::getConstantList('STATUS', SaDegree::class), $model->status)
                ],
                [
                    'attribute' => 'created_by',
                    'value' => function ($model) {
                        return !empty($model->author) ? $model->author->name : '';
                    }
                ],
                [
                    'attribute' => 'updated_by',
                    'value' => function ($model) {
                        return $model->updater->name ?? '';
                    }
                ],
                'created_at:datetime',
                'updated_at:datetime',
            ],
        ]) ?>
    </div>
</div>
