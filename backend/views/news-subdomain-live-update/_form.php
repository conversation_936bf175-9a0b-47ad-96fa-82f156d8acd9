<?php


use yii\helpers\Html;
use yii\widgets\ActiveForm;
use common\helpers\DataHelper;
use common\models\NewsSubdomainLiveUpdate;
use kartik\date\DatePicker;
use kartik\datetime\DateTimePicker;

/* @var $this yii\web\View */
/* @var $model backend\models\LiveUpdate */
/* @var $form yii\widgets\ActiveForm */
?>

<div class="live-update-form form box box-primary">

    <?php $form = ActiveForm::begin(); ?>
    <div class="box-body">

        <?= $form->field($model, 'title')->textInput(['maxlength' => true])->hint('You can add upto 200 characters.') ?>

        <?=
        $this->render('/widget/tinymce', [
            'form' => $form,
            'model' => $model,
            'entity' => 'content',
            'type' => 'live-news'
        ])
        ?>
        <div class="row">
            <div class="col-md-6">
                <?= $form->field($model, 'scheduled_at')->widget(
                    \kartik\datetime\DateTimePicker::class,
                    [
                        'options' => ['placeholder' => 'Select Date'],
                        'pluginOptions' => [
                            'autoclose' => true,
                            'todayHighlight' => true,
                            'locale' => [
                                'format' => 'yyyy-mm-dd',
                            ],
                            'startDate' => date('Y-m-d'),
                        ],
                    ]
                ); ?>
                


            </div>
            <div class="col-md-6">
            <?= $form->field($model, 'expired_at')->widget(
                \kartik\datetime\DateTimePicker::class,
                [
                    'options' => ['placeholder' => 'Select Date'],
                    'pluginOptions' => [
                        'autoclose' => true,
                        'todayHighlight' => true,
                        'locale' => [
                            'format' => 'yyyy-mm-dd',
                        ],
                        'startDate' => date('Y-m-d'),
                    ],
                ]
            ); ?>

            </div>
        </div>
        <?= $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', NewsSubdomainLiveUpdate::class)) ?>
        <div class="form-group">
            <?= Html::submitButton('Save', ['class' => 'btn btn-success']) ?>
        </div>
        <?php ActiveForm::end(); ?>

    </div>

    <?php
    $this->registerJs("
          $(document).ready(function() {
            function toggleFields(status) {
                if (status == 1) {
                    $('#newssubdomainliveupdate-scheduled_at').prop('disabled', true);
                    $('#newssubdomainliveupdate-expired_at').prop('disabled', true);
                } else {
                    $('#newssubdomainliveupdate-scheduled_at').prop('disabled', false);
                    $('#newssubdomainliveupdate-expired_at').prop('disabled', false);
                }
            }
            var stat = $('#newssubdomainliveupdate-status').val();
            toggleFields(stat);
            $('#newssubdomainliveupdate-status').on('change', function() {
                var status = $(this).val();
                toggleFields(status);
            });
        });
    ");
    ?>