<?php

use dosamigos\tinymce\TinyMce;
use yii\web\JsExpression;
use common\helpers\ContentHelper;

if (isset($model->description)) {
    //$model->description = ContentHelper::removeStyleTag(stripslashes($model->description));
}
?>

<?= $form->field($model, $entity ?? 'description')->widget(TinyMce::class, [
    'options' => ['rows' => 10],
    'language' => 'en',
    'clientOptions' => [
        'plugins' => [
            'advlist', 'autolink', 'lists', 'link', 'charmap', 'preview', 'anchor', 'wordcount','print',
            'searchreplace', 'visualblocks', 'code', 'fullscreen',
            'insertdatetime', 'media', 'table', 'image','contextmenu', 'paste', 'image', 'imagetools'
        ],
        'toolbar' => 'undo redo | styleselect | bold italic | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | link image',
        // Enable rel=nofollow option in the Insert/Edit Link panel
        'rel_list' => [
            ['title' => '', 'value' => ''],
            ['title' => 'No Follow', 'value' => 'nofollow'],
            // ['title' => 'No Opener', 'value' => 'noopener'],
            // ['title' => 'No Referrer', 'value' => 'noreferrer'],
        ],
        'table_default_styles'=>[
            'width'=> '100%',
           // 'height'=> '500px'
        ],
        'table_default_attributes' => [
            'cellpadding' => '1', // Example cell padding
            'cellspacing' => '1', // Example cell padding
            'border'=>'1'
        ],
        'allow_unsafe_link_target' => true,
        // Automatically set rel="nofollow" for external links
        // 'setup' => new JsExpression("function(editor) {
        //     editor.on('change', function() {
        //         var links = editor.dom.select('a');
        //         links.forEach(function(link) {
        //             var href = editor.dom.getAttrib(link, 'href');
        //             if (href && !href.startsWith(window.location.origin)) { // External link check
        //                 if (!editor.dom.getAttrib(link, 'rel')) {
        //                     editor.dom.setAttrib(link, 'rel', 'nofollow');
        //                 }
        //             }
        //         });
        //     });
        // }"),
        'images_upload_url' => '/article/tinymce-upload',
        'file_picker_types' => 'image',
        'images_upload_handler' => new JsExpression("function(blobInfo, success, failure) {
                    var imageType = blobInfo.blob().type;
                    var validateImageType = imageType !== '' ? imageType.split('/') : '';
                    if (validateImageType && validateImageType[1] !== 'webp') {
                        failure('Only files with webp extension is allowed');
                        return; 
                    }
                    var image_size = blobInfo.blob().size / 1000;  // image size in kbytes
                    var max_size   = 100;                // max size in kbytes

                    if( image_size  > max_size ) {        
                        failure('Image is too large( '+ image_size  + ') ,Maximum image size is:' + max_size + ' kB');
                        return;  
                    }

                    var xhr, formData;
                    xhr = new XMLHttpRequest();
                    xhr.withCredentials = false;
                    xhr.open('POST', '/ajax/tinymce-upload');

                    xhr.onload = function() {
                        var json;

                        if (xhr.status != 200) {
                            failure('HTTP Error: ' + xhr.status);
                            return;
                        }
                        
                        json = JSON.parse(xhr.responseText);
                        
                        if (!json || typeof json.location != 'string') {
                            failure('Invalid JSON: ' + xhr.responseText);
                            return;
                        }
                        success(json.location);
                    };

                    formData = new FormData();
                    formData.append('UploadForm[editorImage]', blobInfo.blob(), blobInfo.filename());
                    formData.append('_csrf-backend', document.querySelector('meta[name=csrf-token]').content);
                    formData.append('type', '$type');

                    xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
                    xhr.send(formData);
                }")
    ]
]);
