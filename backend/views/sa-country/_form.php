<?php

use common\helpers\DataHelper;
use common\models\SaCountry;
use common\models\User;
use kartik\select2\Select2;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\web\JsExpression;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model common\models\SaCountry */
/* @var $form yii\widgets\ActiveForm */

$user =  ArrayHelper::map(User::find()->where(['id' => Yii::$app->user->identity->id])->all(), 'id', 'name');
$disable = $model->isNewRecord ? false : true;
?>

<div class="sa-country-form box box-primary">
    <?php $form = ActiveForm::begin(); ?>
    <div class="box-body table-responsive">

        <?= $form->field($model, 'created_by')->dropDownList(
            ArrayHelper::map(User::find()->all(), 'id', 'name'),
            [
                'options' => [
                    Yii::$app->user->identity->id => ['Selected' => true], // Set the selected value
                ],
                'disabled' => true // Disable the entire dropdown
            ]
        )->label('Author'); ?>

        <?= $form->field($model, 'name')->textInput(['maxlength' => true, 'disabled' => $disable]) ?>

        <?= $form->field($model, 'currency')->textInput(['maxlength' => true, 'disabled' => $disable]) ?>

        <?= $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', SaCountry::class)) ?>
    </div>
    <div class="box-footer">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <?php ActiveForm::end(); ?>
</div>