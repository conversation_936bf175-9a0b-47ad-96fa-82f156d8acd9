<?php

use common\helpers\DataHelper;
use common\models\SaCountry;
use common\models\SaCountryDetail;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model common\models\SaCountry */

$this->title = $model->name;
$this->params['breadcrumbs'][] = ['label' => 'Sa Countries', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
$countryDetail = SaCountryDetail::find()->where(['sa_country_id' => $model->id])->one();
?>
<div class="sa-country-view box box-primary">
    <div class="box-header">
        <?= Html::a('Update', ['update', 'id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
        <?php if (!empty($countryDetail)): ?>
            <?= Html::a('Update Country Detail', ['sa-country-detail/update', 'id' => $countryDetail->id], ['class' => 'btn btn-primary btn-flat']) ?>
        <?php else: ?>
            <?= Html::a('Create Country Detail', ['sa-country-detail/create', 'country_id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
        <?php endif; ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?= DetailView::widget([
            'model' => $model,
            'attributes' => [
                'slug',
                [
                    'label' => 'Status',
                    'attribute' => 'status',
                    'value' => ArrayHelper::getValue(DataHelper::getConstantList('STATUS', SaCountry::class), $model->status)
                ],
                [
                    'attribute' => 'created_by',
                    'value' => function ($model) {
                        return !empty($model->author) ? $model->author->name : '';
                    }
                ],
                [
                    'attribute' => 'updated_by',
                    'value' => function ($model) {
                        return $model->updater->name ?? '';
                    }
                ],
                'created_at:datetime',
                'updated_at:datetime',
            ],
        ]) ?>
    </div>
</div>