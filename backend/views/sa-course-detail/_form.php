<?php

use common\helpers\DataHelper;
use common\models\SaCollege;
use common\models\SaCourse;
use common\models\SaCourseDetail;
use common\models\SaCourseDurationType;
use common\models\SaDegree;
use common\models\SaSpecialization;
use common\models\SaStream;
use common\models\User;
use kartik\select2\Select2;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\web\JsExpression;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model common\models\SaCourseDetail */
/* @var $form yii\widgets\ActiveForm */

$collegeList = ArrayHelper::map(SaCollege::find()->andWhere(['id' => $model->sa_college_id])->all(), 'id', 'name');
$courseList = ArrayHelper::map(SaCourse::find()->andWhere(['id' => $model->sa_course_id])->all(), 'id', 'name');
$streamList = ArrayHelper::map(SaStream::find()->andWhere(['id' => $model->sa_stream_id])->all(), 'id', 'name');
$degreeList = ArrayHelper::map(SaDegree::find()->andWhere(['id' => $model->sa_degree_id])->all(), 'id', 'name');
$specializationList = ArrayHelper::map(SaSpecialization::find()->andWhere(['id' => $model->sa_specialization_id])->all(), 'id', 'name');
$durationMode = ArrayHelper::map(SaCourseDurationType::find()->andWhere(['id' => $model->sa_course_duration_type_id])->all(), 'id', 'name');
$disabled = false;

if (isset($_GET['college_id'])) {
    $selectedCollege = SaCollege::findOne($_GET['college_id']); // Fetch the country by ID
    if ($selectedCollege) {
        $collegeList[$selectedCollege->id] = $selectedCollege->name;
        $model->sa_college_id = $selectedCollege->id; // Set the model's value
    }
}

if (isset($_GET['college_id']) || !$model->isNewRecord) {
    $disabled = true;
}
?>

<div class="sa-course-detail-form box box-primary">
    <?php $form = ActiveForm::begin(); ?>
    <div class="box-body table-responsive">

        <div class="col-md-6">
            <?= $form->field($model, 'created_by')->dropDownList(
                ArrayHelper::map(User::find()->all(), 'id', 'name'),
                [
                    'options' => [
                        Yii::$app->user->identity->id => ['Selected' => true], // Set the selected value
                    ],
                    'disabled' => true // Disable the entire dropdown
                ]
            )->label('Author'); ?>
        </div>


        <div class="col-md-6">
            <?= $form->field($model, 'sa_college_id')->widget(Select2::classname(), [
                'disabled' => $disabled,
                'data' => $collegeList, // array of text to show in the tag for the selected items
                'options' => [
                    'placeholder' => '--Select--',
                    'multiple' => false,
                ],
                'pluginOptions' => [
                    'allowClear' => true,
                    'minimumInputLength' => 3,
                    'language' => [
                        'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                    ],
                    'ajax' => [
                        'url' => ['../ajax/sa-college'],
                        'dataType' => 'json',
                        'data' => new JsExpression('function(params) {return {q:params.term}; }')
                    ],
                    'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                    'templateResult' => new JsExpression('function(data) { return data.text; }'),
                    'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                ],
            ])->label('SA College Name');
?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'sa_course_id')->widget(Select2::classname(), [
                'disabled' => !$model->isNewRecord ? true : false,
                'data' => $courseList, // array of text to show in the tag for the selected items
                'options' => [
                    'placeholder' => '--Select--',
                    'multiple' => false,
                ],
                'pluginOptions' => [
                    'allowClear' => true,
                    'minimumInputLength' => 3,
                    'language' => [
                        'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                    ],
                    'ajax' => [
                        'url' => ['../ajax/sa-course'],
                        'dataType' => 'json',
                        'data' => new JsExpression('function(params) {return {q:params.term}; }')
                    ],
                    'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                    'templateResult' => new JsExpression('function(data) { return data.text; }'),
                    'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                ],
            ])->label('SA Course Name');
?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'sa_stream_id')->widget(Select2::classname(), [
                'disabled' => !$model->isNewRecord ? true : false,
                'data' => $streamList, // array of text to show in the tag for the selected items
                'options' => [
                    'placeholder' => '--Select--',
                    'multiple' => false,
                ],
                'pluginOptions' => [
                    'allowClear' => true,
                    'minimumInputLength' => 3,
                    'language' => [
                        'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                    ],
                    'ajax' => [
                        'url' => ['../ajax/sa-stream'],
                        'dataType' => 'json',
                        'data' => new JsExpression('function(params) {return {q:params.term}; }')
                    ],
                    'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                    'templateResult' => new JsExpression('function(data) { return data.text; }'),
                    'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                ],
            ])->label('SA Stream Name');
?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'sa_degree_id')->widget(Select2::classname(), [
                'disabled' => !$model->isNewRecord ? true : false,
                'data' => $degreeList, // array of text to show in the tag for the selected items
                'options' => [
                    'placeholder' => '--Select--',
                    'multiple' => false,
                ],
                'pluginOptions' => [
                    'allowClear' => true,
                    'minimumInputLength' => 3,
                    'language' => [
                        'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                    ],
                    'ajax' => [
                        'url' => ['../ajax/sa-degree'],
                        'dataType' => 'json',
                        'data' => new JsExpression('function(params) {return {q:params.term}; }')
                    ],
                    'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                    'templateResult' => new JsExpression('function(data) { return data.text; }'),
                    'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                ],
            ])->label('SA Degree Name');
?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'sa_specialization_id')->widget(Select2::classname(), [
                'disabled' => !$model->isNewRecord ? true : false,
                'data' => $specializationList, // array of text to show in the tag for the selected items
                'options' => [
                    'placeholder' => '--Select--',
                    'multiple' => false,
                ],
                'pluginOptions' => [
                    'allowClear' => true,
                    'minimumInputLength' => 3,
                    'language' => [
                        'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                    ],
                    'ajax' => [
                        'url' => ['../ajax/sa-specialization'],
                        'dataType' => 'json',
                        'data' => new JsExpression('function(params) {return {q:params.term}; }')
                    ],
                    'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                    'templateResult' => new JsExpression('function(data) { return data.text; }'),
                    'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                ],
            ])->label('SA Specialization Name');
?>
        </div>

        <div class="col-md-4">
            <?= $form->field($model, 'total_fees')->textInput() ?>
        </div>

        <div class="col-md-4">
            <?= $form->field($model, 'duration')->textInput() ?>
        </div>

        <div class="col-md-4">
            <?= $form->field($model, 'duration_type')->dropDownList(array_flip(DataHelper::$saDurationType), [
                'prompt' => 'Select Duration Type'
            ]) ?>
        </div>

        <div class="col-md-4">
            <?= $form->field($model, 'sa_course_duration_type_id')->widget(Select2::classname(), [
                'disabled' => false,
                'data' => $durationMode, // array of text to show in the tag for the selected items
                'options' => [
                    'placeholder' => '--Select--',
                    'multiple' => false,
                ],
                'pluginOptions' => [
                    'allowClear' => true,
                    'minimumInputLength' => 3,
                    'language' => [
                        'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                    ],
                    'ajax' => [
                        'url' => ['../ajax/sa-course-duration-mode'],
                        'dataType' => 'json',
                        'data' => new JsExpression('function(params) {return {q:params.term}; }')
                    ],
                    'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                    'templateResult' => new JsExpression('function(data) { return data.text; }'),
                    'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                ],
            ])->label('Duration Mode');
?>
        </div>

        <div class="col-md-4">
            <?= $form->field($model, 'is_published')->dropDownList(DataHelper::getConstantList('IS_PUBLISHED', SaCourseDetail::class)) ?>
        </div>

        <div class="col-md-4">
            <?= $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', SaCourseDetail::class)) ?>
        </div>

    </div>
    <div class="box-footer">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <?php ActiveForm::end(); ?>
</div>