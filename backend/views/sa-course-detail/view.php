<?php

use common\helpers\DataHelper;
use common\models\SaCourseDetail;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model common\models\SaCourseDetail */

$this->title = $model->saCollege->name;
$this->params['breadcrumbs'][] = ['label' => 'Sa Course Detail' , 'url' => '/sa-course-detail/index?SaCourseDetailSearch[sa_college_name]=' . $model->saCollege->name];
$this->params['breadcrumbs'][] = ['label' => $this->title , 'url' => '/sa-college/view?id=' . $model->saCollege->id];
?>
<div class="sa-course-detail-view box box-primary">
    <div class="box-header">
        <?= Html::a('Update', ['update', 'id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?= DetailView::widget([
            'model' => $model,
            'attributes' => [
                [
                    'attribute' => 'sa_college_id',
                    'value' => function ($model) {
                        return !empty($model->saCollege) ? $model->saCollege->name : '';
                    }
                ],
                [
                    'attribute' => 'sa_course_id',
                    'value' => function ($model) {
                        return !empty($model->saCourse) ? $model->saCourse->name : '';
                    }
                ],
                [
                    'attribute' => 'sa_stream_id',
                    'value' => function ($model) {
                        return !empty($model->saStream) ? $model->saStream->name : '';
                    }
                ],
                [
                    'attribute' => 'sa_degree_id',
                    'value' => function ($model) {
                        return !empty($model->saDegree) ? $model->saDegree->name : '';
                    }
                ],
                [
                    'attribute' => 'sa_specialization_id',
                    'value' => function ($model) {
                        return !empty($model->saSpecialization) ? $model->saSpecialization->name : '';
                    }
                ],
                'total_fees',
                'duration',
                [
                    'label' => 'Duration Type',
                    'attribute' => 'duration_type',
                    'value' => function ($model) {
                        $mode = array_flip(DataHelper::$saDurationType);
                        return !empty($model->duration_type) ? $mode[$model->duration_type] : '';
                    }
                ],
                [
                    'label' => 'Duration Mode',
                    'attribute' => 'sa_course_duration_type_id',
                    'value' => function ($model) {
                        return !empty($model->sa_course_duration_type_id) ? $model->saCourseDurationType->name : '';
                    }
                ],
                [
                    'label' => 'Is Published',
                    'attribute' => 'is_published',
                    'value' => ArrayHelper::getValue(DataHelper::getConstantList('IS_PUBLISHED', SaCourseDetail::class), $model->is_published)
                ],
                [
                    'label' => 'Status',
                    'attribute' => 'status',
                    'value' => ArrayHelper::getValue(DataHelper::getConstantList('STATUS', SaCourseDetail::class), $model->status)
                ],
                [
                    'attribute' => 'created_by',
                    'value' => function ($model) {
                        return !empty($model->author) ? $model->author->name : '';
                    }
                ],
                [
                    'attribute' => 'updated_by',
                    'value' => function ($model) {
                        return $model->updater->name ?? '';
                    }
                ],
                'created_at:datetime',
                'updated_at:datetime',
            ],
        ]) ?>
    </div>
</div>
