<?php

use common\helpers\DataHelper;
use common\models\SaCourseDetail;
use yii\helpers\Html;
use yii\grid\GridView;
use yii\helpers\ArrayHelper;

/* @var $this yii\web\View */
/* @var $searchModel backend\models\SaCourseDetailSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Sa Course Details';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="sa-course-detail-index box box-primary">
    <div class="box-header with-border">
        <?= Html::a('Create Sa Course Detail', ['create'], ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?php // echo $this->render('_search', ['model' => $searchModel]); ?>
        <?= GridView::widget([
            'dataProvider' => $dataProvider,
            'filterModel' => $searchModel,
            'layout' => "{items}\n{summary}\n{pager}",
            'columns' => [
                ['class' => 'yii\grid\SerialColumn'],

                [
                    'attribute' => 'sa_college_name',
                    'label' => 'College',
                    'value' => function ($m) {
                        return !empty($m->saCollege->name) ? $m->saCollege->name : '';
                    }
                ],
                [
                    'attribute' => 'sa_course_name',
                    'label' => 'Course',
                    'value' => function ($m) {
                        return !empty($m->saCourse->name) ? $m->saCourse->name : '';
                    }
                ],
                [
                    'attribute' => 'sa_specialization_name',
                    'label' => 'Specialization',
                    'value' => function ($m) {
                        return !empty($m->saSpecialization->name) ? $m->saSpecialization->name : '';
                    }
                ],
                [
                    'attribute' => 'status',
                    'value' => function ($model) {
                        return ArrayHelper::getValue(DataHelper::getConstantList('STATUS', SaCourseDetail::class), $model->status);
                    },
                    'filter' => DataHelper::getConstantList('STATUS', SaCourseDetail::class)
                ],

                ['class' => 'yii\grid\ActionColumn','template' => '{view} {update}',],
            ],
        ]); ?>
    </div>
</div>
