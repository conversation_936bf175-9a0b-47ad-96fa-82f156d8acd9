<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model backend\models\SaCourseDetailSearch */
/* @var $form yii\widgets\ActiveForm */
?>

<div class="sa-course-detail-search">

    <?php $form = ActiveForm::begin([
        'action' => ['index'],
        'method' => 'get',
    ]); ?>

    <?= $form->field($model, 'id') ?>

    <?= $form->field($model, 'sa_course_id') ?>

    <?= $form->field($model, 'sa_college_id') ?>

    <?= $form->field($model, 'sa_stream_id') ?>

    <?= $form->field($model, 'sa_degree_id') ?>

    <?php // echo $form->field($model, 'sa_program_id') ?>

    <?php // echo $form->field($model, 'sa_specialization_id') ?>

    <?php // echo $form->field($model, 'total_fees') ?>

    <?php // echo $form->field($model, 'duration') ?>

    <?php // echo $form->field($model, 'duration_type') ?>

    <?php // echo $form->field($model, 'mode') ?>

    <?php // echo $form->field($model, 'is_published') ?>

    <?php // echo $form->field($model, 'status') ?>

    <?php // echo $form->field($model, 'created_at') ?>

    <?php // echo $form->field($model, 'updated_at') ?>

    <div class="form-group">
        <?= Html::submitButton('Search', ['class' => 'btn btn-primary']) ?>
        <?= Html::resetButton('Reset', ['class' => 'btn btn-default']) ?>
    </div>

    <?php ActiveForm::end(); ?>

</div>
