<?php

use common\helpers\ContentHelper;
use common\helpers\DataHelper;
use common\models\College;
use common\models\CollegeListingNotification;
use common\models\Course;
use common\models\Exam;
use common\models\State;
use common\models\ExamNotification;
use common\models\Program;
use common\models\Stream;
use kartik\date\DatePicker;
use kartik\select2\Select2;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\web\JsExpression;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model common\models\ExamNotification */
/* @var $form yii\widgets\ActiveForm */

$data = ArrayHelper::map(Exam::find()->where(['id' => $model->exam_id])->all(), 'id', 'name');
$streamData = ArrayHelper::map(Stream::find()->where(['id' => $model->stream_id])->all(), 'id', 'name');
$programData = ArrayHelper::map(Program::find()->where(['id' => $model->program_id])->all(), 'id', 'name');
$courseData = ArrayHelper::map(Course::find()->where(['id' => $model->course_id])->all(), 'id', 'name');
$content = ContentHelper::removeStyleTag(stripslashes(html_entity_decode($model->content)));
if (!empty($model->state_id)) {
    $state[] =  ArrayHelper::map(State::find()->where(['in', 'id',json_decode($model->state_id)])->all(), 'id', 'name');
}
$allstate =  ArrayHelper::map(State::find()->all(), 'id', 'name');
?>

<div class="exam-notification-form box box-primary">
    <?php $form = ActiveForm::begin(); ?>
    <div class="box-body table-responsive">

        <?=
        $form->field($model, 'stream_id')->widget(Select2::class, [
            'disabled' => !$model->isNewRecord,
            'data' => $streamData, // array of text to show in the tag for the selected items
            'options' => [
                'placeholder' => '--Select Stream--',
                'multiple' => true,
            ],
            'pluginOptions' => [
                'allowClear' => true,
                'minimumInputLength' => 1,
                'maximumSelectionLength' => 2,
                'language' => [
                    'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                    'maximumSelected' => new JsExpression("function () { return 'You can only select maximum 2 exams'; }"),
                ],
                'ajax' => [
                    'url' => ['../ajax/stream-list'],
                    'dataType' => 'json',
                    'data' => new JsExpression('function(params) {return {query:params.term}; }')
                ],
                'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                'templateResult' => new JsExpression('function(data) { return data.text; }'),
                'templateSelection' => new JsExpression('function (data) { return data.text; }'),
            ],
        ])->label('Stream Name');
        ?>

        <?=
        $form->field($model, 'exam_id')->widget(Select2::class, [
            'disabled' => !$model->isNewRecord,
            'data' => $data, // array of text to show in the tag for the selected items
            'options' => [
                'placeholder' => '--Select Exam--',
                'multiple' => true,
            ],
            'pluginOptions' => [
                'allowClear' => true,
                'minimumInputLength' => 0,
                'maximumSelectionLength' => 2,
                'language' => [
                    'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                    'maximumSelected' => new JsExpression("function () { return 'You can only select maximum 2 exams'; }"),
                ],
                'ajax' => [
                    'url' => ['../ajax/stream-exam-list'],
                    'dataType' => 'json',
                    'data' => new JsExpression('function(params) {return {stream_id:$("#collegelistingnotification-stream_id").val(), query:params.term}; }')
                ],
                'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                'templateResult' => new JsExpression('function(data) { return data.text; }'),
                'templateSelection' => new JsExpression('function (data) { return data.text; }'),
            ],
        ])->label('Exam Name');
        ?>

        <?=
        $form->field($model, 'course_id')->widget(Select2::class, [
            'disabled' => !$model->isNewRecord,
            'data' => $courseData, // array of text to show in the tag for the selected items
            'options' => [
                'placeholder' => '--Select Course--',
                'multiple' => true,
            ],
            'pluginOptions' => [
                'allowClear' => true,
                'maximumSelectionLength' => 20,
                'language' => [
                    'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                    'maximumSelected' => new JsExpression("function () { return 'You can only select maximum 20 Course'; }"),
                ],
                'ajax' => [
                    'url' => ['../ajax/exam-course'],
                    'dataType' => 'json',
                    'data' => new JsExpression('function(params) {return {exam_id:$("#collegelistingnotification-exam_id").val(), stream_id:$("#collegelistingnotification-stream_id").val(), q:params.term}; }')
                ],
                'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                'templateResult' => new JsExpression('function(data) { return data.text; }'),
                'templateSelection' => new JsExpression('function (data) { return data.text; }'),
            ],
        ])->label('Course');
        ?>

        <?=
        $form->field($model, 'program_id')->widget(Select2::class, [
            'disabled' => !$model->isNewRecord,
            'data' => $programData, // array of text to show in the tag for the selected items
            'options' => [
                'placeholder' => '--Select Program--',
                'multiple' => true,
                'maximumSelectionLength' => 30,
            ],
            'pluginOptions' => [
                'allowClear' => true,
                'language' => [
                    'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                    'maximumSelected' => new JsExpression("function () { return 'You can only select maximum 30 Programs'; }"),
                ],
                'ajax' => [
                    'url' => ['../ajax/program-course'],
                    'dataType' => 'json',
                    'data' => new JsExpression('function(params) {return {course_id:$("#collegelistingnotification-course_id").val(), q:params.term}; }')
                ],
                'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                'templateResult' => new JsExpression('function(data) { return data.text; }'),
                'templateSelection' => new JsExpression('function (data) { return data.text; }'),
            ],
        ])->label('Program');
        ?>

        <?=
        $this->render('/widget/tinymce', [
            'form' => $form,
            'model' => $model,
            'type' => College::ENTITY_COLLEGE,
            'entity' => 'content'
        ])
        ?>
        <?= $form->field($model, 'text')->textInput()->label('Heading Text') ?>

        <?=
        $form->field($model, 'start_date')->widget(DatePicker::class, [
            'value' => '',
            'options' => ['placeholder' => 'Select date ...', 'class' => 'start_date'],
            'type' => DatePicker::TYPE_COMPONENT_APPEND,
            'pluginOptions' => [
                'format' => 'yyyy-mm-dd',
                'minViewMode' => 'month',
                'startDate' => date('Y-m-d'),
                'todayHighlight' => true,
                'autocomplete' => 'off',
            ],
        ])->label('Start Date');
        ?>
        <?=
        $form->field($model, 'end_date')->widget(DatePicker::class, [
            'value' => '',
            'options' => ['placeholder' => 'Select date ...', 'class' => 'start_date'],
            'type' => DatePicker::TYPE_COMPONENT_APPEND,
            'pluginOptions' => [
                'format' => 'yyyy-mm-dd',
                'minViewMode' => 'month',
                'startDate' => date('Y-m-d'),
                'todayHighlight' => true,
                'autocomplete' => 'off',
            ],
        ])->label('End Date');
        ?>
    <?php
    if (!$model->isNewRecord) {
        $selectedValueState = [];
        if (!empty($state)) {
            foreach ($state as $value) {
                if (!empty($value)) {
                    foreach ($value as $key => $val) {
                        $selectedValueState[$key] = ['selected' => true];
                    }
                }
            }
        }
    } ?>
    
                <?= $form->field($model, 'state_id')->widget(Select2::classname(), [
                'data'=>$allstate,
                'disabled' => !$model->isNewRecord,
                'options' => [
                    'multiple' => true, 'placeholder' => 'Select State', 'autocomplete' => 'off',
                    'options' => $selectedValueState ?? [],
                ],
                'pluginOptions' => [
                    'allowClear' => true,

                ],
            ])->label('State'); ?>
                
               
        <?= $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', CollegeListingNotification::class)) ?>

    </div>
    <div class="box-footer">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <?php ActiveForm::end(); ?>
</div>