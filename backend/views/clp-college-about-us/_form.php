<?php

use common\helpers\DataHelper;
use common\models\ClpCollegeAboutUs;
use common\models\College;
use kartik\select2\Select2;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\web\JsExpression;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model common\models\ClpCollegeAboutUs */
/* @var $form yii\widgets\ActiveForm */

$data =  ArrayHelper::map(College::find()->andWhere(['id' => $model->college_id])->all(), 'id', 'name');
?>

<div class="clp-college-about-us-form box box-primary">
    <?php $form = ActiveForm::begin(); ?>
    <div class="box-body table-responsive">

        <div class="col-md-12">
            <?= $form->field($model, 'college_id')->widget(Select2::classname(), [
                'disabled' => !$model->isNewRecord || $model->college_id,
                'data' => $data, // array of text to show in the tag for the selected items
                'options' => [
                    'placeholder' => '--Select--',
                    'multiple' => false,
                ],
                'pluginOptions' => [
                    'allowClear' => true,
                    'minimumInputLength' => 3,
                    'language' => [
                        'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                    ],
                    'ajax' => [
                        'url' => ['../ajax/college-list'],
                        'dataType' => 'json',
                        'data' => new JsExpression('function(params) {return {q:params.term}; }')
                    ],
                    'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                    'templateResult' => new JsExpression('function(data) { return data.text; }'),
                    'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                ],
            ])->label('College Name'); ?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'banner_image')->radioList([
                1 => 'Yes',
                0 => 'No',
            ], [
                'itemOptions' => ['labelOptions' => ['style' => 'margin-right: 15px;']]
            ]) ?>
        </div>

        <div class="col-md-12">
            <?=
            $this->render('/widget/tinymce', [
                'form' => $form,
                'model' => $model,
                'entity' => 'content',
                'type' => ''
            ])
            ?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', ClpCollegeAboutUs::class)); ?>
        </div>

    </div>
    <div class="box-footer">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <?php ActiveForm::end(); ?>
</div>