<?php

use common\helpers\DataHelper;
use common\models\College;
use common\models\TopRecruiters;
use kartik\select2\Select2;
use yii\bootstrap\ActiveForm;
use unclead\multipleinput\TabularInput;
use yii\helpers\Html;
use unclead\multipleinput\TabularColumn;
use yii\helpers\ArrayHelper;
use yii\web\JsExpression;

/* @var $this \yii\web\View */
/* @var $models Item[] */

$this->title = 'Top Recruiters';
if (!empty($clp_id)) {
    $this->params['breadcrumbs'][] = ['label' => 'Custom Landing Page', 'url' => ['custom-landing-page/view', 'id' => $clp_id]];
}
$this->params['breadcrumbs'][] = $this->title;


$this->registerCssFile('https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css');
$this->registerJsFile('https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js', [
    'depends' => \yii\web\JqueryAsset::class,
]);

$backgroundColor = empty($collegeId) ? 'background-color: #fff;' : 'rgb(233 207 207 / 30%)';
$pointerEvents = empty($collegeId) ? '' : 'pointer-events: none;';
?>
<style>
    .select2-selection--single {
        background-color: <?= $backgroundColor ?> !important;
        <?= $pointerEvents ?>
    }

    .list-cell__college_id {
        width: 352px !important;
    }

    .form-control-static {
        min-height: 0px;
        padding: 0px;
    }

    .flash-message {
        padding: 10px;
        background: #ffe0e0;
        border: 1px solid #ffb3b3;
        color: #d8000c;
        font-weight: bold;
        border-radius: 4px;
        margin-bottom: 10px;
    }

    table.multiple-input-list.table-renderer tr>td:last-child {
        display: none;
    }

    .box-body {
        display: flex;
    }

    .multiple-input {
        width: 1457px;
    }

    #file-input-container {
        width: 149px;
        margin-top: 50px;
        margin-left: 20px;
    }

    .file-upload-row {
        margin-bottom: 25px;
    }
</style>

<div class="exam-date-form box box-primary">
    <?php $form = \yii\bootstrap\ActiveForm::begin([
        'id' => 'tabular-form',
        'options' => [
            'enctype' => 'multipart/form-data'
        ]
    ]) ?>
    <div id="flash-message-container"></div>
    <div class="box-body">

        <?= TabularInput::widget([
            'models' => $models,
            'modelClass' => TopRecruiters::class,
            'allowEmptyList' => 'false',
            'min' => 0,
            'max' => 10,
            'addButtonPosition' => [
                TabularInput::POS_HEADER,
            ],
            'layoutConfig' => [
                'offsetClass'   => 'col-sm-offset-4',
                'labelClass'    => 'col-sm-2',
                'wrapperClass'  => 'col-sm-10',
                'errorClass'    => 'col-sm-4'
            ],
            'attributeOptions' => [
                'enableAjaxValidation'   => false,
                'enableClientValidation' => true,
                'validateOnChange'       => true,
                'validateOnSubmit'       => true,
                'validateOnBlur'         => false,
            ],
            'form' => $form,
            'columns' => [
                [
                    'name' => 'id',
                    'type' => TabularColumn::TYPE_HIDDEN_INPUT
                ],
                [
                    'name' => 'college_id',
                    'title' => 'College',
                    'type' => TabularColumn::TYPE_TEXT_INPUT,
                    'options' => function ($model) use ($collegeId, $collegeName) {
                        $value = $model->college_id ?: $collegeId;
                        $text = $model->college->name ?? $collegeName;

                        return [
                            'class' => 'form-control college-autocomplete',
                            'data-init-text' => $text,
                            'data-index' => 0,
                            'value' => $value,
                            'autocomplete' => 'off',
                            'placeholder' => 'Search for a college...',
                            'readonly' => true,
                        ];
                    }
                ],
                [
                    'name' => 'recruiter_name',
                    'title' => 'Recruiter Name',
                    'type' => TabularColumn::TYPE_TEXT_INPUT,
                ],
                [
                    'name' => 'recruiter_image',
                    'title' => 'Recruiter Image',
                    'type' => TabularColumn::TYPE_TEXT_INPUT,
                    'options' => ['disabled' => true],
                ],
                [
                    'name'  => 'status',
                    'title' => 'Status',
                    'type' => TabularColumn::TYPE_DROPDOWN,
                    'items' => DataHelper::getConstantList('STATUS', TopRecruiters::class),
                ],
            ],
        ]) ?>
        <!-- File input container -->
        <div id="file-input-container">
            <?php foreach ($models as $index => $model): ?>
                <div class="file-upload-row" data-index="<?= $index ?>">
                    <!-- <label>Logo for Recruiter #<?= $index + 1 ?></label> -->
                    <?= Html::fileInput("TopRecruiters[$index][recruiter_image]", null, ['accept' => 'image/*']) ?>
                </div>
            <?php endforeach; ?>
        </div>
    </div>

    <div class="box-footer">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <?php ActiveForm::end(); ?>
</div>

<?php
$js = <<<JS
var fileInputContainer = $('#file-input-container');

function initializeSelect2(context) {
    context.find('.college-autocomplete').each(function() {
        if ($(this).hasClass("select2-hidden-accessible")) return;

        var \$input = $(this);
        var index = \$input.data('index');

        // Create hidden input to store actual college_id
        var hiddenInputName = 'TopRecruiters[' + index + '][college_id]';
        var hiddenInputSelector = 'input[name="' + hiddenInputName + '"]';

        if (!\$input.siblings(hiddenInputSelector).length) {
            $('<input>', {
                type: 'hidden',
                name: hiddenInputName,
                class: 'college-hidden-input',
                'data-index': index,
                value: \$input.val()
            }).insertAfter(\$input);
        }

        var hiddenInput = \$input.siblings(hiddenInputSelector);

        \$input.select2({
            placeholder: 'Search for a college...',
            minimumInputLength: 2,
            ajax: {
                url: '/ajax/college-list',
                dataType: 'json',
                delay: 250,
                data: function (params) {
                    return { q: params.term };
                },
                processResults: function (data) {
                    return { results: data.results };
                },
            },
            templateResult: function (data) { return data.text; },
            templateSelection: function (data) { return data.text || ''; }
        }).on('select2:select', function (e) {
            var selected = e.params.data;
            hiddenInput.val(selected.id); // Store selected value in hidden input
           
            // Clear previous option (prevent duplicates)
            \$input.find('option').remove();

            // Add new one
            var option = new Option(selected.text, selected.id, true, true);
            \$input.append(option).trigger('change');
        }).on('select2:clear', function () {
            hiddenInput.val('');
        });

        // Pre-fill with data-init-text and value
        var initText = \$input.data('init-text');
        var initVal = \$input.val();

        if (initVal && initText) {
            var option = new Option(initText, initVal, true, true);
            \$input.append(option).trigger('change');
        }
    });
}

$(document).ready(function() {
    initializeSelect2($(document));
});

$(document).on('keyup', 'input[name^="TopRecruiters"][name$="[recruiter_name]"]', function () {
    $('#flash-message-container').empty();
});

$('.js-input-plus').on('click', function (e) {
    var lastIndex = $('.file-upload-row').length - 1;
    if (lastIndex < 0) return;

    var lastFileInput = fileInputContainer.find('.file-upload-row[data-index="' + lastIndex + '"] input[type="file"]');
    var lastNameInput = $('#toprecruiters-' + lastIndex + '-recruiter_name');
    var lastCollege = $('input[name="TopRecruiters[' + 0 + '][college_id]"]').val();
    console.log(lastIndex, lastCollege);

    var lastImageTextInput = $('#toprecruiters-' + lastIndex + '-recruiter_image');

    if (!lastNameInput.length || !lastFileInput.length || lastCollege.length == 0) return;
    $('#flash-message-container').empty();

    var nameVal = lastNameInput.val().trim();
    var fileCount = lastFileInput[0].files.length;
    var imageVal = lastImageTextInput.val().trim();

     if(lastCollege) {
        var lastRowCollegeSelect = $('input[name="TopRecruiters[' + lastIndex + '][college_id]"]');
        lastRowCollegeSelect.prop('readonly', true);
        $('.select2-selection--single').attr('style', 'background-color: rgb(233 207 207 / 30%) !important;');
    }

    if (!lastCollege) {
        $('#flash-message-container').html('<div class="flash-message">Please select a college for row #' + (lastIndex + 1) + '</div>');
        e.stopImmediatePropagation();
        return false;
    }

    if (!nameVal) {
        $('#flash-message-container').html(
            '<div class="flash-message">Please enter a name for recruiter #' + (lastIndex + 1) + ' before adding a new one.</div>'
        );
        e.stopImmediatePropagation();
        return false;
    }

    if (nameVal && fileCount === 0 && !imageVal) {
        $('#flash-message-container').html(
            '<div class="flash-message">Please upload an image for recruiter #' + (lastIndex + 1) + ' before adding a new one.</div>'
        );
        setTimeout(function () {
            $('#flash-message-container').empty();
        }, 4000);
        e.stopImmediatePropagation();
        return false;
    }
    
    // var lastFileInput = fileInputContainer.find('.file-upload-row[data-index="' + lastIndex + '"] input[type="file"]');
    // var lastNameInput = $('#toprecruiters-' + lastIndex + '-recruiter_name');
    // var lastRowCollegeSelect = $('select[name="TopRecruiters[' + lastIndex + '][college_id]"]');
    // var lastImageTextInput = $('#toprecruiters-' + lastIndex + '-recruiter_image');

    // if (!lastNameInput.length || !lastFileInput.length || !lastRowCollegeSelect.length) return;

    // var nameVal = lastNameInput.val().trim();
    // var collegeVal = lastRowCollegeSelect.val();
    // var fileCount = lastFileInput[0].files.length;
    // var imageVal = lastImageTextInput.val().trim();

    // if (!collegeVal) {
    //     $('#flash-message-container').html(
    //         '<div class="flash-message">Please select a college for recruiter #' + (lastIndex + 1) + ' before adding a new one.</div>'
    //     );
    //     e.stopImmediatePropagation();
    //     return false;
    // }

    // if (!nameVal) {
    //     $('#flash-message-container').html(
    //         '<div class="flash-message">Please enter a name for recruiter #' + (lastIndex + 1) + ' before adding a new one.</div>'
    //     );
    //     e.stopImmediatePropagation();
    //     return false;
    // }

    // if (nameVal && fileCount === 0 && !imageVal) {
    //     $('#flash-message-container').html(
    //         '<div class="flash-message">Please upload an image for recruiter #' + (lastIndex + 1) + ' before adding a new one.</div>'
    //     );
    //     setTimeout(function () {
    //         $('#flash-message-container').empty();
    //     }, 4000);
    //     e.stopImmediatePropagation();
    //     return false;
    // }
});

$('#tabular-form').on('afterAddRow', function (e, row, index) {
    initializeSelect2(row);

    var fileRow = $('<div class="file-upload-row" data-index="' + index + '"></div>')
        .append('<input type="file" name="TopRecruiters[' + index + '][recruiter_image]" accept="image/*" />');
    fileInputContainer.append(fileRow);


    var nextIndex = index;

    var newVisibleInput = row.find('input.college-autocomplete');
    var newHiddenInput = row.find('input.college-hidden-input');

    newVisibleInput.attr('data-index', nextIndex);
    newHiddenInput.attr('data-index', nextIndex);
    newHiddenInput.attr('name', 'TopRecruiters[' + nextIndex + '][college_id]');
    newVisibleInput.attr('name', 'TopRecruiters[' + nextIndex + '][college_id]');


    // Pre-fill new college with first row value if present
    let firstHidden = $('input[name="TopRecruiters[0][college_id]"]').val();
    let firstText = $('input[name="TopRecruiters[0][college_id]"]').text() ?? '';

        console.log("New row newVisibleInput:", newVisibleInput);
    console.log("New row newHiddenInput:", newHiddenInput);
    console.log("New row firstHidden:", firstHidden);
    console.log("New row firstText:", firstText);

    if (firstHidden && firstText) {
        let input = row.find('.college-autocomplete');
        let hidden = row.find('.college-hidden-input');

        let option = new Option(firstText, firstHidden, true, true);
        input.append(option).trigger('change').prop('disabled', true);
        hidden.val(firstHidden);
    }

});
JS;
$this->registerJs($js);
?>