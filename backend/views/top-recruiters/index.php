<?php

use common\helpers\DataHelper;
use common\models\TopRecruiters;
use yii\helpers\Html;
use yii\grid\GridView;
use yii\helpers\ArrayHelper;

/* @var $this yii\web\View */
/* @var $searchModel backend\models\TopRecruitersSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Top Recruiters';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="top-recruiters-index box box-primary">
    <div class="box-header with-border">
        <?= Html::a('Create Top Recruiters', ['top-recruiters/tabular-form'], ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?php // echo $this->render('_search', ['model' => $searchModel]);
        ?>
        <?= GridView::widget([
            'dataProvider' => $dataProvider,
            'filterModel' => $searchModel,
            'layout' => "{items}\n{summary}\n{pager}",
            'columns' => [
                ['class' => 'yii\grid\SerialColumn'],

                [
                    'attribute' => 'college_name',
                    'value' => function ($model) {
                        return $model->college->name;
                    }
                ],
                'recruiter_name',
                [
                    'attribute' => 'status',
                    'value' => function ($model) {
                        return ArrayHelper::getValue(DataHelper::getConstantList('STATUS', TopRecruiters::class), $model->status);
                    },
                    'filter' => DataHelper::getConstantList('STATUS', TopRecruiters::class)
                ],

                [
                    'class' => 'yii\grid\ActionColumn',
                    'header' => 'Actions',
                    'template' => '{update}',
                    'urlCreator' => function ($action, $model, $key, $index) {
                        if ($action === 'update') {
                            $url = ['top-recruiters/tabular-form', 'college_id' => $model->college_id];
                            return $url;
                        }
                    }
                ],
            ],
        ]); ?>
    </div>
</div>