<?php

use yii\helpers\Html;
use yii\widgets\DetailView;
use yii\helpers\ArrayHelper;
use common\helpers\DataHelper;
use common\models\ArticleSubpageSection;

/* @var $this yii\web\View */
/* @var $model common\models\ArticleSubpageSection */

$this->title = $model->name;
$this->params['breadcrumbs'][] = ['label' => 'Article Subpage Sections', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
\yii\web\YiiAsset::register($this);
?>
<div class="article-subpage-section-view  box box-primary">
    <div class="box-header">
    <p>
        <?= Html::a('Update', ['update', 'id' => $model->id], ['class' => 'btn btn-primary']) ?>
    </p>
    <?= DetailView::widget([
        'model' => $model,
        'attributes' => [
            'id',
            'name',
            'slug',
            'article_id',
            'article_subpage_id',
            'topic_page_exam',
            'title',
            'h1:ntext',
            'meta_description:ntext',
            'description:ntext',
            'created_at',
            'updated_at',
            'status',
            [
                'label' => 'Status',
                'attribute' => 'status',
                'value' => ArrayHelper::getValue(DataHelper::getConstantList('STATUS', ArticleSubpageSection::class), $model->status)
            ],
        ],
    ]) ?>
    </div>
</div>
