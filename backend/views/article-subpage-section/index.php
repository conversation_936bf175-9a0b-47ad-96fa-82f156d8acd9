<?php

use yii\helpers\Html;
use yii\grid\GridView;
use yii\helpers\ArrayHelper;
use common\helpers\DataHelper;
use common\models\ArticleSubpageSection;

/* @var $this yii\web\View */
/* @var $searchModel common\models\ArticleSubpageSectionSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Article Subpage Sections';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="article-subpage-section-index box box-primary">
    <div class="box-body table-responsive no-padding">
    <?= GridView::widget([
        'dataProvider' => $dataProvider,
        'filterModel' => $searchModel,
        'columns' => [
            ['class' => 'yii\grid\SerialColumn'],
            'name',
            [
                'attribute' => 'slug',
                'label' => 'Slug',
                'filter'=>false,
                'value' => function ($model) {
                    return $model->slug;
                }
            ],
            [
                'attribute' => 'article_id',
                'label' => 'Article Name',
                'filter'=>false,
                'value' => function ($model) {
                    return $model->article->title;
                }
            ],
            [
                'attribute' => 'status',
                'value' => function ($model) {
                    return ArrayHelper::getValue(DataHelper::getConstantList('STATUS', ArticleSubpageSection::class), $model->status);
                },
                'filter' => DataHelper::getConstantList('STATUS', ArticleSubpageSection::class)
            ],
            ['class' => 'yii\grid\ActionColumn',
            'template' => '{view} {update}'
            ],
        ],
    ]); ?>
    </div>
</div>
