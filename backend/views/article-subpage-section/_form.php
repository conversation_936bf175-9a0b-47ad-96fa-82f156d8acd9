<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;
use common\helpers\DataHelper;
use common\models\ArticleSubpageSection;

/* @var $this yii\web\View */
/* @var $model common\models\ArticleSubpageSection */
/* @var $form yii\widgets\ActiveForm */
?>

<div class="article-subpage-section-form box box-primary">
<div class="box-body">
    <div class="row">
       <div class="col-md-6">
    <?php $form = ActiveForm::begin(); ?>
    <?= $form->field($model, 'name')->textInput(['maxlength' => true,'disabled'=>true]) ?>
    <?= $form->field($model, 'slug')->textInput(['maxlength' => true,'disabled'=>true]) ?>
    <?= $form->field($model, 'title')->textInput(['maxlength' => true,'disabled'=>true]) ?>
    <?= $form->field($model, 'meta_description')->textInput(['maxlength' => true]) ?>
    <?=
        $this->render('/widget/tinymce', [
            'form' => $form,
            'model' => $model,
            'entity' => 'description',
            'type' => 'article-section',
        ])
        ?>
      <?php if ($model->isNewRecord) {
                        echo $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', ArticleSubpageSection::class), ['options' => [2 => ['selected' => true]]]);
      } else {
          echo $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', ArticleSubpageSection::class));
      } ?>
    <div class="form-group">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success']) ?>
    </div>

    <?php ActiveForm::end(); ?>
       </div>
        </div>
</div>
</div>
