<?php

use common\helpers\DataHelper;
use common\models\Article;
use frontend\helpers\Url;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\widgets\DetailView;
use common\models\Degree;
use common\models\LeadBucket;
use common\models\LeadBucketTagging;
use common\models\User;

/* @var $this yii\web\View */
/* @var $model common\models\Article */

$degree = Degree::find()->where(['id' => (int)$model->highest_qualification])->one();
// dd($degree);
$this->title = $model->title;
$this->params['breadcrumbs'][] = ['label' => 'Articles', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="article-view box box-primary">
    <div class="box-header">
        <?= Html::a('Update', ['update', 'id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
        <!-- <?= Html::a('Delete', ['delete', 'id' => $model->id], [
                    'class' => 'btn btn-danger btn-flat',
                    'data' => [
                        'confirm' => 'Are you sure you want to delete this item?',
                        'method' => 'post',
                    ],
                ]) ?> -->
    </div>
    <div class="box-body table-responsive no-padding">
        <?= DetailView::widget([
            'model' => $model,
            'attributes' => [
                'id',
                'author.username',
                [
                    'attribute' => 'category_id',
                    'label' => 'Category',
                    'format' => 'raw',
                    'value' => ($model->category_id) ?   $model->category->slug : Html::tag('span', '(not set)', ['class' => 'not-set']) ,
                ],
                'country_slug',
                'title',
                [
                    'attribute' => 'display_name',
                    'label' => 'Display Name',
                    'format' => 'raw',
                    'value' => $model->display_name ?  $model->display_name : Html::tag('span', '(not set)', ['class' => 'not-set'])
                ],
                'slug',
                [
                    'label' => 'Cover Image',
                    'attribute' => 'cover_image',
                    'format' => 'html',
                    'value' => function ($model) {
                        if (!empty($model->cover_image)) {
                            return $model->entity != 'study-abroad' ? Html::img(Yii::getAlias('@articleGeneralFrontend') . '/' . $model->cover_image, ['width' => '250', 'height' => '150']) : Html::img(Yii::getAlias('@gmuAzureStudyAbroadUpload') . '/' . $model->cover_image, ['width' => '250', 'height' => '150']);
                        }
                    }
                ],
                [
                    'label' => 'Audio File',
                    'attribute' => 'audio',
                    'format' => 'raw',
                    'value' => function ($model) {
                        if (!empty($model->audio)) {
                            return "<audio controls='controls'>
                            <source src='" . $model->audio . "' type='audio/mpeg'>
                        </audio>";
                        }
                    }
                ],
                [
                    'label' => 'Document details',
                    'format' => 'raw',
                    'value' => html_entity_decode($model->description),
                ],
                'h1',
                [
                    'label' => 'Language Code',
                    'attribute' => 'lang_code',
                    'format' => 'html',
                    'value' => function ($model) {
                        if (!empty($model->lang_code)) {
                            return array_search($model->lang_code, DataHelper::$languageCode);
                        }
                    }
                ],
                'meta_description',
                'view_count',
                [
                    'label' => 'Is Popular',
                    'attribute' => 'is_popular',
                    'value' => ArrayHelper::getValue(DataHelper::getConstantList('POPULAR', Article::class), $model->is_popular)
                ],
                'position',
                [
                    'label' => 'Status',
                    'attribute' => 'status',
                    'value' => ArrayHelper::getValue(DataHelper::getConstantList('STATUS', Article::class), $model->status)
                ],
                [
                    'label' => 'Stream',
                    'attribute' => 'stream_id',
                    'value' =>  $model->stream->name ?? ''
                ],
                [
                    'label' => 'Level',
                    'attribute' => 'highest_qualification',
                    'value' =>  $degree->name ?? '',
                ],
                [
                    'label' => 'CTA Bucket',
                    'attribute' => 'bucket_name',
                    'value' =>  function ($model) {
                        if (!empty($model->id)) {
                            $bucketid = LeadBucketTagging::find()->select(['id', 'bucket_id'])->where(['article_id' => $model->id, 'status' => LeadBucketTagging::STATUS_ACTIVE])->one();
                            if (!empty($bucketid)) {
                                $bucketname = LeadBucket::find()->where(['id' => $bucketid->bucket_id])->active()->one();
                            }
                        }
                        return $bucketname->bucket ?? '';
                    },
                ],
                'updated_by' => [
                    'attribute' => 'updated_by',
                    'value' => function ($model) {
                        if (!empty($model->updated_by)) {
                            $user =  User::find()->select('name')->where(['id' => $model->updated_by])->one();
                            return $user->name ?? '';
                        } else {
                            return '-';
                        }
                    },
                    'label' => 'Updated By'
                ],
                'published_at:datetime',
                'scheduled_at:datetime',
                'created_at:datetime',
                'updated_at:datetime',
            ],
        ]) ?>
    </div>
</div>