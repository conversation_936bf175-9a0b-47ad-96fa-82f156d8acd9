<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;
use yii\helpers\ArrayHelper;
use vova07\imperavi\Widget;
use common\services\UserService;
use kartik\select2\Select2;
use common\helpers\DataHelper;
use common\models\Category;
use common\models\Article;
use common\models\Board;
use common\models\College;
use common\models\Country;
use common\models\Stream;
use common\models\Course;
use common\models\Degree;
use common\models\Exam;
use common\models\News;
use common\models\User;
use common\models\LeadBucket;
use common\models\LeadBucketTagging;
use dosamigos\selectize\SelectizeDropDownList;
use dosamigos\tinymce\TinyMce;
use frontend\helpers\Url;
use kartik\datetime\DateTimePicker;
use kartik\depdrop\DepDrop;
use yii\web\JsExpression;
use common\helpers\ContentHelper;

$model->description = ContentHelper::removeStyleTag(stripslashes(html_entity_decode($model->description)));
/* @var $this yii\web\View */
/* @var $model common\models\Article */
/* @var $form yii\widgets\ActiveForm */
foreach ($model->news as $value) {
    $newsData[] = ArrayHelper::map(News::find()->where(['id' => $value->id])->all(), 'id', 'name');
}

foreach ($model->college as $value) {
    $collegeData[] = ArrayHelper::map(College::find()->where(['id' => $value->id])->all(), 'id', 'name');
}

foreach ($model->exam as $value) {
    $examData[] = ArrayHelper::map(Exam::find()->where(['id' => $value->id])->all(), 'id', 'name');
}

foreach ($model->board as $value) {
    $boardData[] = ArrayHelper::map(Board::find()->where(['id' => $value->id])->all(), 'id', 'name');
}

foreach ($model->article as $value) {
    $articleData[] = ArrayHelper::map(Article::find()->where(['id' => $value->id])->all(), 'id', 'title');
}

foreach ($model->translation as $value) {
    $translationData[] = ArrayHelper::map(Article::find()->where(['id' => $value->id])->all(), 'id', 'title');
}

foreach ($model->course as $value) {
    $courseData[] = ArrayHelper::map(Course::find()->where(['id' => $value->id])->all(), 'id', 'name');
}
if (isset($model->stream_id) && !empty($model->stream_id) &&  !empty($model->stream->id)) {
    $streamData[] = ArrayHelper::map(Stream::find()->where(['id' => $model->stream->id])->all(), 'id', 'name');
}
if (!empty($model->author_id)) {
    $user =  ArrayHelper::map(User::find()->where(['id' => $model->author_id])->all(), 'id', 'name');
}
if (!empty($model->id)) {
    $bucketname = ArrayHelper::map(LeadBucketTagging::find()->select(['id', 'bucket_id'])->where(['article_id' => $model->id, 'status' => LeadBucketTagging::STATUS_ACTIVE])->all(), 'id', 'bucket_id');
}
?>

<div class="article-form box box-primary">
    <?=
    $this->render('/partials/_user-list');
    ?>
    <?php $form = ActiveForm::begin(['options' => ['enctype' => 'multipart/form-data']]); ?>
    <div class="box-body">
        <div class="row">
            <div class="col-md-4">
                <?= $form->field($model, 'author_id')->widget(Select2::classname(), [
                    'data' => $user ?? [], //array of text to show in the field for the selected items
                    'options' => [
                        'placeholder' => '--Select--',
                        'options' => $selectedValueTranslation ?? [],
                    ],
                    'pluginOptions' => [
                        'allowClear' => true,
                        'minimumInputLength' => 3,
                        'maximumInputLength' => 10,
                        'language' => [
                            'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                        ],
                        'ajax' => [
                            'url' => ['../ajax/all-users'],
                            'dataType' => 'json',
                            'data' => new JsExpression('function(params) {return {q:params.term}; }')
                        ],
                        'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                        'templateResult' => new JsExpression('function(data) { return data.text; }'),
                        'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                    ],
                ])->label('Author ID');
?>
            </div>
            <div class="col-md-4">
                <?php echo $form->field($model, 'is_freelancer')->dropDownList([Article::IS_FREELANCER_NO => 'No', Article::IS_FREELANCER_YES => 'Yes'], ['options' => [$model->is_freelancer => ['selected' => true]],'disabled'=> $model->is_freelancer ? true: false]); ?>
            </div>
            <input type="hidden" value="<?= $model->is_freelancer; ?>" name="update-validation" id="update-validation">
            <div class="col-md-4">
                <?= $form->field($model, 'entity')->dropDownList(
                    DataHelper::getConstantList('ENTITY', Article::class),
                    [
                        'id' => 'article-entity-id',
                        'onchange' => 'var studyAbroad = this.value;
                        if(this.value == studyAbroad) {
                            $("#field-country-id").attr("disabled", false);
                        } else {
                            $("#field-country-id").attr("disabled", true);
                        }',
                        'prompt' => '-- Select Page Type --'
                    ]
                )->label('Page Type') ?>
            </div>

            <div class="col-md-6">
                <?= $form->field($model, 'category_id')->dropDownList(
                    ArrayHelper::map(Category::find()->all(), 'id', 'name'),
                    [
                        'prompt' => '-- select category --'
                    ]
                ) ?>
            </div>

            <div class="col-md-6">
                <?= $form->field($model, 'country_slug')->widget(DepDrop::class, [
                    'type' => DepDrop::TYPE_SELECT2,
                    'options' => [
                        'id' => 'country-id',
                        'placeholder' => '--Select Country--',
                        'multiple' => false,
                    ],
                    'select2Options' => ['pluginOptions' => [
                        'allowClear' => true,
                        'disabled' => true,
                    ]],
                    'pluginOptions' => [
                        'depends' => ['article-entity-id'],
                        'placeholder' => 'Select Country...',
                        'url' => Url::to(['/ajax/get-country'])
                    ],

                ])->label('Country');
?>
            </div>
            <div class="col-md-6">
                <?= $form->field($model, 'title')->textInput(['maxlength' => true]) ?>
            </div>

            <?php
            $selectedValue = [];
            foreach ($model->tags as $tag) {
                $selectedValue[$tag->id] = ['selected' => true];
            }
            ?>

            <div class="col-md-6">
                <?= $form->field($model, 'tagNames')->widget(SelectizeDropDownList::class, [
                    'loadUrl' => ['ajax/tag-list'],
                    'items' => ArrayHelper::map($model->tags, 'id', 'name'),
                    'options' => [
                        'class' => 'form-control',
                        'multiple' => true,
                        'options' => $selectedValue
                    ],
                    'clientOptions' => [
                        'plugins' => ['remove_button'],
                        'valueField' => 'id',
                        'labelField' => 'name',
                        'searchField' => ['name'],
                        'create' => false,
                        'maxItems' => 5
                    ],
                ])->hint('You can select upto 5 Tags');  ?>
            </div>

            <div class="col-md-6">
                <?= $form->field($model, 'display_name')->textInput(['maxlength' => true]) ?>
            </div>

            <div class="col-md-6">
                <?php if ((!$model->isNewRecord) && isset($model->status) && ($model->status == Article::STATUS_DRAFT  || $model->status == Article::STATUS_INACTIVE)  &&  empty($model->published_at)) { ?>
                    <div class="col-md-12">
                        <?= $form->field($model, 'slug')->textInput(['maxlength' => true]) ?>
                    </div>
                <?php } else { ?>
                    <div class="col-md-12">
                        <?= $form->field($model, 'slug')->textInput(['maxlength' => true, 'disabled' => true]) ?>
                    </div>
                <?php } ?>
            </div>
        </div>

        <?=
        $this->render('/widget/tinymce', [
            'form' => $form,
            'model' => $model,
            'type' => Article::ENTITY_ARTICLE
        ])
        ?>
        <div class="row">
            <div class="col-md-4">
                <?= $form->field($model, 'h1')->textInput(['maxlength' => true]) ?>
            </div>
            <div class="col-md-4">
                <?= $form->field($model, 'lang_code')->dropDownList(
                    array_flip(DataHelper::$languageCode),
                    [
                        'onChange' => 'getSlugTitle(' . $model->isNewRecord . ')',
                    ]
                )->label('Language code') ?>
            </div>
            <div class="col-md-4">
                <?= $form->field($model, 'slug_title')->textInput(['maxlength' => true, 'disabled' => true]) ?>
            </div>
        </div>

        <?= $form->field($model, 'meta_description')->textInput(['maxlength' => true]) ?>
        <div class="row">
            <div class="col-md-6">
                <?php
                if (!empty($model->cover_image)) {
                    echo $model->entity == 'study-abroad' ? Html::img(Yii::getAlias('@gmuAzureStudyAbroadUpload') . '/' . $model->cover_image, ['width' => '250', 'height' => '150']) : Html::img(Yii::getAlias('@articleGeneralFrontend') . '/' . $model->cover_image, ['width' => '250', 'height' => '150']);
                }
                ?>
                <?= $form->field($model, 'cover_image')->fileInput() ?>
            </div>
            <div class="col-md-6">
                <?php
                if (!empty($model->audio)) {
                    ?>
                    <audio controls id="myAudio" class="center" preload="none">
                        <source src="<?php echo $model->audio ?>" type="audio/mpeg">
                        Your browser does not support the audio element.
                    </audio>
                    <?php
                }
                ?>
                <?= $form->field($model, 'audio')->fileInput() ?>
            </div>
        </div>
        <div class="row">
            <?php
            if (!$model->isNewRecord) {
                $selectedValueTranslation = [];
                if (!empty($translationData)) {
                    foreach ($translationData as $value) {
                        if (!empty($value)) {
                            $selectedValueTranslation[array_keys($value)[0]] = ['selected' => true];
                        }
                    }
                }
            }
            ?>
            <div class="col-md-12">
                <?= $form->field($model, 'translation[]')->widget(Select2::classname(), [
                    'data' => $translationData ?? [], //array of text to show in the field for the selected items
                    'options' => [
                        'placeholder' => '--Select--',
                        'multiple' => true,
                        'options' => $selectedValueTranslation ?? [],
                    ],
                    'pluginOptions' => [
                        'allowClear' => true,
                        'maximumInputLength' => 10,
                        'language' => [
                            'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                        ],
                        'ajax' => [
                            'url' => ['../ajax/translated-article-list'],
                            'dataType' => 'json',
                            'data' => new JsExpression('function(params) {return {q:params.term}; }')
                        ],
                        'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                        'templateResult' => new JsExpression('function(data) { return data.text; }'),
                        'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                    ],
                ])->label('Article Translation');
?>

            </div>
            <?php
            if (!$model->isNewRecord) {
                $selectedValueStream = [];
                if (!empty($streamData)) {
                    foreach ($streamData as $value) {
                        if (!empty($value)) {
                            $selectedValueStream[array_keys($value)[0]] = ['selected' => true];
                        }
                    }
                }
            }
            ?>
            <div class="col-md-6">
                <?= $form->field($model, 'stream_id')->widget(Select2::classname(), [
                    'data' => $streamData ?? [], //array of text to show in the field for the selected items
                    'options' => [
                        'placeholder' => '--Select--',
                        'multiple' => false,
                        'options' => $selectedValueStream ?? [],
                    ],
                    'pluginOptions' => [
                        'allowClear' => true,
                        'maximumInputLength' => 3,
                        'language' => [
                            'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                        ],
                        'ajax' => [
                            'url' => ['../ajax/stream-list'],
                            'dataType' => 'json',
                            'data' => new JsExpression('function(params) {return {query:params.term}; }')
                        ],
                        'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                        'templateResult' => new JsExpression('function(data) { return data.text; }'),
                        'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                    ],
                ])->label('Preferred Stream');
?>

            </div>

            <div class="col-md-6">
                <?= $form->field($model, 'highest_qualification')->dropDownList(
                    ArrayHelper::map(Degree::find()->all(), 'id', 'name'),
                    [
                        'prompt' => '-- Select Level --'
                    ]
                )->label('Preferred Level') ?>
            </div>
            <div class="col-md-6">
                <?= $form->field($model, 'view_count')->textInput(['value' => 0]) ?>
            </div>
            <div class="col-md-6">
                <?= $form->field($model, 'position')->textInput(['value' => 0]) ?>
            </div>
            <?php if (UserService::hasSeoRole()) { ?>
                <div class="col-md-6">
                    <?= $form->field($model, 'is_popular')->dropDownList(DataHelper::getConstantList('POPULAR', Article::class)) ?>
                </div>
                <div class="col-md-6">
                    <?php if ($model->isNewRecord) {
                        if (UserService::hasNewsArticleInactiveRole()) {
                            echo $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', Article::class));
                        } else {
                            $articleStatus[Article::STATUS_DRAFT] = 'Draft';
                            $articleStatus[Article::STATUS_ACTIVE] = 'Active';
                            echo $form->field($model, 'status')->dropDownList($articleStatus, ['options' => [0 => ['selected' => true]]]);
                        }
                    } else {
                        if (UserService::hasNewsArticleInactiveRole()) {
                            echo $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', Article::class));
                        } else {
                            if ($model->status == Article::STATUS_INACTIVE) {
                                echo $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', Article::class));
                            } else {
                                echo $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', Article::class), ['options' => [0 => ['disabled' => true]]]);
                            }
                          //  echo $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', Article::class), ['options' => [0 => ['disabled' => true]]]);
                        }
                    } ?>
                </div>
            <?php } ?>

            <div class="col-md-6">
                <?= $form->field($model, 'published_at')->textInput(['value' => !empty($model->published_at) ? Yii::$app->formatter->asDatetime($model->published_at, 'php:F j, Y H:i:s') : '', 'disabled' => true]); ?>
            </div>

            <div class="col-md-6">
                <?= $form->field($model, 'scheduled_at')->widget(
                    DateTimePicker::class,
                    [
                        'options' => ['placeholder' => 'Select Date'],
                        'pluginOptions' => [
                            'timePicker' => true,
                            'locale' => [
                                'format' => 'yyyy-mm-dd HH:ii',
                            ],
                            'autoclose' => true,
                            'todayHighlight' => true,
                        ],
                    ]
                );
?>
            </div>

            <?php
            if (!$model->isNewRecord) {
                $selectedValue = [];
                if (!empty($newsData)) {
                    foreach ($newsData as $value) {
                        if (!empty($value)) {
                            $selectedValue[array_keys($value)[0]] = ['selected' => true];
                        }
                    }
                }
            }
            ?>
            <?php if (Yii::$app->user->can('Seo-Tag-Permission')): ?>
                <div class="col-md-12">

                    <?= $form->field($model, 'news[]')->widget(Select2::classname(), [
                        'data' => $newsData ?? [], //array of text to show in the field for the selected items
                        'options' => [
                            'placeholder' => '--Select--',
                            'multiple' => true,
                            'options' => $selectedValue ?? [],
                        ],
                        'pluginOptions' => [
                            'allowClear' => true,
                            'minimumInputLength' => 3,
                            'maximumInputLength' => 10,
                            'language' => [
                                'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                            ],
                            'ajax' => [
                                'url' => ['../ajax/news-list'],
                                'dataType' => 'json',
                                'data' => new JsExpression('function(params) {return {q:params.term}; }')
                            ],
                            'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                            'templateResult' => new JsExpression('function(data) { return data.text; }'),
                            'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                        ],
                    ])->label('News Name');
                    ?>
                </div>
                <?php
                if (!$model->isNewRecord) {
                    $selectedValueCollege = [];
                    if (!empty($collegeData)) {
                        foreach ($collegeData as $value) {
                            if (!empty($value)) {
                                $selectedValueCollege[array_keys($value)[0]] = ['selected' => true];
                            }
                        }
                    }
                }
                ?>
                <div class="col-md-12">

                    <?= $form->field($model, 'college[]')->widget(Select2::classname(), [
                        'data' => $collegeData ?? [], //array of text to show in the field for the selected items
                        'options' => [
                            'placeholder' => '--Select--',
                            'multiple' => true,
                            'options' => $selectedValueCollege ?? [],
                        ],
                        'pluginOptions' => [
                            'allowClear' => true,
                            'minimumInputLength' => 3,
                            'language' => [
                                'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                            ],
                            'ajax' => [
                                'url' => ['../ajax/college-list'],
                                'dataType' => 'json',
                                'data' => new JsExpression('function(params) {return {q:params.term}; }')
                            ],
                            'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                            'templateResult' => new JsExpression('function(data) { return data.text; }'),
                            'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                        ],
                    ])->label('College Name');
                    ?>
                </div>
                <?php
                if (!$model->isNewRecord) {
                    $selectedValueExam = [];
                    if (!empty($examData)) {
                        foreach ($examData as $value) {
                            if (!empty($value)) {
                                $selectedValueExam[array_keys($value)[0]] = ['selected' => true];
                            }
                        }
                    }
                }
                ?>
                <div class="col-md-12">
                    <?= $form->field($model, 'exam[]')->widget(Select2::classname(), [
                        'data' => $examData ?? [], //array of text to show in the field for the selected items
                        'options' => [
                            'placeholder' => '--Select--',
                            'multiple' => true,
                            'options' => $selectedValueExam ?? [],
                        ],
                        'pluginOptions' => [
                            'allowClear' => true,
                            'maximumInputLength' => 10,
                            'language' => [
                                'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                            ],
                            'ajax' => [
                                'url' => ['../ajax/exam-list'],
                                'dataType' => 'json',
                                'data' => new JsExpression('function(params) {return {query:params.term}; }')
                            ],
                            'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                            'templateResult' => new JsExpression('function(data) { return data.text; }'),
                            'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                        ],
                    ])->label('Exam Name');
                    ?>
                </div>
                <?php
                if (!$model->isNewRecord) {
                    $selectedValueBoard = [];
                    if (!empty($boardData)) {
                        foreach ($boardData as $value) {
                            if (!empty($value)) {
                                $selectedValueBoard[array_keys($value)[0]] = ['selected' => true];
                            }
                        }
                    }
                }
                ?>
                <div class="col-md-12">
                    <?= $form->field($model, 'board[]')->widget(Select2::classname(), [
                        'data' => $boardData ?? [], //array of text to show in the field for the selected items
                        'options' => [
                            'placeholder' => '--Select--',
                            'multiple' => true,
                            'options' => $selectedValueBoard ?? [],
                        ],
                        'pluginOptions' => [
                            'allowClear' => true,
                            'maximumInputLength' => 10,
                            'language' => [
                                'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                            ],
                            'ajax' => [
                                'url' => ['../ajax/board-list'],
                                'dataType' => 'json',
                                'data' => new JsExpression('function(params) {return {q:params.term}; }')
                            ],
                            'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                            'templateResult' => new JsExpression('function(data) { return data.text; }'),
                            'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                        ],
                    ])->label('Board Name');
                    ?>
                </div>
                <?php
                if (!$model->isNewRecord) {
                    $selectedArticleValue = [];
                    if (!empty($articleData)) {
                        foreach ($articleData as $value) {
                            if (!empty($value)) {
                                $selectedArticleValue[array_keys($value)[0]] = ['selected' => true];
                            }
                        }
                    }
                }
                ?>
                <div class="col-md-12">
                    <?= $form->field($model, 'article[]')->widget(Select2::classname(), [
                        'data' => $articleData ?? [], //array of text to show in the field for the selected items
                        'options' => [
                            'placeholder' => '--Select--',
                            'multiple' => true,
                            'options' => $selectedArticleValue ?? [],
                        ],
                        'pluginOptions' => [
                            'allowClear' => true,
                            'maximumInputLength' => 10,
                            'language' => [
                                'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                            ],
                            'ajax' => [
                                'url' => ['../ajax/article-list'],
                                'dataType' => 'json',
                                'data' => new JsExpression('function(params) {return {q:params.term}; }')
                            ],
                            'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                            'templateResult' => new JsExpression('function(data) { return data.text; }'),
                            'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                        ],
                    ])->label('Article Name');
                    ?>
                </div>
                <?php
                if (!$model->isNewRecord) {
                    $selectedValueCourse = [];
                    if (!empty($courseData)) {
                        foreach ($courseData as $value) {
                            if (!empty($value)) {
                                $selectedValueCourse[array_keys($value)[0]] = ['selected' => true];
                            }
                        }
                    }
                }
                ?>
                <div class="col-md-12">
                    <?= $form->field($model, 'course[]')->widget(Select2::classname(), [
                        'data' => $courseData ?? [], //array of text to show in the field for the selected items
                        'options' => [
                            'placeholder' => '--Select--',
                            'multiple' => true,
                            'options' => $selectedValueCourse ?? [],
                        ],
                        'pluginOptions' => [
                            'allowClear' => true,
                            'maximumInputLength' => 10,
                            'language' => [
                                'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                            ],
                            'ajax' => [
                                'url' => ['../ajax/all-course'],
                                'dataType' => 'json',
                                'data' => new JsExpression('function(params) {return {q:params.term}; }')
                            ],
                            'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                            'templateResult' => new JsExpression('function(data) { return data.text; }'),
                            'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                        ],
                    ])->label('Course Name');
                    ?>
                    <input type="hidden" name="autoSaveId" id="article-auto-save-id" value="<?= $model->id ?>" />
                    <?=
                    $this->render('/widget/tinymce', [
                        'form' => $form,
                        'model' => $model,
                        'type' => Article::ENTITY_ARTICLE,
                        'entity' => 'editor_remark'
                    ])
                    ?>
                </div>
            <?php endif; ?>

            <div class="col-md-12">
                <?= $form->field($model, 'bucket_id')->widget(Select2::class, [
                    'data' => ArrayHelper::map(LeadBucket::find()->active()->where(['entity_id' => LeadBucket::LEAD_ENTITY_NEWS_AND_ARTICLE])->all(), 'id', 'bucket'),
                    'language' => 'en',
                    'options' => ['placeholder' => '--Select Bucket--', 'value' => $bucketname ?? []],
                    'pluginOptions' => [
                        'allowClear' => true
                    ],
                ])->label('CTA Bucket'); ?>
            </div>

            <div class="col-md-12 box-footer">
                <?= Html::submitButton('Save', ['class' => 'btn btn-success btn-flat']) ?>
            </div>
            <?php ActiveForm::end(); ?>
        </div>
        <?php $this->registerJsFile(Yii::$app->request->baseUrl . '/js/auto-save.js', ['depends' => [\yii\web\JqueryAsset::className()]]); ?>
        <script>
            window.onload = function() {
                getSlugTitle();
            };

            function getSlugTitle(isNewRecord) {
                var lang_value = document.getElementById('article-lang_code');
                if (lang_value.value == 1) {
                    $('#article-slug_title').attr('disabled', true);
                } else {
                    if (isNewRecord) {
                        $('#article-slug_title').removeAttr('disabled');
                    }
                }
            }
        </script>
        <?=
        $this->render('../partials/_socket-io-client', [
            'model' => $model,
            'entity' => 'article'
        ]);
        ?>
        