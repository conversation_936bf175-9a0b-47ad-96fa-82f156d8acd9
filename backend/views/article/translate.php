<?php

use yii\helpers\Html;
use common\helpers\ContentHelper;
use common\helpers\DataHelper;
use common\helpers\ArticleDataHelper;
use frontend\helpers\Url;
use backend\assets\AppAsset;
use yii\widgets\ActiveForm;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model common\models\Article */
// dd($model);


$this->title = $model->title;
// $this->params['pageTitle'] = $model->title;
Yii::$app->params['previewDescription'] =  $model->title;
$this->params['breadcrumbs'][] = ['label' => 'Article', 'url' => ['index']];
// $this->params['breadcrumbs'][] = $this->title;
$this->registerCssFile((rtrim(Url::toGetmyuni(), '/')) . Yii::$app->params['cssPath'] . 'article-detail.css', ['depends' => [AppAsset::class]]);
?>
<div class="container">
    <div class="row">
        <div class="col-md-12">
            <div class="articleHeader">
                <section class="pageDescription">
                <div class="box-body table-responsive no-padding">
                    <?= DetailView::widget([
                        'model' => $model,
                        'attributes' => [
                            'title',
                            [
                                'label' => 'Description',
                                'format' => 'raw',
                                'value' => html_entity_decode($model->description),
                            ],
                            'h1',
                            'meta_description'
                        ],
                    ]) ?>
                </div>
                    <div class="row">
                        <div class="col-md-12">
                        <?php $form = ActiveForm::begin(['id' => 'submitFormTranslate','options' => ['enctype' => 'multipart/form-data']]); ?>
                        <input type="hidden" name="article_id" value="<?= $model->id?>" class="article_id">
                        <div class="box-body">
                            <div class="row">
                                 <div class="col-md-6">
                                 <label class="control-label" for="">Please select language</label>
                                   <?= Html::dropDownList('translate', null, (DataHelper::$languageCodeReverse), ['class' => 'form-control']); ?>
                                 </div>
                            </div>
                        </div>
                        <div class="col-md-1 box-footer">
                          <?= Html::submitButton('Save', ['class' => 'btn btn-success btn-flat submitFormTranslate']) ?>
                        </div>
                        <?php ActiveForm::end(); ?>
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </div>
</div>
<div class="modal" tabindex="-1" role="dialog" id="article-translation">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Vernacular Linking and Translation</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close" data-bs-dismiss="modal">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <p class="translate-message">Article translation is in progress, it may take couple of minutes.</p>
      </div>
    </div>
  </div>
</div>
