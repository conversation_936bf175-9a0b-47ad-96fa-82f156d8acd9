<?php
use yii\helpers\Html;
use yii\grid\GridView;
use common\helpers\DataHelper;
use common\models\Article;
use common\models\Category;
use common\models\Faq;
use common\models\User;
use kartik\date\DatePicker;
use yii\helpers\ArrayHelper;
use yii\helpers\Url;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $searchModel backend\models\ArticleSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Articles';
$this->params['breadcrumbs'][] = $this->title;

?>
<div class="article-index box box-primary">
    <div class="box-header with-border" style="display: flex;">
        <?= Html::a('Create Article', ['create'], [
            'class' => 'btn btn-success btn-flat',
            'style' => 'margin-right:auto;',
        ]) ?>
        <?php $form = ActiveForm::begin([
            'method' => 'get'
        ]); ?>

        <?php if (Yii::$app->user->can(Yii::$app->params['articleExport'])): ?>
            <?= Html::submitButton('Export to CSV', [
                'class' => 'btn btn-success',
                'name' => 'export',
                'formaction' => Url::to(['article/export']), // URL for the export action
            ]); ?>
        <?php endif; ?>
        <!-- Export button -->

        <?php ActiveForm::end(); ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?php // echo $this->render('_search', ['model' => $searchModel]);
        ?>
        <?= GridView::widget([
            'dataProvider' => $dataProvider,
            'filterModel' => $searchModel,
            'layout' => "{items}\n{summary}\n{pager}",
            'rowOptions' => function ($model, $key, $index, $widget) {
                if (!empty($model->articleSection)) {
                    return ['style' => 'background-color: #90EE90'];
                }
            },
            'columns' => [
                [
                'attribute'=>'id',
                'filter' => false,
                'label' => 'Article ID',
                'value' => function ($model) {
                    return $model->id;
                }
                ],
                'h1',
                'title',
                'slug',
                [
                    'attribute' => 'article_name',
                    'label' => 'Author',
                    'value' => function ($model) {
                        return $model->backendauthor->name ?? '' ;
                    }
                ],
                [
                    'attribute' => 'category_id',
                    'value' => 'category.name',
                    'filter' => ArrayHelper::map(Category::find()->asArray()->all(), 'id', 'name')
                ],
                [
                    'attribute' => 'is_popular',
                    'value' => function ($model) {
                        return ArrayHelper::getValue(DataHelper::getConstantList('POPULAR', Article::class), $model->is_popular);
                    },
                    'filter' => DataHelper::getConstantList('POPULAR', Article::class)
                ],
                [
                    'attribute' => 'lang_code',
                    'value' => function ($model) {
                        return ArrayHelper::getValue(DataHelper::getConstantList('LANG', Article::class), $model->lang_code);
                    },
                    'filter' => DataHelper::getConstantList('LANG', Article::class)
                ],
                [
                    'attribute' => 'status',
                    'value' => function ($model) {
                        return ArrayHelper::getValue(DataHelper::getConstantList('STATUS', Article::class), $model->status);
                    },
                    'filter' => DataHelper::getConstantList('STATUS', Article::class)
                ],
                'updated_by' => [
                    'attribute' => 'updated_by',
                    'value' => function ($model) {
                        if (!empty($model->updated_by)) {
                            $user =  User::find()->select('name')->where(['id' => $model->updated_by])->one();
                            return $user->name ?? '';
                        } else {
                            return '-';
                        }
                    },
                    'label' => 'Updated By'
                ],
                [
                    'attribute' => 'published_at',
                    'format' => 'datetime',
                    'filter' => DatePicker::widget([
                        'model' => $searchModel,
                        'attribute' => 'published_at',
                        'type' => DatePicker::TYPE_COMPONENT_APPEND,
                        'pluginOptions' => [
                            'format' => 'yyyy-mm-dd',
                            'showMeridian' => true,
                            'todayBtn' => true,
                            'endDate' => '0d',
                        ]
                    ]),
                ],

                [
                    'attribute' => 'updated_at',
                    'format' => 'datetime',
                    'filter' => DatePicker::widget([
                        'model' => $searchModel,
                        'attribute' => 'updated_at',
                        'type' => DatePicker::TYPE_COMPONENT_APPEND,
                        'pluginOptions' => [
                            'format' => 'yyyy-mm-dd',
                            'showMeridian' => true,
                            'todayBtn' => true,
                            'endDate' => '0d',
                        ]
                    ]),
                ],
                [
                    'attribute' => 'word_count',
                    'value' => function ($model) {
                        $words = preg_split('/\s+/', preg_replace('/[^\p{L}\p{N}\s]+/u', '', strip_tags($model->description)));
                        $word_count = count($words);
                        return $word_count;
                    },
                ],

                [
                    'class' => 'yii\grid\ActionColumn',
                    'template' => '{view} {update} {preview} {faq} {translate}',
                    'buttons' => [
                        'preview' => function ($url, $model, $key) {
                            return Html::a('Preview', $url, [
                                'title' => Yii::t('app', 'Preview'),
                                'class' => 'btn btn-sm btn-primary'
                            ]);
                        },
                        'faq' => function ($url, $model, $key) {
                            $faqId = Faq::find()->select('id')->where(['entity_id' => $model->id])->andWhere(['entity' => 'articles'])->one();
                            if (!empty($faqId)) {
                                $url = '/faq/update?id=' . $faqId->id;
                            } else {
                                $url = '/faq/create';
                            }
                            return Html::a('FAQ', $url, [
                                'title' => Yii::t('app', 'FAQ'),
                                'class' => 'btn btn-sm btn-primary',
                                // 'style' => 'margin-left:12px',
                                'target' => '_blank'
                            ]);
                        },
                        // 'logs' => function ($url, $model, $key) {
                        //     return Html::a('Logs', $url, [
                        //         'title' => Yii::t('app', 'Logs'),
                        //         'class' => 'btn btn-sm btn-primary'
                        //     ]);
                        // },
                        'translate' => function ($url, $model, $key) {
                            if ($model->lang_code=='1') {
                                return Html::a('Translate', $url, [
                                    'title' => Yii::t('app', 'Translate'),
                                    'class' => 'btn btn-sm btn-primary'
                                ]);
                            }
                        }
                    ],
                ],
            ],
        ]); ?>
    </div>
</div>