<?php

use common\helpers\DataHelper;
use common\models\GetgisArticle;
use kartik\date\DatePicker;
use kartik\select2\Select2;
use yii\helpers\Html;
use yii\grid\GridView;
use yii\helpers\ArrayHelper;

/* @var $this yii\web\View */
/* @var $searchModel backend\models\GetgisArticleSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Getgis Articles';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="getgis-article-index box box-primary">
    <div class="box-header with-border">
        <?= Html::a('Create Getgis Article', ['create'], ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <div class="box-body table-responsive no-padding">

        <?= GridView::widget([
            'dataProvider' => $dataProvider,
            'filterModel' => $searchModel,
            'layout' => "{items}\n{summary}\n{pager}",
            'columns' => [
                ['class' => 'yii\grid\SerialColumn'],
                [
                    'attribute' => 'author_id',
                    'label' => 'Author',
                    'value' => function ($model) {
                        return $model->backendauthor->name;
                    }
                ],
                [
                    'attribute' => 'category_id',
                    'options' => ['style' => 'width: 10%'],
                    'filter' => Select2::widget([
                        'model' => $searchModel,
                        'attribute' => 'category_id',
                        'data' => $categories,
                        'options' => [
                            'placeholder' => 'All',
                            'multiple' => false,
                        ],
                        'pluginOptions' => [
                            'allowClear' => true,
                        ],
                    ]),
                    'value' => function ($model) use ($categories) {
                        return $categories[$model->category_id] ?? '-';
                    },
                ],
                'title',
                'slug',
                // 'h1',
                [
                    'attribute' => 'is_popular',
                    'value' => function ($model) {
                        return ArrayHelper::getValue(DataHelper::getConstantList('POPULAR', GetgisArticle::class), $model->is_popular);
                    },
                    'filter' => DataHelper::getConstantList('POPULAR', GetgisArticle::class)
                ],
                [
                    'attribute' => 'is_published',
                    'value' => function ($model) {
                        return ArrayHelper::getValue(DataHelper::getConstantList('PUBLISHED', GetgisArticle::class), $model->is_published);
                    },
                    'filter' => DataHelper::getConstantList('PUBLISHED', GetgisArticle::class)
                ],
                [
                    'attribute' => 'status',
                    'value' => function ($model) {
                        return ArrayHelper::getValue(DataHelper::getConstantList('STATUS', GetgisArticle::class), $model->status);
                    },
                    'filter' => DataHelper::getConstantList('STATUS', GetgisArticle::class)
                ],
                [
                    'attribute' => 'created_at',
                    'format' => ['date', 'php:Y-m-d'],
                    'filter' => DatePicker::widget([
                        'model' => $searchModel,
                        'attribute' => 'created_at',
                        'type' => DatePicker::TYPE_COMPONENT_APPEND,
                        'pluginOptions' => [
                            'format' => 'yyyy-mm-dd',
                            'todayHighlight' => true,
                            'endDate' => '0d',
                        ],
                    ]),
                ],
                [
                    'attribute' => 'updated_at',
                    'format' => ['date', 'php:Y-m-d'],
                    'filter' => DatePicker::widget([
                        'model' => $searchModel,
                        'attribute' => 'updated_at',
                        'type' => DatePicker::TYPE_COMPONENT_APPEND,
                        'pluginOptions' => [
                            'format' => 'yyyy-mm-dd',
                            'todayHighlight' => true,
                            'endDate' => '0d',
                        ],
                    ]),
                ],
                [
                    'class' => 'yii\grid\ActionColumn',
                    'template' => '{view}&nbsp;&nbsp;&nbsp;{update}&nbsp;{preview}',
                    'header' => '<a href="#">Actions</a>',
                    'buttons' => [
                        'preview' => function ($url, $model, $key) {
                            return Html::a('Preview', $url, [
                                'title' => Yii::t('app', 'Preview'),
                                'class' => 'btn btn-sm btn-primary',
                                'style' => 'margin-left:12px',
                                'target' => '_blank'
                            ]);
                        },
                    ],
                ],
            ]
        ]); ?>
    </div>
</div>