<?php

use common\helpers\ContentHelper;
use common\helpers\ArticleDataHelper;
use frontend\helpers\Url;
use backend\assets\AppAsset;

/* @var $this yii\web\View */
/* @var $model common\models\Article */

$this->beginContent('@backend/views/layouts/_previewHeader.php');
$this->endContent();
$this->params['breadcrumbs'][] = ['label' => 'Article', 'url' => ['index']];
$this->registerCssFile((rtrim(Url::toGet<PERSON>uni(), '/')) . Yii::$app->params['cssPath'] . 'article-detail.css', ['depends' => [AppAsset::class]]);
?>

<div class="container">
    <div class="row">
        <div class="col-md-12">
            <div class="articleHeader">
                <section class="pageDescription">
                    <div class="row">
                        <div class="col-md-12">
                            <h1><?= ContentHelper::htmlDecode(stripslashes($model->h1), false) ?></h1>
                        </div>
                    </div>
                </section>
            </div>
        </div>
        <div class="col-md-8">
            <main>
                <article>
                    <div class="articelNote">
                        <p><?= $model->meta_description ?></p>
                    </div>
                    <?php if (!empty($model->cover_image)): ?>
                        <div class="bannerImg">
                            <picture>
                                <source media="(max-width: 500px)" srcset="<?= ArticleDataHelper::getImage($model->cover_image) ?>">
                                <img width="1200" height="675" src="<?= ArticleDataHelper::getImage($model->cover_image) ?>" alt="<?= $model->h1 ?>" />
                            </picture>
                        </div><br />
                    <?php endif; ?>
                    <?php if (!empty($model->audio)): ?>
                        <div class="audio-container">
                            <span class="audio-text"><?= Yii::t('app', 'Listen to this article') ?></span>
                            <audio controls id="myAudio" preload="none">
                                <source src="<?php echo $model->audio ?>" type="audio/mpeg">
                                Your browser does not support the audio element.
                            </audio>
                        </div>
                    <?php endif; ?>
                    <div class="articleInfo" style="margin-bottom: 0px;">
                        <?= ContentHelper::removeStyleTag(stripslashes(html_entity_decode($model->description))) ?>
                    </div>
                </article>
            </main>
        </div>
    </div>
</div>
