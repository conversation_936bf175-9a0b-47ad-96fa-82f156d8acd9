<?php

use common\helpers\DataHelper;
use common\models\Article;
use common\models\GetgisArticle;
use common\models\GetgisArticleCategory;
use common\models\User;
use common\services\UserService;
use kartik\select2\Select2;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\web\JsExpression;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model common\models\GetgisArticle */
/* @var $form yii\widgets\ActiveForm */

$model->description = html_entity_decode($model->description);
if (!empty($model->author_id)) {
    $user =  ArrayHelper::map(User::find()->where(['id' => $model->author_id])->all(), 'id', 'name');
}
?>

<style>
    .form-group.has-error .help-block {
        position: absolute;
        bottom: -13px;
        font-size: 13px;
    }
</style>

<div class="getgis-article-form box box-primary">
    <?php $form = ActiveForm::begin(); ?>
    <div class="box-body table-responsive">
        <div class="row" style="margin: 0;">
            <div class="col-md-6">
                <?= $form->field($model, 'author_id')->widget(Select2::classname(), [
                    'data' => $user ?? [],
                    'options' => [
                        'placeholder' => '--Select--',
                        'options' => $selectedValueTranslation ?? [],
                    ],
                    'pluginOptions' => [
                        'allowClear' => true,
                        'minimumInputLength' => 3,
                        'maximumInputLength' => 10,
                        'language' => [
                            'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                        ],
                        'ajax' => [
                            'url' => ['../ajax/all-users'],
                            'dataType' => 'json',
                            'data' => new JsExpression('function(params) {return {q:params.term}; }')
                        ],
                        'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                        'templateResult' => new JsExpression('function(data) { return data.text; }'),
                        'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                    ],
                ])->label('Author ID');
?>
            </div>
            <div class="col-md-6">
                <?= $form->field($model, 'category_id')->dropDownList(
                    ArrayHelper::map(GetgisArticleCategory::find()->where(['status' => GetgisArticleCategory::STATUS_ACTIVE])->all(), 'id', 'name'),
                    [
                        'prompt' => '-- select category --'
                    ]
                ) ?>
            </div>
            <div class="col-md-6">
                <?= $form->field($model, 'title')->textInput(['maxlength' => true]) ?>
            </div>
            <div class="col-md-6">
                <?= $form->field($model, 'slug')->textInput(['maxlength' => true, 'disabled' => true]) ?>
            </div>
            <div class="col-md-4">
                <?php if (!empty($model->cover_image)): ?>
                    <?= Html::img(Yii::getAlias('@articleGeneralFrontend') . '/' . $model->cover_image, ['width' => '250', 'height' => '150']) ?>
                <?php endif; ?>
                <?= $form->field($model, 'cover_image')->fileInput() ?>
            </div>
            <div class="col-md-4">
                <?php if (!empty($model->thumbnail_image)): ?>
                    <?= Html::img(Yii::getAlias('@articleGeneralFrontend') . '/' . $model->thumbnail_image, ['width' => '250', 'height' => '150']) ?>
                <?php endif; ?>
                <?= $form->field($model, 'thumbnail_image')->fileInput() ?>
            </div>
            <div class="col-md-4">
                <?= $form->field($model, 'cover_image_alt_text')->textInput(['maxlength' => true]) ?>
            </div>
            <div class="col-md-12">
                <?= $form->field($model, 'description')->textarea(['rows' => 20]) ?>
                <?php $this->render('/widget/tinymce', [
                    'form' => $form,
                    'model' => $model,
                    'type' => Article::ENTITY_ARTICLE
                ]); ?>
            </div>
            <div class="col-md-6">
                <?= $form->field($model, 'h1')->textInput(['maxlength' => true]) ?>
            </div>
            <div class="col-md-6">
                <?= $form->field($model, 'meta_title')->textInput(['maxlength' => true]) ?>
            </div>
            <div class="col-md-6">
                <?= $form->field($model, 'meta_description')->textInput(['maxlength' => true]) ?>
            </div>
            <div class="col-md-6">
                <?= $form->field($model, 'is_popular')->dropDownList(DataHelper::getConstantList('POPULAR', GetgisArticle::class)) ?>
            </div>
            <div class="col-md-6">
                <?php if ($model->isNewRecord) {
                    echo $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', GetgisArticle::class), ['options' => [2 => ['selected' => true]]]);
                } else {
                    echo $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', GetgisArticle::class));
                } ?>
            </div>
        </div>
    </div>
    <div class="box-footer">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success btn-flat']) ?>
        <?php if ($model->id != null && $model->is_published == GetgisArticle::PUBLISHED_NO): ?>
            <?= Html::button('publish', ['class' => 'btn btn-primary btn-flat', 'id' => 'blog-publish']) ?>
        <?php endif ?>
    </div>
    <?php ActiveForm::end(); ?>
</div>
<?php
if ($model->id !== null) {
    $this->registerJs(
        '$("#blog-publish").on("click", function(e) {
            e.preventDefault();
            $.post("/getgis-article/publish-article", {
                id: ' . $model->id . ',
            }).done(function(data) {
                window.location.reload()
            })
        });'
    );
}
$this->registerJs("
    $('#getgisarticle-title').on('input', function() {
        var title = $(this).val();
        var slug = title.toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/^-|-$/g, '');
        $('#getgisarticle-slug').val(slug);
    });
");
?>