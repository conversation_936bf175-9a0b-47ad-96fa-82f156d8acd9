<?php

use common\helpers\DataHelper;
use common\models\GetgisArticle;
use common\models\GetgisArticleCategory;
use common\models\User;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model common\models\GetgisArticle */

$this->title = $model->title;
$this->params['breadcrumbs'][] = ['label' => 'Getgis Articles', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="getgis-article-view box box-primary">
    <div class="box-header">
        <?= Html::a('Update', ['update', 'id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?= DetailView::widget([
            'model' => $model,
            'attributes' => [
                'id',
                'author_id' => [
                    'attribute' => 'author_id',
                    'value' => function ($model) {
                        if (!empty($model->author_id)) {
                            $user =  User::find()->select('name')->where(['id' => $model->author_id])->one();
                            return $user->name ?? '';
                        } else {
                            return '-';
                        }
                    },
                    'label' => 'Author Id'
                ],
                'category_id' => [
                    'attribute' => 'category_id',
                    'value' => function ($model) {
                        if (!empty($model->category_id)) {
                            $user =  GetgisArticleCategory::find()->select('name')->where(['id' => $model->category_id])->one();
                            return $user->name ?? '';
                        } else {
                            return '-';
                        }
                    },
                    'label' => 'Category Id'
                ],
                'title',
                'slug',
                [
                    'label' => 'Cover Image',
                    'attribute' => 'cover_image',
                    'format' => 'html',
                    'value' => function ($model) {
                        if (!empty($model->cover_image)) {
                            return Html::img(Yii::getAlias('@articleGeneralFrontend') . '/' . $model->cover_image, ['width' => '250', 'height' => '150']);
                        }
                    }
                ],
                [
                    'label' => 'Thumbnail Image',
                    'attribute' => 'thumbnail_image',
                    'format' => 'html',
                    'value' => function ($model) {
                        if (!empty($model->thumbnail_image)) {
                            return Html::img(Yii::getAlias('@articleGeneralFrontend') . '/' . $model->thumbnail_image, ['width' => '250', 'height' => '150']);
                        }
                    }
                ],
                [
                    'label' => 'Document details',
                    'format' => 'raw',
                    'value' => html_entity_decode($model->description),
                ],
                // 'description:ntext',
                'h1',
                'meta_title',
                'meta_description',
                'view_count',
                // 'is_popular',
                [
                    'label' => 'Is Popular',
                    'attribute' => 'is_popular',
                    'value' => ArrayHelper::getValue(DataHelper::getConstantList('POPULAR', GetgisArticle::class), $model->is_popular)
                ],
                'reading_time',
                [
                    'label' => 'Status',
                    'attribute' => 'status',
                    'value' => ArrayHelper::getValue(DataHelper::getConstantList('STATUS', GetgisArticle::class), $model->status)
                ],
                'created_by' => [
                    'attribute' => 'created_by',
                    'value' => function ($model) {
                        if (!empty($model->created_by)) {
                            $user =  User::find()->select('name')->where(['id' => $model->created_by])->one();
                            return $user->name ?? '';
                        } else {
                            return '-';
                        }
                    },
                    'label' => 'Created By'
                ],
                'updated_by' => [
                    'attribute' => 'updated_by',
                    'value' => function ($model) {
                        if (!empty($model->updated_by)) {
                            $user =  User::find()->select('name')->where(['id' => $model->updated_by])->one();
                            return $user->name ?? '';
                        } else {
                            return '-';
                        }
                    },
                    'label' => 'Updated By'
                ],
                // 'published_by',
                // 'published_at',
                'created_at:datetime',
                'updated_at:datetime',
            ],
        ]) ?>
    </div>
</div>