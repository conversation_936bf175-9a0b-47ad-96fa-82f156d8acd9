<?php

use yii\helpers\Html;
use common\helpers\DataHelper;
use common\models\Course;
use common\models\SponsorCollege;
use common\models\State;
use common\models\Stream;
use kartik\select2\Select2;
use yii\bootstrap\ActiveForm;
use unclead\multipleinput\TabularInput;
use unclead\multipleinput\TabularColumn;
use yii\helpers\ArrayHelper;

/* @var $this \yii\web\View */
/* @var $models Item[] */

$states = ArrayHelper::map(State::find()->select(['slug', 'name'])->all(), 'slug', 'name');
$allStates = array_merge(['all-states' => 'All States'], $states);

$streams = ArrayHelper::map(Stream::find()->select(['slug', 'name'])->byExcludeSlug('other')->all(), 'slug', 'name');
$allStreams = array_merge(['all-streams' => 'All Streams'], $streams);

$courses = ArrayHelper::map(Course::find()->select(['slug', 'name'])->all(), 'slug', 'name');
$allCourses = array_merge(['all-courses' => 'All Courses'], $courses);
?>

<div class="sponsor-college-form box box-primary">
    <?php $form = \yii\bootstrap\ActiveForm::begin([
        'id' => 'sponsor-college-tabular-form',
        'options' => [
            'enctype' => 'multipart/form-data'
        ]
    ]) ?>
    <div class="col-md-4">
     <div class="form-group ">
       <label class="control-label" for="college-is_sposor_view">Sponsor College View Type</label>
            <?= Html::dropDownList('sponser_college_view', null, (DataHelper::getConstantList('VIEWS', SponsorCollege::class)), ['class' => 'form-control', 'id'=>'college-is_sposor_view','options' => [$college->sponser_college_view =>['selected'=>true]]]) ?>
            <div class="help-block"></div>
      </div>
   </div>
   
    <div class="box-body">

        <?= TabularInput::widget([
            'models' => $models,
            'modelClass' => SponsorCollege::class,
            'allowEmptyList' => 'false',
            'min' => 0,
            'max' => 100,
            'addButtonPosition' => [
                TabularInput::POS_HEADER,
            ],
            'layoutConfig' => [
                'offsetClass'   => 'col-sm-offset-4',
                'labelClass'    => 'col-sm-2',
                'wrapperClass'  => 'col-sm-10',
                'errorClass'    => 'col-sm-4'
            ],
            'attributeOptions' => [
                'enableAjaxValidation'   => false,
                'enableClientValidation' => true,
                'validateOnChange'       => true,
                'validateOnSubmit'       => true,
                'validateOnBlur'         => false,
            ],
            'form' => $form,
            'columns' => [
                [
                    'name' => 'college_id',
                    'defaultValue' => $college->id,
                    'type' => TabularColumn::TYPE_HIDDEN_INPUT
                ],
                [
                    'name'  => 'state',
                    'type' => Select2::className(),
                    'title' => 'Select States',
                    'options' => [
                        'data' => $allStates,
                        'showToggleAll' => false,
                        'options' => [
                            'placeholder' => '...',
                        ],
                        'pluginOptions' => ['allowClear' => true],
                    ]
                ],
                [
                    'name'  => 'stream',
                    'type' => Select2::className(),
                    'title' => 'Select Streams',
                    'options' => [
                        'data' => $allStreams,
                        'showToggleAll' => false,
                        'options' => [
                            'placeholder' => '...',
                        ],
                        'pluginOptions' => ['allowClear' => true],
                        'pluginEvents' => [
                            'change' => 'function(data) { 
                                var data_value = data.currentTarget.value;
                                if (data_value != "") { 
                                    $(this).parent().parent().next().children().children("select").val(null);
                                    $(this).parent().parent().next().children().children("select").attr("disabled", true);
                                } else {
                                    $(this).parent().parent().next().children().children("select").attr("disabled", false);                                    
                                }
                            }',
                        ],
                    ]
                ],
                [
                    'name'  => 'course',
                    'type' => Select2::className(),
                    'title' => 'Select Courses',
                    'options' => [
                        'data' => $allCourses,
                        'showToggleAll' => false,
                        'options' => [
                            'placeholder' => '...',
                        ],
                        'pluginOptions' => ['allowClear' => true],
                        'pluginEvents' => [
                            'change' => 'function(data) { 
                                var data_value = data.currentTarget.value;
                                if (data_value != "") { 
                                    $(this).parent().parent().prev().children().children("select").val(null);
                                    $(this).parent().parent().prev().children().children("select").attr("disabled", true);
                                } else {
                                    $(this).parent().parent().prev().children().children("select").attr("disabled", false);                                    
                                }
                            }',
                        ],
                    ]
                ],
                [
                    'name' => 'position',
                    'title' => 'Position',
                    'type' => TabularColumn::TYPE_TEXT_INPUT,
                ],
                [
                    'name'  => 'status',
                    'title' => 'Status',
                    'type' => TabularColumn::TYPE_DROPDOWN,
                    'items' => DataHelper::getConstantList('STATUS', SponsorCollege::class),
                ],

            ],
        ]) ?>

    </div>

    <div class="box-footer">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <?php ActiveForm::end(); ?>
</div>