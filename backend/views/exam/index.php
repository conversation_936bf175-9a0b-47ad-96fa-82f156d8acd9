<?php

use common\grid\EnumColumn;
use common\helpers\DataHelper;
use common\models\Exam;
use yii\bootstrap\ButtonDropdown;
use yii\helpers\Html;
use yii\grid\GridView;

/* @var $this yii\web\View */
/* @var $searchModel backend\models\ExamSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Exams';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="exam-index box box-primary">
    <div class="box-header with-border">
        <?= Html::a('Create Exam', ['create'], ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <div class="box-body table-responsive no-padding" style="overflow-x:auto;">
        <?php // echo $this->render('_search', ['model' => $searchModel]);
        ?>
        <?= GridView::widget([
            'dataProvider' => $dataProvider,
            'filterModel' => $searchModel,
            'layout' => "{items}\n{summary}\n{pager}",
            'columns' => [
                ['class' => 'yii\grid\SerialColumn'],
                'id',
                'display_name',
                'slug',
                // [
                //     'label' => 'Streams',
                //     'format' => 'ntext',
                //     'attribute' => 'streams',
                //     'headerOptions' => ['style' => 'width:200px;'],
                //     'value' => function ($model) {
                //         $streamNames = [];
                //         foreach ($model->streams as $stream) {
                //             $streamNames[] = $stream->name;
                //         }
                //         return implode(',', $streamNames);
                //     },
                // ],
                // [
                //     'label' => 'Courses',
                //     'format' => 'ntext',
                //     'attribute' => 'courses',
                //     'headerOptions' => ['style' => 'width:300px;'],
                //     'value' => function ($model) {
                //         $courseNames = [];
                //         foreach ($model->courses as $course) {
                //             $courseNames[] = $course->name;
                //         }
                //         return implode(',', $courseNames);
                //     },
                // ],
                // [
                //     'class' => EnumColumn::class,
                //     'attribute' => 'level',
                //     'enum' => DataHelper::getConstantList('LEVEL', Exam::class),
                //     'filter' => DataHelper::getConstantList('LEVEL', Exam::class),
                // ],
                // [
                //     'class' => EnumColumn::class,
                //     'attribute' => 'type',
                //     'enum' => DataHelper::getConstantList('TYPE', Exam::class),
                //     'filter' => DataHelper::getConstantList('TYPE', Exam::class),
                // ],
                // [
                //     'class' => EnumColumn::class,
                //     'attribute' => 'mode',
                //     'enum' => DataHelper::getConstantList('MODE', Exam::class),
                //     'filter' => DataHelper::getConstantList('MODE', Exam::class),
                // ],
                [
                    'attribute' => 'lang_code',
                    'value' => function ($model) {
                        if (!empty($model->lang_code)) {
                            return array_search($model->lang_code, DataHelper::$languageCode);
                        }
                    },
                    'filter' => array_flip(DataHelper::$languageCode)
                ],

                [
                    'class' => EnumColumn::class,
                    'attribute' => 'status',
                    'enum' => DataHelper::getConstantList('STATUS', Exam::class),
                    'filter' => DataHelper::getConstantList('STATUS', Exam::class),
                ],
                [
                    'class' => EnumColumn::class,
                    'attribute' => 'is_popular',
                    'enum' => DataHelper::getConstantList('POPULAR', Exam::class),
                    'filter' => DataHelper::getConstantList('POPULAR', Exam::class),
                ],
                // 'created_at:date',
                'updated_at:date',
                // ['class' => 'yii\grid\ActionColumn'],
                [
                    'label' => '',
                    'format' => 'raw',
                    'value' => function ($data) {
                        return ButtonDropdown::widget([
                            'label' => '<i class="fa fa-fw fa-cogs"></i> Actions',
                            'options' => ['class' => 'btn btn-xs btn-primary dropdown-toggle'],
                            'encodeLabel' => false,
                            'containerOptions' => ['class' => 'btn-group dropdown'],
                            'dropdown' => [
                                'options' => ['class' => 'dropdown-menu dropdown-menu-right'],
                                'encodeLabels' => false,
                                'items' => [
                                    ['label' => '<i class="fa fa-eye margin-r-5"></i>View Exam', 'url' => ['exam/view', 'id' => $data->id]],
                                    ['label' => '<i class="fa fa-pencil margin-r-5"></i>Update Exam', 'url' => ['exam/update', 'id' => $data->id],  'icon' => 'circle-o'],
                                    ['label' => '<i class="fa fa-pencil-square-o margin-r-5"></i>Exam Content', 'url' => ['exam-content/create', 'examId' => $data->id]],
                                    ['label' => '<i class="fa fa-calendar-check-o margin-r-5"></i>Exam Date', 'url' => ['exam-date/create', 'examId' => $data->id]],
                                ],
                            ],
                        ]);
                    }
                ],
            ],
        ]); ?>
    </div>
</div>