<?php

use yii\helpers\Html;
use yii\grid\GridView;

/* @var $this yii\web\View */
/* @var $searchModel backend\models\CollegePiSubpageContentSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'College Pi Subpage Contents';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="college-pi-subpage-content-index box box-primary">
    <div class="box-header with-border">
        <?= Html::a('Create College Pi Subpage Content', ['create'], ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?php // echo $this->render('_search', ['model' => $searchModel]); ?>
        <?= GridView::widget([
            'dataProvider' => $dataProvider,
            'filterModel' => $searchModel,
            'layout' => "{items}\n{summary}\n{pager}",
            'columns' => [
                ['class' => 'yii\grid\SerialColumn'],

                'id',
                'college_program_id',
                'subpage',
                'year',
                'meta_title',
                // 'meta_description',
                // 'h1',
                // 'content:ntext',
                // 'status',
                // 'created_at',
                // 'updated_at',

                ['class' => 'yii\grid\ActionColumn'],
            ],
        ]); ?>
    </div>
</div>
