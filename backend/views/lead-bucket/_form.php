<?php

use common\helpers\DataHelper;
use common\models\LeadBucket;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model common\models\LeadBucket */
/* @var $form yii\widgets\ActiveForm */

$entityId = !empty($_GET['entity_id']) ? $_GET['entity_id'] : ($model[0]->entity_id ?? '');
$entity = ArrayHelper::getValue(DataHelper::getConstantList('LEAD_ENTITY', LeadBucket::class), $entityId);
?>

<div class="lead-bucket-form box box-primary">
    <?php $form = ActiveForm::begin(); ?>
    <div class="box-body">
        <div class="row">
            <div class="col-md-4">
                <?php $disabled = !empty($entity) ? true : false; ?>
                <?= $form->field($model, 'entity_id')->dropDownList(
                    DataHelper::getConstantList('LEAD_ENTITY', LeadBucket::class),
                    [
                        'disabled' => $model->isNewRecord ? $disabled : true,
                        // 'onchange' => '$("#leadbucket-0-entity_id").val(this.value);',
                    ]
                ); ?>
            </div>
            <?php /** ?>
            <div class="col-md-4">
                <?= $form->field($model, 'template_id')->dropDownList(
                    DataHelper::getConstantList('TEMPLATE', LeadBucket::class),
                    [
                        'id' => 'template_id',
                        'onchange' => 'console.log(this.value);if (this.value == 2) {
                        $("#leadbucket-cta_title").attr("disabled", true);
                        $("#leadbucket-cta_description").attr("disabled", true);
                        } else {
                        $("#leadbucket-cta_title").attr("disabled", false);
                        $("#leadbucket-cta_description").attr("disabled", false);
                        }'
                    ]
                )->label('Template'); ?>
            </div>
            <?php */ ?>
            <div class="col-md-4">
                <?= $form->field($model, 'bucket')->textInput(['maxlength' => true]) ?>
            </div>
            <div class="col-md-4">
                <?= $form->field($model, 'status')->dropDownList(
                    DataHelper::getConstantList('STATUS', LeadBucket::class)
                )->label('Status') ?>
            </div>
            <?php /** ?>
            <div class="col-md-4">
                <?= Html::a('Add CTA', ['lead-bucket/create-cta', 'model' => $model], ['class' => 'btn btn-success btn-flat']) ?>
            </div>

            <div class="col-md-3">
                <?= $form->field($model, 'cta_text')->textInput(['maxlength' => true]) ?>
            </div>

            <div class="col-md-6">
                <?= $form->field($model, 'cta_title')->textInput(['maxlength' => true, 'disabled' => true]) ?>
            </div>

            <div class="col-md-6">
                <?= $form->field($model, 'cta_description')->textInput(['maxlength' => true, 'disabled' => true]) ?>
            </div>

            <div class="col-md-6">
                <?= $form->field($model, 'lead_form_title')->textInput(['maxlength' => true]) ?>
            </div>

            <div class="col-md-6">
                <?= $form->field($model, 'lead_form_description')->textInput(['maxlength' => true]) ?>
            </div>

            <div class="col-md-4">
                <?= $form->field($model, 'page_event')->dropDownList(
                    DataHelper::getConstantList('LEAD_PAGE', LeadBucket::class),
                    [
                        'id' => 'page_event',
                        'onchange' => 'console.log(this.value);if (this.value == 1) {
                        $("#leadbucket-page_link").attr("disabled", false);
                        } else {
                        $("#leadbucket-page_link").attr("disabled", true);
                        }'
                    ]
                ); ?>
            </div>

            <div class="col-md-4">
                <?= $form->field($model, 'page_link')->textInput(['maxlength' => true, 'disabled' => false]) ?>
            </div>

            <div class="col-md-4">
                <?= $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', LeadBucket::class)) ?>
            </div>

            <?php */ ?>
        </div>
        <br>
        <div class="box box-success expanded-box">
            <div class="box-header with-border">
                <h3 class="box-title">Add CTA Content</h3>
                <?php /**<div class="box-tools pull-right">
                        <button type="button" class="btn btn-box-tool" data-widget="expand"><i class="fa fa-minus"></i></button>
                    </div> */ ?>
            </div>
            <?php if ($entityId == LeadBucket::LEAD_ENTITY_NEWS_AND_ARTICLE) { ?>
                <div class="box-body">
                    <?php echo $this->render('../lead-bucket-cta/_tabular-form-news-articles', [
                        'model' => $model,
                        'models' => $models
                    ]) ?>
                </div>
            <?php } else { ?>
                <div class="box-body">
                    <?php echo $this->render('../lead-bucket-cta/_tabular-form', [
                        'model' => $model,
                        'models' => $models
                    ]) ?>
                </div>
            <?php } ?>
        </div>
    </div>
    <?php ActiveForm::end(); ?>
</div>