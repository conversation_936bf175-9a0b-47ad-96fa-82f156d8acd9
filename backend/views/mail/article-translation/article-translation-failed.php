<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Article Translation Email Notification</title>
    <style>
        table {
            border-spacing: 0;
            width: 100%;
            color: #333;
            border: .2px solid #eaeaea;
            border-bottom: 0;
        }

        table thead tr th {
            font-size: 14px;
            line-height: 24px;
            padding: 11px;
            border-right: .2px solid #eaeaea;
            border-bottom: .2px solid #eaeaea;
            min-width: 130px;
            text-align: center;
        }

        table thead tr {
            color: #333333;
            background: #f1f3f4;
            font-size: 14px;
            padding: 0;
            font-weight: 700;
            border: .2px solid #eaeaea;
            border-bottom: none;
        }

        table td {
            font-size: 14px;
            line-height: 24px;
            padding: 11px;
            border-right: .2px solid #eaeaea;
            border-bottom: .2px solid #eaeaea;
            min-width: 130px;
            text-align: center;
            vertical-align: middle;
        }
    </style>
</head>
<?php
use frontend\helpers\Url;
use common\helpers\DataHelper;
?>
<body>
    <p>Hi <?= $data['name']; ?>, </p>
    <p>This article translation  has been failed,Please try again. </p>
    <br>
    <table style="width: 100%">
        <thead>
            <tr>
                <th>Article Title</th>
                <th>Article Slug</th>
                <th>Article Link</th>
                <th>Article  Transaction Status</th>
            </tr>
        </thead>
        <tbody>
            <?php if (!empty($data)): ?>
                <tr>
                    <td><?= $data->title; ?></td>
                    <td><?= $data->slug; ?></td>
                    <td> <a  traget="_blank" href="https://backend.getmyuni.com/article/translate?id=<?= $data->id?>">Translate</a></td>
                    <td>Failed</td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>
</body>

</html>