<?php

use yii\helpers\Html;
use common\helpers\DataHelper;
use common\models\CollegeRankings;
use common\models\CollegeRankingPublisher;
use common\models\Stream;
use common\models\Course;
use common\models\old\CollegesRank;
use kartik\select2\Select2;
use yii\bootstrap\ActiveForm;
use unclead\multipleinput\TabularInput;
use unclead\multipleinput\TabularColumn;
use yii\helpers\ArrayHelper;
use yii\web\JsExpression;
use yii\helpers\Url;

/* @var $this \yii\web\View */
/* @var $models Item[] */

$publishers = ArrayHelper::map(CollegeRankingPublisher::find()->select(['id', 'name'])->all(), 'id', 'name');


$courses = ArrayHelper::map(Course::find()->select(['slug', 'name', 'id'])->all(), 'slug', 'name');
$allCourses = array_merge(['all-courses' => 'All Courses'], $courses);
$streams = ArrayHelper::map(Stream::find()->select(['slug', 'id', 'name'])->all(), 'slug', 'name');
$allStreams = array_merge(['all-streams' => 'All Streams'], $streams);

?>
<div class="sponsor-college-form box box-primary">
    <?php $form = \yii\bootstrap\ActiveForm::begin([
        'id' => 'college-rank-tabular-form',
        'options' => [
            'enctype' => 'multipart/form-data'
        ]
    ]) ?>
    <div class="box-body">

        <?= TabularInput::widget([
            'models' => $models,
            'modelClass' => CollegeRankings::class,
            'allowEmptyList' => 'false',
            'min' => 0,
            'max' => 100,
            'addButtonPosition' => [
                TabularInput::POS_HEADER,
            ],
            'layoutConfig' => [
                'offsetClass'   => 'col-sm-offset-4',
                'labelClass'    => 'col-sm-2',
                'wrapperClass'  => 'col-sm-10',
                'errorClass'    => 'col-sm-4'
            ],
            'attributeOptions' => [
                'enableAjaxValidation'   => false,
                'enableClientValidation' => true,
                'validateOnChange'       => true,
                'validateOnSubmit'       => true,
                'validateOnBlur'         => false,
            ],
            'form' => $form,
            'columns' => [

                [
                    'name'  => 'publisher_id',
                    'type' => Select2::className(),
                    'title' => 'Select Publisher',
                    'options' => [
                        'data' => $publishers,
                        'showToggleAll' => false,
                        'options' => [
                            'placeholder' => 'Select Publisher',
                        ],
                        'pluginOptions' => ['allowClear' => true],

                    ]
                ],
                [
                    'name' => 'id',
                    'type' => TabularColumn::TYPE_HIDDEN_INPUT,
                ],
                [
                    'name' => 'criteria_id_update',
                    'type' => TabularColumn::TYPE_HIDDEN_INPUT,
                    'value' => function ($model) {
                        return $model->criteria_id;
                    }
                ],
                [
                    'name'  => 'criteria',
                    'title' => 'Criteria',
                    'type' => TabularColumn::TYPE_DROPDOWN,
                    'options' => [
                        'class' => 'form-control criteria-select',
                        //'disabled' => 'disabled'
                    ],
                    'items' => DataHelper::getConstantList('CRITERIA', CollegeRankings::class),
                ],
                [
                    'name' => 'criteria_value',
                    'title' => 'Criteria Value',
                    'type' => TabularColumn::TYPE_TEXT_INPUT,
                    'options' => [
                        'class' => 'criteria_value',
                        'disabled' => 'disabled'
                    ],
                    'value' => function ($model) {
                        if ($model->criteria == CollegeRankings::CRITERIA_COURSE) {
                            return $model->course->name;
                        } elseif ($model->criteria == CollegeRankings::CRITERIA_STREAM) {
                            return $model->stream->name ?? '';
                        } elseif ($model->criteria == CollegeRankings::CRITERIA_SPECIALIZATION) {
                            return $model->specialization->name;
                        } elseif ($model->criteria == CollegeRankings::CRITERIA_OTHER) {
                            return $model->other->name;
                        }
                    }
                ],
                [
                    'name' => 'criteria_id',
                    'title' => 'Select Criteria Value',
                    'type' => Select2::className(),
                    'options' => [
                        'data' => $courses,
                        'showToggleAll' => false,
                        'options' => [
                            'placeholder' => 'Select Criteria Value',
                        ],
                        'pluginOptions' => [
                            'allowClear' => true,
                            'placeholder' => '--Select--',
                            'minimumInputLength' => 1,
                            'language' => [
                                'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                            ],
                            'ajax' => [
                                'url' => Url::to(['/college-rankings/get-criteria-value']),
                                'dataType' => 'json',
                                'data' => new JsExpression("function(params) {
                                    var id =  $(this).attr('id');
                                    var result = id.split('-');
                                    var publisher_id = result[0]+'-'+result[1]+'-publisher_id';
                                    var criteria = result[0]+'-'+result[1]+'-criteria';
                                    var criteriaVal = $('#'+criteria).val();
                                    var publisherVal = $('#'+publisher_id).val();
                                    if(criteriaVal=='other'){
                                        if(publisherVal == ''){
                                         alert('Please select publisher for Other criteria');
                                         return false;
                                        }
                                    }
                                    return {q:params.term,depdrop_parents:$('#'+criteria).val(),publisher_id:$('#'+publisher_id).val()};
                                
                                }")
                            ],
                        ],
                    ]
                ],
                [
                    'name' => 'rank',
                    'title' => 'Rank',
                    'type' => TabularColumn::TYPE_TEXT_INPUT,
                ],
                [
                    'name' => 'year',
                    'title' => 'Year',
                    'type' => TabularColumn::TYPE_TEXT_INPUT,
                ],
                [
                    'name'  => 'status',
                    'title' => 'Status',
                    'type' => TabularColumn::TYPE_DROPDOWN,
                    'items' => DataHelper::getConstantList('STATUS', CollegeRankings::class),
                ],

            ],
        ]) ?>

    </div>

    <div class="box-footer">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <?php ActiveForm::end(); ?>
</div>