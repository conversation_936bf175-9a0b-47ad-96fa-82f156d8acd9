<?php

use common\helpers\DataHelper;
use common\models\SaCollegeSubpage;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model common\models\SaCollegeSubpage */

$this->title = $model->name;
$this->params['breadcrumbs'][] = ['label' => 'Sa College Subpages', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="sa-college-subpage-view box box-primary">
    <div class="box-header">
        <?= Html::a('Update', ['update', 'id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?= DetailView::widget([
            'model' => $model,
            'attributes' => [
                'name',
                'slug',
                [
                    'label' => 'Status',
                    'attribute' => 'status',
                    'value' => ArrayHelper::getValue(DataHelper::getConstantList('STATUS', SaCollegeSubpage::class), $model->status)
                ],

                [
                    'attribute' => 'created_by',
                    'value' => function ($model) {
                        return !empty($model->author) ? $model->author->name : '';
                    }
                ],
                [
                    'attribute' => 'updated_by',
                    'value' => function ($model) {
                        return $model->updater->name ?? '';
                    }
                ],
                'created_at:datetime',
                'updated_at:datetime',
            ],
        ]) ?>
    </div>
</div>