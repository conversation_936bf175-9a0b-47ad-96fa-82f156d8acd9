<?php

use frontend\helpers\Html;
use yii\grid\GridView;

/* @var $this yii\web\View */
/* @var $searchModel backend\models\CollegeCourseSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = $college->name . ' || ' . $entity_type . ' ' . 'Dates';

$this->params['breadcrumbs'][] = ['label' => $college->name, 'url' => '/college/view?id=' . $college->id];

if ($params['entity_type'] == 1) {
    $columns = [
        [
            'label' => 'Courses',
            'attribute' => 'course',
            'value' => 'course.name'
        ]
    ];
} else {
    $columns = [
        [
            'label' => 'Programs',
            'attribute' => 'program',
            'value' => 'program.name'
        ],
        [
            'label' => 'Courses',
            'attribute' => 'course',
            'value' => 'course.name'
        ]
    ];
}
?>
<div class="college-course-index box box-primary">
    <div class="box-header">
    </div>
    <div class="box-header with-border">
    </div>
    <div class="box-body table-responsive no-padding">
        <?= GridView::widget([
            'dataProvider' => $dataProvider,
            'filterModel' => $searchModel,
            'columns' => array_merge(
                [
                    ['class' => 'yii\grid\SerialColumn']
                ],
                $columns,
                [
                    [
                        'class' => 'yii\grid\ActionColumn',
                        'template' => '{update}',
                        'buttons' => [
                            'view' => function ($url, $model) {
                                return Html::a('<span class="glyphicon glyphicon-eye-open"></span>', $url);
                            },
                        ],
                        'urlCreator' => function ($action, $model, $key, $index) use ($params) {
                            if ($action === 'update') {
                                return ['course-program-dates/add-dates', 'entityType' => $params['entity_type'], 'entityId' => $model->id];
                            }
                        }
                    ]
                ]
            ),
        ]); ?>
    </div>
</div>