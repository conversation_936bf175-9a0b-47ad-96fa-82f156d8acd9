<?php

use yii\helpers\Html;
use yii\grid\GridView;

/* @var $this yii\web\View */
/* @var $searchModel backend\models\CourseProgramDatesSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Course Program Dates';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="course-program-dates-index box box-primary">
    <div class="box-header with-border">
        <?= Html::a('Create Course Program Dates', ['create'], ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?php // echo $this->render('_search', ['model' => $searchModel]); ?>
        <?= GridView::widget([
            'dataProvider' => $dataProvider,
            'filterModel' => $searchModel,
            'layout' => "{items}\n{summary}\n{pager}",
            'columns' => [
                ['class' => 'yii\grid\SerialColumn'],

                'id',
                'entity_type',
                'entity_id',
                'name',
                'slug',
                // 'start',
                // 'end',
                // 'type',
                // 'status',
                // 'created_at',
                // 'updated_at',

                ['class' => 'yii\grid\ActionColumn'],
            ],
        ]); ?>
    </div>
</div>
