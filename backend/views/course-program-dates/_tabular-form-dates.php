<?php

use common\helpers\DataHelper;
use common\models\CourseProgramDates;
use common\models\ExamDate;
use yii\bootstrap\ActiveForm;
use unclead\multipleinput\TabularInput;
use yii\helpers\Html;
use unclead\multipleinput\TabularColumn;
use kartik\datetime\DateTimePicker;

/* @var $this \yii\web\View */
/* @var $models Item[] */

if ($entityType == CourseProgramDates::ENTITY_TYPE_CI) {
    $this->title = $college->college->name . ' -- Course -- ' . $college->course->name . ' ' . 'Dates Panel';
} else {
    $this->title = $college->college->name . ' -- Program -- ' . $college->program->name . ' ' . 'Dates Panel';
}
$this->params['breadcrumbs'][] = ['label' => $college->college->name, 'url' => '/college/view?id=' . $college->college->id];
$this->params['breadcrumbs'][] = ['label' => 'Dates Panel', 'url' => '/course-program-dates/home?entity_type=' . $entityType . '&college_id=' . $college->college->id];

$courseProgramDateList =  DataHelper::examDateList();

?>
<style>
    h1 {
        margin-top: 30px !important;
    }
</style>
<div class="callout callout-info">
    <h4>Date hint!</h4>
    <p>End Date should be greater than the Start Date.</p>
</div>
<div class="exam-date-form box box-primary">
    <?php $form = \yii\bootstrap\ActiveForm::begin([
        'id' => 'tabular-form',
        'options' => [
            'enctype' => 'multipart/form-data'
        ]
    ]) ?>
    <div class="box-body">

        <?= TabularInput::widget([
            'models' => $models,
            'modelClass' => CourseProgramDates::class,
            // 'cloneButton' => true,
            // 'sortable' => true,
            'allowEmptyList' => 'false',
            'min' => count($courseProgramDateList),
            'max' => count($courseProgramDateList),
            // 'addButtonPosition' => [
            //     TabularInput::POS_HEADER,
            //     TabularInput::POS_FOOTER,
            //     TabularInput::POS_ROW
            // ],
            'layoutConfig' => [
                'offsetClass'   => 'col-sm-offset-4',
                'labelClass'    => 'col-sm-2',
                'wrapperClass'  => 'col-sm-10',
                'errorClass'    => 'col-sm-4'
            ],
            'attributeOptions' => [
                'enableAjaxValidation'   => false,
                'enableClientValidation' => true,
                'validateOnChange'       => true,
                'validateOnSubmit'       => true,
                'validateOnBlur'         => false,
            ],
            'form' => $form,
            'columns' => [
                [
                    'name' => 'id',
                    'type' => TabularColumn::TYPE_HIDDEN_INPUT
                ],
                [
                    'name' => 'entity_type',
                    'type' => TabularColumn::TYPE_HIDDEN_INPUT,
                    'defaultValue' => $entityType
                ],
                [
                    'name' => 'entity_id',
                    'type' => TabularColumn::TYPE_HIDDEN_INPUT,
                    'defaultValue' => $entityId
                ],
                [
                    'name' => 'slug',
                    'title' => 'Date for',
                    'options' => ['disabled' => true],
                    'type' => TabularColumn::TYPE_DROPDOWN,
                    'items' => $courseProgramDateList
                ],
                [
                    'name' => 'start',
                    'type'  => DateTimePicker::class,
                    'title' => 'Start Date',
                    'enableError' => true,
                    'options' => [
                        'pluginOptions' => [
                            'autoclose' => true,
                            'format' => 'yyyy-mm-dd HH:ii:ss',
                            'todayHighlight' => true,
                        ],

                    ],
                    'headerOptions' => [
                        'style' => 'width: 250px;',
                        'class' => 'day-css-class'
                    ]
                ],
                [
                    'name' => 'end',
                    'type'  => DateTimePicker::class,
                    'title' => 'End Date',
                    'enableError' => true,
                    'options' => [
                        'pluginOptions' => [
                            'autoclose' => true,
                            'format' => 'yyyy-mm-dd HH:ii:ss',
                            'todayHighlight' => true,
                        ],

                    ],
                    'headerOptions' => [
                        'style' => 'width: 250px;',
                        'class' => 'day-css-class'
                    ]
                ],
                [
                    'name' => 'type',
                    'title' => 'Type',
                    'type' => TabularColumn::TYPE_DROPDOWN,
                    'items' => DataHelper::getConstantList('TYPE', CourseProgramDates::class)
                ],
                [
                    'name' => 'status',
                    'title' => 'Status',
                    'defaultValue' => CourseProgramDates::STATUS_INACTIVE,
                    'type' => TabularColumn::TYPE_DROPDOWN,
                    'items' => DataHelper::getConstantList('STATUS', CourseProgramDates::class)
                ],
            ],
        ]) ?>

    </div>

    <div class="box-footer">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <?php ActiveForm::end(); ?>
</div>