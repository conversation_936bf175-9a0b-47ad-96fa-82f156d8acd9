<?php

use common\helpers\DataHelper;
use common\models\SaFaq;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model common\models\SaFaq */

$this->title = $model->getTitle();
$this->params['breadcrumbs'][] = ['label' => 'Sa Faqs', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="sa-faq-view box box-primary">
    <div class="box-header">
        <?= Html::a('Update', ['update', 'id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?= DetailView::widget([
            'model' => $model,
            'attributes' => [
                [
                    'label' => 'Entity',
                    'attribute' => 'entity',
                    'value' => ArrayHelper::getValue(DataHelper::getConstantList('ENTITY', SaFaq::class), $model->entity)
                ],
                [
                    'label' => 'Page',
                    'value' => function ($model) {
                        return $model->getTitle();
                    }
                ],
                [
                    'label' => 'Question\'s & Answer\'s',
                    'format' => 'raw',
                    'value' => function ($model) {
                        return $this->render('_qnas', ['model' => $model]);
                    }
                ],
                [
                    'label' => 'Status',
                    'attribute' => 'status',
                    'value' => ArrayHelper::getValue(DataHelper::getConstantList('STATUS', SaFaq::class), $model->status)
                ],
                'created_at:datetime',
                'updated_at:datetime',
            ],
        ]) ?>
    </div>
</div>
