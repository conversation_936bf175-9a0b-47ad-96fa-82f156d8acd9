<?php

use common\helpers\ContentHelper;

$qnas = $model->qnas;
?>

<?php if (is_array($qnas)): ?>
    <table class="table table-striped table-bordered detail-view">
        <tr>
            <th>Question</th>
            <th>Answer</th>
        </tr>
        <?php foreach ($qnas as $qna): ?>
            <tr>
                <td><?=  ContentHelper::removeStyleTag(stripslashes(html_entity_decode($qna['question']))) ?? '' ?></td>
                <td><?=  ContentHelper::removeStyleTag(stripslashes(html_entity_decode($qna['answer']))) ?? '' ?></td>

            </tr>
        <?php endforeach; ?>
    </table>
<?php else: ?>
    N/A
<?php endif ?>