<?php

use common\helpers\DataHelper;
use common\models\SaCollege;
use common\models\SaCountry;
use common\models\SaFaq;
use yii\helpers\Html;
use yii\grid\GridView;
use yii\helpers\ArrayHelper;

/* @var $this yii\web\View */
/* @var $searchModel backend\models\SaFaqSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Sa Faqs';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="sa-faq-index box box-primary">
    <div class="box-header with-border">
        <?= Html::a('Create Sa Faq', ['create'], ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?php // echo $this->render('_search', ['model' => $searchModel]); ?>
        <?= GridView::widget([
            'dataProvider' => $dataProvider,
            'filterModel' => $searchModel,
            'layout' => "{items}\n{summary}\n{pager}",
            'columns' => [
                ['class' => 'yii\grid\SerialColumn'],

                [
                    'attribute' => 'entity_id',
                    'label' => 'Title',
                    'value' => function ($model) {
                        return $model->getTitle();
                    }
                ],

                [
                    'attribute' => 'entity',
                    'value' => function ($model) {
                        return ArrayHelper::getValue(DataHelper::getConstantList('ENTITY', SaFaq::class), $model->entity);
                    },
                    'filter' => DataHelper::getConstantList('ENTITY', SaFaq::class)
                ],

                [
                    'attribute' => 'status',
                    'value' => function ($model) {
                        return ArrayHelper::getValue(DataHelper::getConstantList('STATUS', SaFaq::class), $model->status);
                    },
                    'filter' => DataHelper::getConstantList('STATUS', SaFaq::class)
                ],

                ['class' => 'yii\grid\ActionColumn', 'template' => '{view} {update}'],
            ],
        ]); ?>
    </div>
</div>
