<?php

use common\helpers\DataHelper;
use common\models\SaCollege;
use common\models\SaCountry;
use common\models\SaFaq;
use frontend\helpers\Url;
use kartik\depdrop\DepDrop;
use unclead\multipleinput\MultipleInput;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model common\models\SaFaq */
/* @var $form yii\widgets\ActiveForm */

$disabled = !$model->isNewRecord ? true : false;
if ($model->entity == 1) {
    $data = ArrayHelper::map(SaCountry::find()->where(['id' => $model->entity_id])->all(), 'id', 'name');
} else {
    $data = ArrayHelper::map(SaCollege::find(), 'id', 'name');
}
?>

<div class="sa-faq-form box box-primary">
    <?php $form = ActiveForm::begin(); ?>
    <div class="box-body table-responsive">

        <?= $form->field($model, 'entity')->dropDownList(
            DataHelper::getConstantList('ENTITY', SaFaq::class),
            [
                'id' => 'faq-entity-id',
                'prompt' => '-- Select Page Type --',
                'disabled' => $disabled,
            ]
        )->label('Page Type') ?>

        <?= $form->field($model, 'entity_id')->widget(DepDrop::class, [
            'type' => DepDrop::TYPE_SELECT2,
            'options' => [
                'id' => 'entity-id',
                'placeholder' => '--Select--',
                'multiple' => false,
            ],
            'data' => $data,
            'select2Options' => ['pluginOptions' => [
                'allowClear' => true,
                'disabled' => true,
            ]],
            'pluginOptions' => [
                'depends' => ['faq-entity-id'],
                'placeholder' => 'Select Country...',
                'url' => Url::to(['/ajax/get-entity-id'])
            ],

        ])->label('Page');
?>

        <div class="text-muted well well-sm no-shadow">

            <?= $form->field($model, 'qnas')->widget(MultipleInput::class, [
                'id' => 'custom-multiple-input-id', // Set a custom ID
                'max'               => 30,
                'allowEmptyList'    => false,
                'enableGuessTitle'  => true,
                'columns' => [
                    [
                        'name'  => 'question',
                        'title' => 'Question',
                        'type' => 'textarea',
                        'columnOptions' => [
                            'style' => 'width: 40%;resize: none;',

                        ],
                    ],
                    [
                        'name'  => 'answer',
                        'title' => 'Answer',
                        'type' => 'textarea',
                        'columnOptions' => [
                            'style' => 'width:58%;resize: none;',
                        ],
                        'options' => [
                            'class' => 'tiny-mce', // Use this class to initialize TinyMCE
                        ],
                    ]
                ]
            ])->label();
?>
        </div>

        <?= $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', SaFaq::class)); ?>

    </div>
    <div class="box-footer">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <?php ActiveForm::end(); ?>
</div>