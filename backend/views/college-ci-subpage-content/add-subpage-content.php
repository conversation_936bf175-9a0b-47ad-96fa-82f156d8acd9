<?php

use common\helpers\DataHelper;
use common\models\CollegeCiSubpageContent;
use dosamigos\tinymce\TinyMce;
use yii\helpers\Html;
use yii\bootstrap\ActiveForm;
use unclead\multipleinput\TabularInput;
use unclead\multipleinput\TabularColumn;
use yii\helpers\ArrayHelper;

$this->title = 'College:' . $college_course_content->college->name . ' || ' . 'Course:' . $college_course_content->course->name;
$this->params['breadcrumbs'][] = ['label' => $college_course_content->college->name, 'url' => ['college/view?id=' . $college_course_content->college->id]];
$this->params['breadcrumbs'][] = ['label' => $college_course_content->course->name, 'url' => ['college-course-content/view?id=' . $college_course_content->id]];

?>
<style>
    #addSubpageWrapper {
        position: relative;
        width: fit-content;
    }

    #addSubpage {
        font-weight: bold;
        background: #6dcee1;
        color: #333;
        padding: 10px 15px;
        border-radius: 5px;
        cursor: pointer;
    }

    .content-header>h1 {
        font-weight: bold;
    }

    .multiple-input-list tbody input,
    .multiple-input-list tbody select,
    .multiple-input-list tbody textarea {
        width: 100% !important;
        /* Forces full width */
        max-width: 100%;
        /* Prevents any restrictions */
    }

    .multiple-input-list thead {
        display: none;
    }



    .multiple-input-list tbody {
        display: grid;
        grid-template-columns: repeat(2, minmax(200px, 1fr));
        gap: 10px;
    }

    table.multiple-input-list.table-renderer tr>td:first-child,
    table.multiple-input-list.table-renderer tr>td:last-child,
    .full-width {
        grid-column: span 2;
        /* Make first & last row span full width */
    }

    .multiple-input-list__item {
        display: contents;
    }

    table.multiple-input-list.table-renderer tr>td:first-child {
        padding-left: 5px;
    }

    .list-cell-static::before {
        content: attr(data-label);
        /* Adds title before field */
        font-weight: bold;
        font-size: 14px;
        display: block;
        margin-bottom: 5px;
        color: #333;
    }
</style>
<div class="subpage-content-form box box-primary">
    <?php $form = ActiveForm::begin([
        'id' => 'tabular-form',
        'options' => [
            'enctype' => 'multipart/form-data'
        ]
    ]) ?>
    <div class="box-body">

        <?= TabularInput::widget([
            'models' => $models,
            'modelClass' => CollegeCiSubpageContent::class,
            'allowEmptyList' => false,
            'min' => count($models),
            'max' => count($models),
            'addButtonPosition' => [
                TabularInput::POS_HEADER,
                // TabularInput::POS_FOOTER,
                TabularInput::POS_ROW
            ],
            'layoutConfig' => [
                'offsetClass' => 'col-md-offset-2',
                'labelClass' => 'col-md-2',
                'wrapperClass' => 'col-md-8',
                'errorClass' => 'col-md-12'
            ],
            'attributeOptions' => [
                'enableAjaxValidation'   => false,
                'enableClientValidation' => true,
                'validateOnChange'       => true,
                'validateOnSubmit'       => true,
                'validateOnBlur'         => false,
            ],
            'form' => $form,

            'columns' => [
                [
                    'name' => 'subpage',
                    'title' => 'Subpage',
                    'options' => ['disabled' => true],
                    'type' => TabularColumn::TYPE_DROPDOWN,
                    'items' => ArrayHelper::map(DataHelper::$courseCiPiSubpageItems, 'id', 'name'),
                    'columnOptions' => [
                        'class' => 'list-cell-static',
                        'data-label' => 'Subpage',
                    ],

                ],
                [
                    'name' => 'h1',
                    'title' => 'H1',
                    'type' => 'textarea',
                    'options' => [
                        'class' => 'form-control',
                        'rows' => 1
                    ],
                    'columnOptions' => [
                        'class' => 'list-cell-static',
                        'data-label' => 'H1',
                    ],
                ],
                [
                    'name' => 'meta_title',
                    'title' => 'Title',
                    'type' => 'textarea',
                    'options' => [
                        'class' => 'form-control',
                        'rows' => 1
                    ],
                    'columnOptions' => [
                        'class' => 'list-cell-static',
                        'data-label' => 'Title',
                    ],
                ],
                [
                    'name' => 'meta_description',
                    'title' => 'Description',
                    'type' => 'textarea',
                    'options' => [
                        'class' => 'form-control',
                        'rows' => 1
                    ],
                    'columnOptions' => [
                        'class' => 'list-cell-static',
                        'data-label' => 'Description',
                    ],
                ],
                [
                    'name' => 'year',
                    'title' => 'Year',
                    'type' => TabularColumn::TYPE_DROPDOWN,
                    'items' => DataHelper::$courseCiPiSubpageyearList,
                    'columnOptions' => [
                        'class' => 'list-cell-static',
                        'data-label' => 'Year',
                    ],
                    'options' => function ($model) {
                        return ['disabled' => $model->subpage == 'cut-off' ? false : true];
                    },
                ],
                [
                    'name' => 'position',
                    'title' => 'Position',
                    'type' => TabularColumn::TYPE_TEXT_INPUT,
                    'enableError' => true,
                    'options' => ['class' => 'form-control'],
                    'columnOptions' => [
                        'class' => 'list-cell-static',
                        'data-label' => 'Position',
                    ],
                ],
                [
                    'name' => 'status',
                    'title' => 'Status',
                    'type' => TabularColumn::TYPE_DROPDOWN,
                    'items' => DataHelper::getConstantList('STATUS', CollegeCiSubpageContent::class),
                    'options' => ['class' => 'form-control'],
                    'columnOptions' => [
                        'class' => 'list-cell-static',
                        'data-label' => 'Status',
                    ],
                ],
                [
                    'name' => 'content',
                    'title' => 'Content',
                    'type' => 'textarea',
                    'columnOptions' => ['style' => 'width:60%'],
                    'options' => function ($model) {
                        return [
                            'class' => 'tiny-mce',
                            'data-readonly' => ($model->subpage == 'cut-off' && $model->year == '') ? 'true' : 'false', // Add a custom data attribute
                        ];
                    },
                    'columnOptions' => function ($model) {
                        return [
                            'class' => 'list-cell-static full-width',
                            'data-label' => ($model->subpage == 'cut-off') ? 'Content --> Hint: Add year if content is disabled' : 'Content',
                            'style' => 'border-bottom: 2px solid #eeeeee !important; padding-bottom:30px;',
                        ];
                    },
                ],
            ],
        ]) ?>

    </div>

    <div class="box-footer">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <?php
    // Register TinyMCE asset
    TinyMce::widget([
        'name' => 'TinyMCE',
        'options' => [
            'class' => 'tiny-mce',
        ],
        'clientOptions' => [
            'plugins' => 'advlist autolink lists link image charmap print preview anchor',
            'toolbar' => 'undo redo | formatselect | bold italic backcolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | removeformat | help',
        ],
    ]);
    ActiveForm::end(); ?>
</div>

<?php
$this->registerJs(
    "
function initializeTinyMCE(selector = '.tiny-mce') {
    tinymce.init({
        selector: selector,
        menubar: false,
        plugins: 'link lists',
        toolbar: 'bold italic underline | bullist numlist | link',
        width: '100%',
        height: 200,
        setup: function (editor) {
            editor.on('init', function () {
                let isReadonly = editor.getElement().getAttribute('data-readonly') === 'true';
                if (isReadonly) {
                    editor.setMode('readonly'); // Disables the editor
                }
            });
        }
    });
}

$(document).ready(function() {
    $(window).on('scroll', function () {
        let cutOffSection = $(`tr[data-index='cut-off']`).first();
        let addButton = $('#addSubpageWrapper');
    
        if (!cutOffSection.length || !addButton.length) return;
    
        let sectionOffset = cutOffSection.offset().top;
        let sectionHeight = cutOffSection.outerHeight();
        let scrollTop = $(window).scrollTop();
    
        if (scrollTop >= sectionOffset - 10) {
            addButton.css({
                position: 'fixed',
                top: '10px',
                right: '20px',
                zIndex: 1000
            });
        } else {
            addButton.css({
                position: 'absolute',  // Use 'absolute' so it stays inside the parent row
                top: '0px', 
                right: 'auto'
            });
        }
    });

    $(document).on('change', `select[id^='collegecisubpagecontent-cut-off-'][id$='-year']`, function () {
        let contentField = $(this).closest('tr').find('.tiny-mce')[0]; // Find TinyMCE field in the same row
        if (!contentField) return;
    
        let editor = tinymce.get(contentField.id);
        if (!editor) return;
    
        if (this.value.trim() !== '') {
            editor.setMode('design'); // Enable editing when year is selected
        } else {
            editor.setMode('readonly'); // Disable editing when empty
        }
    });
    
    // $(`tr[data-index='cut-off']`).
    // after(`<a id='addSubpage' class='btn btn-primary ml-2' style = 'float:right; width:19%; font-weight: bold; background: #6dcee1; color: #333;'>Add Cut Off Subpage</a>`);
    
    $('#collegecisubpagecontent-cut-off-subpage')
    .after(`<div id='addSubpageWrapper'>
        <a id='addSubpage' class='btn btn-primary ml-2'>Add Cut Off Subpage</a>
    </div>`);


    $('#addSubpage').on('click', function() {

        let cutOffRow = $(`tr[data-index='cut-off']`).first();
        let clonedRow = cutOffRow.clone(); 

        let lastIndex = Math.max(0, ...$(`tr[data-index^='cut-off']`).map(function() {
            let dataIndex = $(this).attr('data-index');
            let match = dataIndex.match(/cut-off-(\d+)/);
            return match ? parseInt(match[1]) : (dataIndex === 'cut-off' ? 0 : null);
        }).get().filter(n => n !== null));

        let newIndex = 'cut-off-' + (lastIndex + 1);    4

        clonedRow.attr('data-index', newIndex);
        clonedRow.find('#addSubpage').remove();

        clonedRow.find('input, select, textarea').each(function() {
            let element = $(this);
            let name = element.attr('name');

            // if (element.attr('id') === 'collegecisubpagecontent-cut-off-status') {
            //     return; // Do nothing for status column
            // }

            if (name) {
                let newName = name.replace(/\[cut-off(?:-\d+)?\]/, '[' + newIndex + ']');
                element.attr('name', newName);
            }

            if (element.attr('id') === 'collegecisubpagecontent-cut-off-subpage') {
                element.val('cut-off');
            } else if(element.attr('id') === 'collegecisubpagecontent-cut-off-status') {
                element.val(1);
            } else {
                element.val('');
            }

            // Update textarea ID dynamically
            if (element.is('textarea.tiny-mce')) {
                let newId = 'collegecisubpagecontent-' + newIndex + '-content';
                element.attr('id', newId);
                element.attr('data-readonly', 'true');
                
                // Destroy existing TinyMCE instance if any
                if (tinymce.get(newId)) {
                    tinymce.remove('#' + newId);
                }

                // Show the textarea before initializing TinyMCE
                element.show();

                setTimeout(() => {
                    initializeTinyMCE('#' + newId);

                    let editor = tinymce.get(newId);
                if (editor) {
                    editor.setMode('readonly');
                }

                    // Hide the second TinyMCE editor
            document.getElementById(newId)
            ?.closest('div')
            ?.querySelectorAll('.tox.tox-tinymce')[1]
            ?.style.setProperty('display', 'none', 'important');
                }, 100);
            }
        });

        $(`tr[data-index^='cut-off']`).last().after(clonedRow);
        
        checkYearCountLimit();
    });

    function checkYearCountLimit() {
        let yearCount = $(`tr[data-index^='cut-off'] select[name$='[year]'] option:selected`).length;
        let maxYears = 8; 
    
        if (yearCount >= maxYears) {
            $('#addSubpage').hide();
        } else {
            $('#addSubpage').show();
        }
    }

    initializeTinyMCE(); 
});
",
    \yii\web\View::POS_READY
);
?>