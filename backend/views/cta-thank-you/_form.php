<?php

use common\helpers\DataHelper;
use common\models\CtaThankYou;
use frontend\helpers\Url;
use kartik\select2\Select2;
use yii\helpers\Html;
use yii\web\JsExpression;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model common\models\CtaThankYou */
/* @var $form yii\widgets\ActiveForm */
?>

<div class="cta-thank-you-form box box-primary">
    <?php $form = ActiveForm::begin(); ?>
    <div class="box-body table-responsive">

        <?= $form->field($model, 'entity_type')->dropDownList(
            array_merge(['' => 'Select Entity Type'], DataHelper::getConstantList('LEAD_ENTITY', CtaThankYou::class))
        ) ?>

        <?= $form->field($model, 'cta_text')->widget(Select2::classname(), [
            'data' => $user ?? [],
            'options' => [
                'placeholder' => '--Select--',
                'disabled' => !$model->isNewRecord ? true : false,
                'options' => [],
            ],
            'pluginOptions' => [
                'allowClear' => true,
                'minimumInputLength' => 3,
                'maximumInputLength' => 10,
                'language' => [
                    'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                ],
                'ajax' => [
                    'url' => ['../ajax/all-cta-data'],
                    'dataType' => 'json',
                    'data' => new JsExpression('function(params) {return {q:params.term}; }')
                ],
                'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                'templateResult' => new JsExpression('function(data) { return data.text; }'),
                'templateSelection' => new JsExpression('function (data) { return data.text; }'),
            ],
        ])->label('CTA Text');
?>

        <?=
        $this->render('/widget/tinymce', [
            'form' => $form,
            'model' => $model,
            'entity' => 'thank_you_text',
            'type' => ''
        ])
        ?>

        <?= $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', CtaThankYou::class)) ?>

    </div>
    <div class="box-footer">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <?php ActiveForm::end(); ?>
</div>