<?php

use common\models\BoardPages;
use yii\helpers\Html;
use yii\widgets\ActiveForm;
use kartik\depdrop\DepDrop;
use kartik\select2\Select2;
use yii\helpers\ArrayHelper;
use yii\helpers\Url;
use yii\web\JsExpression;
use common\helpers\DataHelper;

/* @var $this yii\web\View */
/* @var $model common\models\BoardPages */
/* @var $form yii\widgets\ActiveForm */
if (!empty($model->parent)) {
    $pageData[] = ArrayHelper::map(BoardPages::find()->where(['is', 'parent_id', new \yii\db\Expression('null')])->asArray()->all(), 'id', 'name');
}
if (!$model->isNewRecord) {
    $selectedParentPage = [];
    if (!empty($pageData)) {
        foreach ($pageData as $value) {
            if (!empty($value)) {
                $selectedParentPage[array_keys($value)[0]] = ['selected' => true];
            }
        }
    }
}
?>

<div class="board-pages-form  box box-primary">
    <?php $form = ActiveForm::begin(); ?>
    <div class="box-body">
    <div class="row">
        <div class="col-md-6">
        <?= $form->field($model, 'parent_id')->widget(Select2::classname(), [
                        'data' => $pageData ?? [], //array of text to show in the field for the selected items
                        'options' => [
                            'placeholder' => '--Select--',
                            'multiple' => false,
                            'options' => $selectedParentPage ?? [],
                            'disabled' => !$model->isNewRecord ? true :false
                        ],
                        'pluginOptions' => [
                            'allowClear' => true,
                            'minimumInputLength' => 3,
                            'language' => [
                                'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                            ],
                            'ajax' => [
                                'url' => Url::to(['/board-pages/get-page-list']),
                                'dataType' => 'json',
                                'data' => new JsExpression('function(params) {return {q:params.term}; }')
                            ],
                            'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                            'templateResult' => new JsExpression('function(data) { return data.text; }'),
                            'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                        ],
                    ])->label('Select Page(if name as subpage)');
?>
       
        </div>
        <div class="col-md-6">
          <?= $form->field($model, 'name')->textInput(['disabled' => !$model->isNewRecord]) ?>
        </div>
    </div>
    <div class="row">
         <div class="col-md-6">
               <?= $form->field($model, 'title')->textInput(['maxlength' => true]) ?>
        </div>
        <div class="col-md-6">
                <?= $form->field($model, 'h1')->textInput(['maxlength' => true]) ?>
            </div>
    </div>
    
    <div class="row">
             <div class="col-md-6">
             <?=
                $this->render('/widget/tinymce', [
                'form' => $form,
                'model' => $model,
                'type' => 'board-pages'
                ])
                ?>
            </div>
            <div class="col-md-6">
                    <?php if ($model->isNewRecord) {
                        echo $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', BoardPages::class), ['options' => [1 => ['selected' => true]]]);
                    } else {
                        echo $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', BoardPages::class));
                    } ?>
                </div>
        </div>
    </div>
        <div  class="col-md-12 box-footer">
            <?= Html::submitButton('Save', ['class' => 'btn btn-success']) ?>
        </div>
    </div>
    <?php ActiveForm::end(); ?>
</div>
