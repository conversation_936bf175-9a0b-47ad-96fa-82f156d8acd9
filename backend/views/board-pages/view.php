<?php

use yii\helpers\Html;
use yii\widgets\DetailView;
use common\helpers\DataHelper;
use common\models\BoardPages;
use yii\helpers\ArrayHelper;

/* @var $this yii\web\View */
/* @var $model common\models\BoardPages */

$this->title = $model->name;
$this->params['breadcrumbs'][] = ['label' => 'Board Pages', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
\yii\web\YiiAsset::register($this);
?>
<div class="board-pages-view box box-primary">
   <div class="box-header">
        <?= Html::a('Update', ['update', 'id' => $model->id], ['class' => 'btn btn-primary']) ?>
   </div>
   <div class="box-body table-responsive no-padding">
        <?= DetailView::widget([
            'model' => $model,
            'attributes' => [
                'name',
                'slug',
                'h1',
                'title',
                [
                    'label' => 'Description',
                    'format' => 'raw',
                    'value' => html_entity_decode($model->description),
                ],
                [
                    'attribute' => 'parent_id',
                    'label' => 'Parent Page',
                    'format' => 'raw',
                    'value' => $model->parent ?  $model->parent->name : Html::tag('span', '(not set)', ['class' => 'not-set'])
                ],
                [
                    'label' => 'Status',
                    'attribute' => 'status',
                    'value' => ArrayHelper::getValue(DataHelper::getConstantList('STATUS', BoardPages::class), $model->status)
                ],
                'created_at',
                'updated_at',
            ],
        ]) ?>
   </div>
</div>
