<?php

use yii\helpers\Html;
use yii\grid\GridView;
use common\helpers\DataHelper;
use common\models\BoardPages;
use yii\helpers\ArrayHelper;
use kartik\select2\Select2;

/* @var $this yii\web\View */
/* @var $searchModel common\models\BoardPagesSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Board Pages';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="board-pages-index box box-primary">

<div class="box-header with-border">
        <?= Html::a('Create Board Pages', ['create'], ['class' => 'btn btn-success']) ?>
    </div>
   
    <?php // echo $this->render('_search', ['model' => $searchModel]); ?>
   <div class="box-body table-responsive no-padding">
    <?= GridView::widget([
        'dataProvider' => $dataProvider,
        'filterModel' => $searchModel,
        'columns' => [
            ['class' => 'yii\grid\SerialColumn'],

            'name',
            'slug',
            [
                'attribute' => 'parent_id',
                'value' => function ($model) {
                    return  $model->parent->name ?? '';
                },
                'label' => 'Parent',
                'filter' => Select2::widget(
                    [
                        'model' => $searchModel,
                        'attribute' => 'parent_id',
                        'data' => ArrayHelper::map(BoardPages::find()->where(['is', 'parent_id', new \yii\db\Expression('null')])->asArray()->all(), 'id', 'name'),
                        'options' => ['placeholder' => ' --Filter by Parent-- '],
                        'language' => 'en',
                        'pluginOptions' => [
                            'allowClear' => true,
                        ],
                    ]
                ),
            ],
            [
                'attribute' => 'status',
                'value' => function ($model) {
                    return ArrayHelper::getValue(DataHelper::getConstantList('STATUS', BoardPages::class), $model->status);
                },
                'filter' => DataHelper::getConstantList('STATUS', BoardPages::class)
            ],
            [
                'class' => 'yii\grid\ActionColumn','template' => '{view} {update}',
            ],
        ],
    ]); ?>

   </div>
</div>
