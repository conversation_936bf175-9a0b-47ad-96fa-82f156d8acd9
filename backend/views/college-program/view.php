<?php

use common\helpers\CollegeHelper;
use common\helpers\DataHelper;
use common\models\CollegeCourse;
use common\models\CollegeProgram;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model common\models\CollegeCourse */

$this->title = $model->program->name ?? '';
$this->params['breadcrumbs'][] = ['label' => ($model->college ? ($model->college->name ?? $model->college->display_name) : $model->id), 'url' => '/college/view?id=' . $model->college->id];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="college-course-view box box-primary">
    <div class="box-header">
        <?= Html::a('College Program Index', ['index?CollegeProgramSearch[course_id]=' . $model->course->id . '&' . 'CollegeProgramSearch[college_id]=' . $model->college->id], ['class' => 'btn btn-primary btn-flat']) ?>
        <?= Html::a('Update', ['update', 'id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
        <?php if ($model->page_index == 1): ?>
            <?= Html::a('Pi Subpage Content', ['college-pi-subpage-content/add-pi-subpage-content', 'college_program_id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
        <?php endif; ?>
        <?= Html::a('Add Fees', ['college-program-fees/create', 'id' => $model->id], ['class' => 'btn btn-success btn-flat']) ?>
        <?php if (!empty($model->collegeProgramContent)): ?>
            <?php if (!empty($model->collegeProgramContent->content) && !empty($model->collegeProgramContent->qualification)): ?>
                <?= Html::a('Update Program Content', ['college-program-content/update', 'id' => $model->collegeProgramContent->id], ['class' => 'btn btn-success btn-flat']) ?>
            <?php else: ?>
                <?php if (!empty($model->collegeProgramContent->template_id)): ?>
                    <?= Html::a('Update Program Content', ['college-program-content/update', 'id' => $model->collegeProgramContent->id], ['class' => 'btn btn-success btn-flat']) ?>
                <?php endif;
            endif; ?>
        <?php else:
            if (!empty($model->program_id)):
                ?>
                <?= Html::a('Add Program Content', ['college-program-content/create', 'id' => $model->id], ['class' => 'btn btn-success btn-flat']) ?>
            <?php endif;
        endif; ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?= DetailView::widget([
            'model' => $model,
            'attributes' => [
                [
                    'attribute' => 'college_id',
                    'label' => 'College',
                    'value' => function ($data) {
                        return $data->college ? $data->college->name : '';
                    }
                ],
                [
                    'attribute' => 'course_id',
                    'value' => function ($data) {
                        return $data->course ? $data->course->name : '';
                    }
                ],
                [
                    'attribute' => 'program_id',
                    'value' => function ($data) {
                        return $data->program ? $data->program->name : '';
                    }
                ],
                'duration',
                'seat',
                [
                    'attribute' => 'duration',
                    'value' => function ($data) {
                        if (isset($data->duration_type) && !empty(CollegeHelper::$programDurationType[$data->duration_type])) {
                            return CollegeHelper::$programDurationType[$data->duration_type];
                        }
                    }
                ],
                'application_link',
                'salary',
                // [
                //     'attribute' => 'type',
                //     'label' => 'Type',
                //     'value' => ArrayHelper::getValue(DataHelper::getConstantList('MODE', CollegeCourse::class), $model->mode)
                // ],
                'position',
                [
                    'attribute' => 'page_index',
                    'label' => 'Pgae Index',
                    'value' => ArrayHelper::getValue(DataHelper::getConstantList('STATUS', CollegeCourse::class), $model->page_index)
                ],
                [
                    'attribute' => 'status',
                    'label' => 'Status',
                    'value' => ArrayHelper::getValue(DataHelper::getConstantList('STATUS', CollegeCourse::class), $model->status)
                ],
                'created_at:datetime',
                'updated_at:datetime',
            ],
        ]) ?>
    </div>
</div>