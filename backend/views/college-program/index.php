<?php

use common\grid\EnumColumn;
use common\helpers\CollegeHelper;
use common\helpers\DataHelper;
use common\models\Brochure;
use common\models\College;
use common\models\CollegeCourse;
use common\models\Course;
use yii\helpers\Html;
use yii\grid\GridView;
use yii\helpers\ArrayHelper;

/* @var $this yii\web\View */
/* @var $searchModel backend\models\CollegeCourseSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$college =  College::find()->where(['id' => $searchModel['college_id']])->one();
$course =  Course::find()->where(['id' => $searchModel['course_id']])->one();
if (!empty($college)) {
    $brochure = Brochure::find()->where(['college_id' => $college->id])->andWhere(['course_id' => $course->id])->andWhere(['entity' => 'course'])->one();
}
$title = !empty($brochure) ? 'Update Brochure' : 'Create Brochure';
$pageSizeArr = [
    10 => 10, 20 => 20, 30 => 30, 50 => 50, 100 => 100
];
$this->title = 'College Program';
if (!empty($college)) {
    $this->title = $college->name ?? $college->id;
    $this->params['breadcrumbs'][] = ['label' => $college->name ?? $college->id, 'url' => '/college/view?id=' . $college->id];
}
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="college-course-index box box-primary">
    <div class="box-header with-border">
        <?php if (!empty($college) && !empty($course) && !empty($brochure)): ?>
            <?= Html::a($title, ['brochure/update', 'id' => $brochure->id], ['class' => 'btn btn-success btn-flat', 'style' => 'float: right; margin-right: 10px;']) ?>
        <?php elseif (!empty($college) && !empty($course)): ?>
            <?= Html::a($title, ['brochure/create', 'college_id' => $college->id, 'course_id' => $course->id], ['class' => 'btn btn-success btn-flat', 'style' => 'float: right; margin-right: 10px;']) ?>
        <?php elseif (!empty($college) && !empty($brochure)): ?>
            <?= Html::a($title, ['brochure/update', 'id' => $brochure->id], ['class' => 'btn btn-success btn-flat', 'style' => 'float: right; margin-right: 10px;']) ?>
        <?php elseif (!empty($college)): ?>
            <?= Html::a($title, ['brochure/create', 'college_id' => $college->id], ['class' => 'btn btn-success btn-flat', 'style' => 'float: right; margin-right: 10px;']) ?>
        <?php else: ?>
            <?php return false; ?>
        <?php endif; ?>
        <?= Html::a('Create College Program', ['create', 'college-id' => $college->id], ['class' => 'btn btn-success btn-flat', 'style' => 'float: right; margin-right: 10px;']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?php // echo $this->render('_search', ['model' => $searchModel]);
        ?>
        <?= GridView::widget([
            'dataProvider' => $dataProvider,
            'filterModel' => $searchModel,
            'layout' => '{summary}' . Html::activeDropDownList($searchModel, 'pageSize', $pageSizeArr, ['id' => 'myPageSize']) . '{items}<br/>{pager}',
            // 'layout' => "{items}\n{summary}\n{pager}",
            'columns' => [
                ['class' => 'yii\grid\SerialColumn'],

                // 'id',
                [
                    
                    'label' => 'College Program Id',
                    'attribute' => 'id',
                    'value' => 'id'
                ],
                [
                    'class' => EnumColumn::class,
                    'label' => 'Course Type',
                    'attribute' => 'course',
                    'value' => 'course.name',
                ],
                [
                    'label' => 'Program Name',
                    'attribute' => 'program_name',
                    'value' => function ($model) {
                        return $model->program->name ?? '';
                    }
                ],
                [
                    'label' => 'Slug',
                    'attribute' => 'program_id',
                    'value' => function ($model) {
                        return $model->program->slug ?? '';
                    }
                ],
                [
                    'class' => EnumColumn::class,
                    'label' => 'Mode',
                    'attribute' => 'mode',
                    'value' => 'program.mode'
                ],
                [
                    'class' => EnumColumn::class,
                    'label' => 'Type',
                    'attribute' => 'type',
                    'value' => function ($model) {
                        return !empty($model->program->type) ? CollegeHelper::$type[$model->program->type] : '';
                    }
                ],
                [
                    'attribute' => 'status',
                    'value' => function ($model) {
                        return ArrayHelper::getValue(DataHelper::getConstantList('STATUS', CollegeCourse::class), $model->status);
                    },
                    'filter' => DataHelper::getConstantList('STATUS', CollegeCourse::class)
                ],
                ['class' => 'yii\grid\ActionColumn', 'template' => '{view}'],

            ],
        ]); ?>
    </div>
</div>

<?php
echo $this->registerJs(
    " $('document').ready(function(){
        $('#myPageSize').on('change', function(){
          var perPage = this.value;
          //get the URl 
          
          url = new URL(window.location.href);
          
          //setting the value to param
          var search_params = url.searchParams;
            search_params.set('pageSize', perPage);
        var new_url = url.toString();
            window.location.href = new_url;
        });
    });"
);
echo $this->registerCSS(
    'select#myPageSize {
        margin: 16px;
        padding: 4px 32px;
    }
    .table {
        overflow: auto;
    }'
);
?>