<?php

use yii\helpers\Html;
use yii\grid\GridView;

/* @var $this yii\web\View */
/* @var $searchModel backend\models\CollegeListingPageExperienceSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'College Listing Page Experiences';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="college-listing-page-experience-index box box-primary">
   

    <?php // echo $this->render('_search', ['model' => $searchModel]); ?>

    <?= GridView::widget([
        'dataProvider' => $dataProvider,
        'filterModel' => $searchModel,
        'columns' => [
            'rating',
            'experience_text:ntext',
            'rating_option',
            'url:url',
            //'created_at',
            //'updated_at',

            [
                'class' => 'yii\grid\ActionColumn',
                'header' => 'Actions',
                'template' => '{view}'
            ],
        ],
    ]); ?>


</div>
