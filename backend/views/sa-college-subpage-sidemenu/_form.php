<?php

use common\helpers\DataHelper;
use common\models\SaCollegeSubpageSidemenu;
use common\models\User;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model common\models\SaCollegeSubpageSidemenu */
/* @var $form yii\widgets\ActiveForm */

$user =  ArrayHelper::map(User::find()->where(['id' => Yii::$app->user->identity->id])->all(), 'id', 'name');
$disable = $model->isNewRecord ? false : true;
?>

<div class="sa-college-subpage-sidemenu-form box box-primary">
    <?php $form = ActiveForm::begin(); ?>
    <div class="box-body table-responsive">

        <?= $form->field($model, 'created_by')->dropDownList(
            ArrayHelper::map(User::find()->all(), 'id', 'name'),
            [
                'options' => [
                    Yii::$app->user->identity->id => ['Selected' => true], // Set the selected value
                ],
                'disabled' => true // Disable the entire dropdown
            ]
        )->label('Author'); ?>

        <?= $form->field($model, 'name')->textInput(['maxlength' => true, 'disabled' => $disable]) ?>

        <?= $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', SaCollegeSubpageSidemenu::class)) ?>

    </div>
    <div class="box-footer">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <?php ActiveForm::end(); ?>
</div>