<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;
use common\helpers\DataHelper;
use common\models\MediaDrive;

/* @var $this yii\web\View */
/* @var $model common\models\MediaDrive */
/* @var $form yii\widgets\ActiveForm */
$entityKey = $model->entity;
$entitiesArr = DataHelper::$entities;
$entitiesArr['others'] = 'OTHERS';
$entityValue = isset($entitiesArr[$entityKey]) ? $entitiesArr[$entityKey] : 'Default Value';
?>

<div class="faq-form box box-primary">
    <?php $form = ActiveForm::begin(); ?>
    <div class="box-body table-responsive">
        <div class="col-md-6">
            <?= $form->field($model, 'entity')->textInput(['value' => $entityValue, 'maxlength' => true, 'disabled' => true]) ?>
        </div>
        <div class="col-md-6">
            <?php if ($model->isNewRecord) {
                echo $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', MediaDrive::class), ['options' => [2 => ['selected' => true]]]);
            } else {
                echo $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', MediaDrive::class));
            } ?>
        </div>
        <?php if ($model->entity !== 'others') { ?>
        <div class="col-md-12">
            <?= $form->field($model, 'entity_id')->textInput(['value' => $model->getTitle(), 'maxlength' => true, 'disabled' => true]) ?>
        </div>
        <div class="col-md-12">
            <?= $form->field($model, 'sub_page')->textInput(['value' => $model->getUploadTypeAttribute(), 'maxlength' => true, 'disabled' => true])->label('Upload Type') ?>
        </div>

        <?php } ?>
        <div class="form-group col-md-6">
            <?php echo Html::submitButton(Yii::t('backend', 'Update'), ['btn btn-success btn-flat']) ?>
        </div>
    </div>
    <?php ActiveForm::end() ?>
</div>