<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;
use common\helpers\DataHelper;
use yii\helpers\ArrayHelper;
use common\models\MediaDrive;
use kartik\depdrop\DepDrop;
use kartik\select2\Select2;
use yii\web\JsExpression;
use yii\helpers\Url;

/* @var $this yii\web\View */
/* @var $model common\models\MediaDrive */
/* @var $form yii\widgets\ActiveForm */

$entitiesArr = DataHelper::$entities;
$entitiesArr['others'] = 'OTHERS';
$excludedArr = ['Select...', 'NCERT', 'FILTER', 'CAREER', 'COURSE', 'ARTICLE', 'NEWS'];
foreach ($excludedArr as $value) {
    if (($key = array_search($value, $entitiesArr)) !== false) {
        unset($entitiesArr[$key]);
    }
}
$currentYear = date('Y', time());
$year = array_reverse(range(($currentYear - 10), date('Y')));
?>

<div class="faq-form box box-primary">
    <?php $form = ActiveForm::begin(); ?>
    <div class="box-body table-responsive">
        <div class="col-md-6">
            <?= $form->field($model, 'entity')->dropDownList($entitiesArr, [
                'id' => 'entity',
                'options' => [
                    'others' => ['Selected' => true],
                ],
                'disabled' => !$model->isNewRecord,
            ])->label('Type*') ?>
        </div>
        <div class="col-md-6">
            <?php if ($model->isNewRecord) {
                echo $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', MediaDrive::class), ['options' => [2 => ['selected' => true]]])->label('Status*');
            } else {
                echo $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', MediaDrive::class))->label('Status*');
            } ?>
        </div>

        <div class="col-md-12">
            <?= $form->field($model, 'entity_id')->widget(Select2::classname(), [
                'data' => !empty($model->pageName) ? ArrayHelper::map($model->pageName, 'id', 'name') : [],
                'language' => 'en',
                'options' => [
                    'placeholder' => '--Select--',
                    'multiple' => false,
                ],
                'pluginOptions' => [
                    'allowClear' => true,
                    'placeholder' => '--Select--',
                    'disabled' => !$model->isNewRecord,
                    'minimumInputLength' => 3,
                    'language' => [
                        'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                    ],
                    'ajax' => [
                        'url' => Url::to(['/faq/get-list']),
                        'dataType' => 'json',
                        'data' => new JsExpression("function(params) {return {q:params.term,depdrop_parents:$('#entity').val()}; }")
                    ],
                    'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                    'templateResult' => new JsExpression('function(data) { return data.text; }'),
                    'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                ],
            ])->label('Select Exam/Board/College*'); ?>
        </div>

        <div class="col-md-12">
            <?= $form->field($model, 'sub_page')->widget(DepDrop::class, [
                'type' => DepDrop::TYPE_SELECT2,
                'options' => [
                    'id' => 'sub_page',
                    'placeholder' => '--Select--',
                    'multiple' => false,
                ],
                'data' => [],
                'select2Options' => ['pluginOptions' => [
                    'allowClear' => true,
                    'disabled' => !$model->isNewRecord ?? (strtolower($model->entity) == 'article') ? true : false
                ]],
                'pluginOptions' => [
                    'depends' => ['entity'],
                    'placeholder' => 'Select...',
                    'url' => Url::to(['/media-drive/upload-options-page'])
                ],
            ])->label('Upload Type*');
?>
        </div>
    </div>

    <div class="exam-container col-md-12">
        <div class="col-md-4">
            <?= $form->field($model, 'year')->dropDownList($year) ?>
        </div>
        <div class="col-md-4">
            <?= $form->field($model, 'slot')->dropDownList(DataHelper::getConstantList('SLOT', MediaDrive::class)) ?>
        </div>
        <div class="col-md-4">
            <?= $form->field($model, 'source')->dropDownList(DataHelper::getConstantList('SOURCE', MediaDrive::class)) ?>
        </div>
        <div class="col-md-6">
            <?= $form->field($model, 'section')->dropDownList(DataHelper::getConstantList('SECTION', MediaDrive::class)) ?>
        </div>
        <div class="col-md-6">
            <?= $form->field($model, 'subject')->textInput(['maxlength' => true]) ?>
        </div>
    </div>

    <div class="col-md-12">
        <div class="image-desc-container ">
            <label for="lname">Document Description:*</label>
            <input class="form-control" type="text" id="image-desc" placeholder="Brief description">
        </div>

        <form action="/media-drive/upload" class="dropzone" id="dropzoneFrom">
        </form>
    </div>
    <br />
    <br />
    <div align="center">
        <button type="button" class="btn btn-info" id="cancel-all">Cancel</button>
        <button type="button" class="btn btn-info" id="submit-all" disabled="true">Submit</button>
    </div>
    <br>
    <div class="notice-container">
        <div class="alert alert-info notice-file-ext" role="alert">
            Allowed file extensions: .pdf
        </div>
        <div class="alert alert-info notice-file-size" role="alert">
            Allowed maximum file size: 50 MB
        </div>
    </div>
    <div class="error-message"></div>
    <dialog class="successfulFileUpload">
        <p class="snackbar">Your File Has Been Uploaded With Description!</p>
    </dialog>
    <?php
    echo $this->registerCSS(
        '.notice-container{
            display:flex;
            justify-content: space-between;
            align-items:center;
            padding: 0 10px;
            position: relative;
        }
        .notice-container .alert.alert-info {
            padding: 5px 10px;
            background-color: #d1ecf1 !important;
            color: #0c5460 !important;
            border-color: #bee5eb;
        }
        .error-message {
            margin-top: 10px;
            padding: 0 10px;
            text-align: center;
            font-weight: 600;
            color: #ff4e53;
        }
        .image-desc-container {
            margin: 10px 10px 10px 10px;
        }
        .successfulFileUpload{
            outline: none;
            border: none;
            padding: 0;
            width: 100%;
            height: 100%;
            background-color: transparent;
          }
          .successfulFileUpload:focus{
            outline: none;
          }
          .successfulFileUpload::backdrop{
            background-color: transparent;
          }
          .successfulFileUpload p{
            width: 746px;
            padding: 8px 30px;
            text-align: center;
            height: 40px;
            border-radius: 3px;
            background-color: #282828;
            font-size: 14px;
            font-weight: normal;
            line-height: 1.71;
            color: #fff;
            top: 0px;
            left: 50%;
            transform: translateX(-50%);
            position: absolute;
          }
          #dropzoneFrom {
            background: white !important;
            border-radius: 5px;
            border: 2px dashed rgb(0, 135, 247);
            border-image: none;
            max-width: 500px;
            margin-left: auto;
            margin-right: auto;
        }
        '
    );
    ?>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.9.1/jquery.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.5.1/dropzone.css" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.5.1/dropzone.js"></script>

    <script defer>
        $(document).ready(function() {
            $('.form-group.field-mediadrive-entity_id, .form-group.field-sub_page, .exam-container').hide();
            $('#entity').change(function(event) {
                let entity = event.target.value;
                (entity === 'exam') ? $('.exam-container').show(): $('.exam-container').hide();
                (entity === 'others') ? $('.form-group.field-mediadrive-entity_id, .form-group.field-sub_page').hide(): $('.form-group.field-mediadrive-entity_id, .form-group.field-sub_page').show();
            })
        })
        // Function to validate form inputs
        function validateForm() {
            let isValid = true;
            let errorMessage = '';

            // Use the correct selector for Select2 value
            const entityValue = $('#entity').val();
            const entityIdValue = $('#mediadrive-entity_id').val();
            const subPageValue = $('#sub_page').val();
            const descriptionValue = $('#image-desc').val();

            if ((!entityIdValue || entityIdValue === '') && entityValue !== 'others') {
                errorMessage = 'Please select the exam/college/board.';
                isValid = false;
            } else if ((!subPageValue || subPageValue === '') && entityValue !== 'others') {
                errorMessage = 'Please select the upload type.';
                isValid = false;
            } else if (!descriptionValue || descriptionValue === '') {
                errorMessage = 'Please add a brief document description.';
                isValid = false;
            }

            if (!isValid) {
                $(".error-message").text(errorMessage);
                return false; // Prevent form submission if validation fails
            }
            return true; // Allow form submission if validation passes
        }

        Dropzone.options.dropzoneFrom = {
            maxFilesize: 50, // megabytes
            chunking: true,
            parallelUploads: 1,
            uploadMultiple: false,
            parallelChunkUploads: true,
            retryChunks: true,
            retryChunksLimit: 0,
            maxFiles: 1,
            forceChunking: true,
            chunkSize: 1000000,
            acceptedFiles: ".pdf",
            clickable: true,
            autoProcessQueue: false,
            headers: {
                'X-CSRF-TOKEN': document.querySelector("meta[name='csrf-token']").getAttribute("content")
            },
            chunksUploaded: function(file, done) {
                // All chunks have been uploaded. Perform any other actions
                let currentFile = file;

                // This calls server-side code to merge all chunks for the currentFile
                $.ajax({
                    url: "/media-drive/chunk-concat?dzuuid=" + currentFile.upload.uuid +
                        "&dztotalchunkcount=" + currentFile.upload.totalChunkCount +
                        "&fileName=" + currentFile.name.substr((currentFile.name.lastIndexOf('.') + 1)) +
                        "&desc=" + $('#image-desc').val() +
                        "&entity=" + $('#entity').val() +
                        "&entity_id=" + $('#mediadrive-entity_id option:selected').val() +
                        "&sub_page=" + $('#sub_page option:selected').val() +
                        "&year=" + $('#mediadrive-year').val() +
                        "&slot=" + $('#mediadrive-slot').val() +
                        "&source=" + $('#mediadrive-source').val() +
                        "&section=" + $('#mediadrive-section').val() +
                        "&subject=" + $('#mediadrive-subject').val() +
                        "&status=" + $('#mediadrive-status').val(),
                    success: function(data) {
                        if (JSON.parse(data).status == 'error') {
                            $(".error-message").text(JSON.parse(data).info);
                            $("select").prop("disabled", true);
                            setTimeout(() => {
                                location.reload();
                            }, 10000);
                        } else {
                            let snackbar = document.querySelector('.successfulFileUpload');
                            snackbar.showModal();
                            setTimeout(() => {
                                location.reload();
                            }, 3000);
                        }
                    },
                    error: function(msg) {
                        currentFile.accepted = false;
                        myDropzone._errorProcessing([currentFile], msg.responseText);
                        $(".error-message").text('Something went wrong.');
                        setTimeout(() => {
                            location.reload();
                        }, 3000);
                    }
                });
            },
            init: function() {
                var submitButton = document.querySelector('#submit-all');
                myDropzone = this;
                this.on("addedfile", function(file) {
                    $("#submit-all").prop("disabled", false);
                });
                this.on("maxfilesexceeded", function(file) {});
                this.on("error", function(file, message) {
                    $("#submit-all").prop("disabled", true);
                    $(".error-message").text(message);
                    this.removeFile(file);
                });
                // Event listener for the submit button
                submitButton.addEventListener("click", function(event) {
                    if (validateForm()) {
                        $("#submit-all").prop("disabled", true); // Disable the submit button
                        myDropzone.processQueue(); // Process Dropzone uploads
                    } else {
                        event.preventDefault(); // Prevent default form submission if validation fails
                    }
                });
                var cancelButton = document.querySelector('#cancel-all');
                cancelButton.addEventListener("click", function() {
                    myDropzone.removeAllFiles(true);
                    $("#submit-all").prop("disabled", true);
                });
            }
        };
    </script>