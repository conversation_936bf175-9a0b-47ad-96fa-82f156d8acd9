<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;

$this->title = 'Script Injection';
$this->params['breadcrumbs'][] = ['label' => 'Custom Landing Pages', 'url' => ['index']];
$this->params['breadcrumbs'][] = ['label' => $model->slug, 'url' => ['view', 'id' => $model->id]];
$this->params['breadcrumbs'][] = $this->title;

?>

<div class="custom-landing-page-form box box-primary">
    <?php $form = ActiveForm::begin(); ?>
    <div class="box-body table-responsive">
        <?php foreach ($scriptKeys as $key):
            ?>
            <div class="form-group">
                <label><?= Html::encode(ucfirst($key)) ?></label>
                <?= Html::textArea("script_injection[$key]", isset($scriptData[$key]) ? $scriptData[$key] : '', [
                    'class' => 'form-control',
                    'rows' => 3,
                ]) ?>
            </div>
        <?php endforeach; ?>

        <?= $form->field($model, 'id')->hiddenInput()->label(false) ?>

        <div class="form-group">
            <?= Html::submitButton('Save', ['class' => 'btn btn-primary']) ?>
        </div>
    </div>
    <?php ActiveForm::end(); ?>
</div>