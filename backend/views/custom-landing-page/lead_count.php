<?php

use kartik\date\DatePicker;
use yii\grid\GridView;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\widgets\ActiveForm;

$this->title = 'Lead Count Summary Panel : ' . $clpSlug;
$this->params['breadcrumbs'][] = ['label' => 'Custom Landing Pages', 'url' => ['index?CustomLandingPageSearch[slug]=' . $clpSlug]];
?>
<style>
    .badge {
        color: #444;
        padding: 10px 15px;
        background-color: #9999992b;
        font-size: 14px;
        margin-right: 5px;
        border-radius: 20px;
        border: 1px solid #ccc;
    }

    .lead-panel-index-heading {
        align-items: center;
        margin-bottom: 20px;
        float: left;
    }
</style>
<div class="lead-panel-index container mt-4 box box-primary">
    <div class="lead-panel-index-heading container mt-4 box-header">
        <span class="badge badge-info">Total Lead Count: <?= $totalCount ?></span>
        <span class="badge badge-info">Distinct Lead Count: <?= $distinctCount ?></span>
        <?php if (!empty($searchModel->created_at_from) || !empty($searchModel->created_at_to)): ?>
            <span class="badge badge-info">Result Count: <?= $dataProvider->getTotalCount() ?></span>
        <?php endif; ?>
        <?php if ($totalCount > 0 || $distinctCount > 0): ?>
            <?= Html::a(
                'Export All',
                ['export-day', 'slug' => $clpSlug],
                ['class' => 'btn btn-sm btn-success']
            ) ?>
        <?php endif; ?>
    </div>

    <div class="box-body">
        <div class="card mb-4 p-3 shadow-sm">
            <?php $form = ActiveForm::begin([
                'method' => 'get',
                'action' => ['lead-count-index', 'slug' => $clpSlug],
            ]); ?>
            <div class="row">
                <div class="col-md-4">
                    <?= $form->field($searchModel, 'created_at_from')->widget(DatePicker::classname(), [
                        'type' => DatePicker::TYPE_COMPONENT_APPEND,
                        'options' => [
                            'autocomplete' => 'off', // disables browser autofill
                            'placeholder' => 'From Date',
                        ],
                        'pluginOptions' => [
                            'autoclose' => true,
                            'format' => 'yyyy-mm-dd',
                            'todayHighlight' => true,
                        ]
                    ]); ?>
                </div>
                <div class="col-md-4">
                    <?= $form->field($searchModel, 'created_at_to')->widget(DatePicker::classname(), [
                        'type' => DatePicker::TYPE_COMPONENT_APPEND,
                        'options' => [
                            'autocomplete' => 'off', // disables browser autofill
                            'placeholder' => 'To Date',
                        ],
                        'pluginOptions' => [
                            'autoclose' => true,
                            'format' => 'yyyy-mm-dd',
                            'todayHighlight' => true,
                        ]
                    ]); ?>
                </div>
                <div class="col-md-4 align-self-end" style="margin-top: 25px;">
                    <?= Html::submitButton('Search', ['class' => 'btn btn-primary']) ?>
                    <?= Html::a('Clear', ['lead-count-index', 'slug' => $clpSlug], ['class' => 'btn btn-primary']) ?>
                    <?php if (!empty($searchModel->created_at_from) || !empty($searchModel->created_at_to)):
                        $from = empty($searchModel->created_at_from) ? '' : (new \DateTime($searchModel->created_at_from))->format('d F Y');
                        $to = empty($searchModel->created_at_to) ? '' : (new \DateTime($searchModel->created_at_to))->format('d F Y');
                        $hypen = !empty($from) && !empty($to) ? ' - ' : '';
                        ?>
                        <?= Html::a("Export ({$from}{$hypen}{$to})", [
                            'export-range',
                            'slug' => $clpSlug,
                            'from' => $searchModel->created_at_from,
                            'to' => $searchModel->created_at_to
                        ], ['class' => 'btn btn-primary']) ?>
                    <?php endif; ?>
                </div>
            </div>

            <?php ActiveForm::end(); ?>
        </div>

        <?= GridView::widget([
            'dataProvider' => $dataProvider,
            'summary' => false,
            'columns' => [
                [
                    'label' => 'Created At',
                    'value' => function ($model) {
                        return $model['created_date'];
                    }
                ],
                [
                    'label' => 'Total Lead Count',
                    'value' => function ($model) {
                        return $model['total_leads'];
                    }
                ],
                [
                    'label' => 'Distinct Lead Count',
                    'value' => function ($model) {
                        return $model['distinct_leads'];
                    }
                ],
                [
                    'label' => 'Export',
                    'format' => 'raw',
                    'value' => function ($model) use ($clpSlug) {
                        return Html::a('Export', Url::to([
                            'export-day',
                            'slug' => $clpSlug,
                            'date' => $model['created_date']
                        ]), ['class' => 'btn btn-sm btn-primary']);
                    },
                ],
            ],
        ]); ?>
    </div>
</div>