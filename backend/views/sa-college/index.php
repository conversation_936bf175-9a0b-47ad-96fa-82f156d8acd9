<?php

use common\helpers\DataHelper;
use common\models\SaCollege;
use yii\helpers\Html;
use yii\grid\GridView;
use yii\helpers\ArrayHelper;

/* @var $this yii\web\View */
/* @var $searchModel backend\models\SaCollegeSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Sa Colleges';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="sa-college-index box box-primary">
    <div class="box-header with-border">
        <?= Html::a('Create Sa College', ['create'], ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?php // echo $this->render('_search', ['model' => $searchModel]); ?>
        <?= GridView::widget([
            'dataProvider' => $dataProvider,
            'filterModel' => $searchModel,
            'layout' => "{items}\n{summary}\n{pager}",
            'columns' => [
                ['class' => 'yii\grid\SerialColumn'],

                'name',
                'slug',
                [
                    'attribute' => 'sa_country_name',
                    'label' => 'Country',
                    'value' => function ($m) {
                        return !empty($m->saCountry->name) ? $m->saCountry->name : '';
                    }
                ],
                [
                    'attribute' => 'status',
                    'value' => function ($model) {
                        return ArrayHelper::getValue(DataHelper::getConstantList('STATUS', SaCollege::class), $model->status);
                    },
                    'filter' => DataHelper::getConstantList('STATUS', SaCollege::class)
                ],

                ['class' => 'yii\grid\ActionColumn', 'template' => '{view} {update}'],
            ],
        ]); ?>
    </div>
</div>
