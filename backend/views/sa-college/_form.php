<?php

use common\helpers\DataHelper;
use common\models\SaCollege;
use common\models\SaCountry;
use common\models\User;
use kartik\select2\Select2;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\web\JsExpression;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model common\models\SaCollege */
/* @var $form yii\widgets\ActiveForm */

$countryList = ArrayHelper::map(SaCountry::find()->where(['id' => $model->sa_country_id])->all(), 'id', 'name');

?>

<div class="sa-college-form box box-primary">
    <?php $form = ActiveForm::begin(); ?>
    <div class="box-body table-responsive">

        <div class="col-md-6">
            <?= $form->field($model, 'sa_country_id')->widget(Select2::classname(), [
                'data' => $countryList ?? [],
                'options' => [
                    'placeholder' => '--Select--',
                    'options' => [],
                ],
                'pluginOptions' => [
                    'allowClear' => true,
                    'minimumInputLength' => 2,
                    'maximumInputLength' => 10,
                    'language' => [
                        'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                    ],
                    'ajax' => [
                        'url' => ['../ajax/sa-country'],
                        'dataType' => 'json',
                        'data' => new JsExpression('function(params) {return {q:params.term}; }')
                    ],
                    'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                    'templateResult' => new JsExpression('function(data) { return data.text; }'),
                    'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                ],
            ])->label('Country');
?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'created_by')->dropDownList(
                ArrayHelper::map(User::find()->all(), 'id', 'name'),
                [
                    'options' => [
                        Yii::$app->user->identity->id => ['Selected' => true], // Set the selected value
                    ],
                    'disabled' => true // Disable the entire dropdown
                ]
            )->label('Author'); ?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'name')->textInput(['maxlength' => true]) ?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'display_name')->textInput(['maxlength' => true]) ?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'email')->textInput(['maxlength' => true]) ?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'address')->textarea(['rows' => 1]) ?>
        </div>

        <div class="col-md-6">

            <?php
            if (!empty($model->cover_image)) {
                echo Html::img(\Yii::$aliases['@gmuAzureSaCollegeImage'] . '/' . $model->cover_image, ['width' => '50', 'height' => '50']);
            }
            ?>
            <?= $form->field($model, 'cover_image')->fileInput() ?>
        </div>
        <div class="col-md-6">

            <?php
            if (!empty($model->logo_image)) {
                echo Html::img(\Yii::$aliases['@gmuAzureSaCollegeImageLogo'] . '/' . $model->logo_image, ['width' => '50', 'height' => '50']);
            }
            ?>
            <?= $form->field($model, 'logo_image')->fileInput() ?>
        </div>

        <div class="col-md-4">
            <?= $form->field($model, 'established_year')->textInput() ?>
        </div>

        <div class="col-md-4">

            <?= $form->field($model, 'url')->textInput(['maxlength' => true]) ?>
        </div>

        <div class="col-md-4">
            <?= $form->field($model, 'location')->textInput(['maxlength' => true]) ?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'phone')->textInput(['maxlength' => true]) ?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'position')->textInput() ?>
        </div>

        <div class="col-md-4">
            <?= $form->field($model, 'is_popular')->dropDownList(DataHelper::getConstantList('POPULAR', SaCollege::class)) ?>
        </div>

        <div class="col-md-4">
            <?= $form->field($model, 'is_deleted')->dropDownList(DataHelper::getConstantList('IS_DELETED', SaCollege::class)) ?>
        </div>

        <div class="col-md-4">
            <?= $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', SaCollege::class)) ?>
        </div>

    </div>
    <div class="box-footer">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <?php ActiveForm::end(); ?>
</div>