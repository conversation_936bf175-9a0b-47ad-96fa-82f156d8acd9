<?php

use common\helpers\DataHelper;
use common\models\SaCollege;
use common\models\SaCollegeDetail;
use common\models\SaCollegeFeesDetail;
use common\models\SaCollegeSubpageContent;
use common\models\SaCourseDetail;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model common\models\SaCollege */

$collegeDetail = SaCollegeDetail::find()->where(['sa_college_id' => $model->id])->one();
$courseDetail = SaCourseDetail::find()->where(['sa_college_id' => $model->id])->one();
$collegeContent = SaCollegeSubpageContent::find()->where(['sa_college_id' => $model->id])->one();
$collegeFee = SaCollegeFeesDetail::find()->where(['sa_college_id' => $model->id])->one();

$this->title = $model->name;
$this->params['breadcrumbs'][] = ['label' => 'Sa Colleges', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="sa-college-view box box-primary">
    <div class="box-header">
        <?= Html::a('Update', ['update', 'id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
        <?php if (!empty($collegeDetail)): ?>
            <?= Html::a('Update College Detail', ['sa-college-detail/update', 'id' => $collegeDetail->id], ['class' => 'btn btn-primary btn-flat']) ?>
        <?php else: ?>
            <?= Html::a('Create College Detail', ['sa-college-detail/create', 'college_id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
        <?php endif; ?>
        <?php if (!empty($collegeContent)): ?>
            <?= Html::a('Update College Content', ['sa-college-subpage-content/index?SaCollegeSubpageContentSearch[sa_college_name]=' . $collegeContent->saCollege->name], ['class' => 'btn btn-primary btn-flat']) ?>
        <?php else: ?>
            <?= Html::a('Create College Content', ['sa-college-subpage-content/create', 'college_id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
        <?php endif; ?>
        <?php if (!empty($courseDetail)): ?>
            <?= Html::a('Update Course Detail', ['sa-course-detail/index?SaCourseDetailSearch[sa_college_name]=' . $courseDetail->saCollege->name], ['class' => 'btn btn-primary btn-flat']) ?>
        <?php else: ?>
            <?= Html::a('Create Course Detail', ['sa-course-detail/create', 'college_id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
        <?php endif; ?>
        <?= Html::a('Sa College Fee', ['sa-college-fees-detail/create', 'college_id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?= DetailView::widget([
            'model' => $model,
            'attributes' => [
                'name',
                'display_name',
                'slug',
                'sa_country_id',
                'email:email',
                'address:ntext',
                'cover_image',
                'logo_image',
                'established_year',
                'url:url',
                'location',
                'phone',
                'position',
                [
                    'label' => 'Is Popular',
                    'attribute' => 'is_popular',
                    'value' => ArrayHelper::getValue(DataHelper::getConstantList('POPULAR', SaCollege::class), $model->is_popular)
                ],
                [
                    'label' => 'Is Deleted',
                    'attribute' => 'is_deleted',
                    'value' => ArrayHelper::getValue(DataHelper::getConstantList('IS_DELETED', SaCollege::class), $model->is_deleted)
                ],
                [
                    'label' => 'Status',
                    'attribute' => 'status',
                    'value' => ArrayHelper::getValue(DataHelper::getConstantList('STATUS', SaCollege::class), $model->status)
                ],
                [
                    'attribute' => 'created_by',
                    'value' => function ($model) {
                        return !empty($model->author) ? $model->author->name : '';
                    }
                ],
                [
                    'attribute' => 'updated_by',
                    'value' => function ($model) {
                        return $model->updater->name ?? '';
                    }
                ],
                'created_at:datetime',
                'updated_at:datetime',
            ],
        ]) ?>
    </div>
</div>