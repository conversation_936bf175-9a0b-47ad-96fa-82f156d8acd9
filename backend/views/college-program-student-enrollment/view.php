<?php

use common\helpers\CollegeHelper;
use common\helpers\DataHelper;
use common\models\CollegeProgramStudentEnrollment;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model common\models\CollegeProgramStudentEnrollment */

$this->title = $model->id;
$this->params['breadcrumbs'][] = ['label' => 'College Program Student Enrollments', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="college-program-student-enrollment-view box box-primary">
    <div class="box-header">
        <?= Html::a('Update', ['update', 'id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?= DetailView::widget([
            'model' => $model,
            'attributes' => [
                [
                    'label' => 'College',
                    'attribute' => 'college_id',
                    'value' => function ($model) {
                        return !empty($model->college) && !empty($model->college->name) ? $model->college->name : '';
                    }
                ],
                [
                    'label' => 'Course',
                    'attribute' => 'course_id',
                    'value' => function ($model) {
                        return !empty($model->course) && !empty($model->course->name) ? $model->course->name : '';
                    }
                ],
                [
                    'label' => 'Program',
                    'attribute' => 'program_id',
                    'value' => function ($model) {
                        return !empty($model->program) && !empty($model->program->name) ? $model->program->name : '';
                    }
                ],
                'department',
                'financing_type',
                [
                    'label' => 'Gender',
                    'attribute' => 'gender',
                    'value' => ArrayHelper::getValue(array_flip(CollegeHelper::$cutoffGender), $model->gender)
                ],
                'year',
                'count',
                [
                    'label' => 'Status',
                    'attribute' => 'status',
                    'value' => ArrayHelper::getValue(DataHelper::getConstantList('STATUS', CollegeProgramStudentEnrollment::class), $model->status)
                ],
            ],
        ]) ?>
    </div>
</div>
