<?php

use common\helpers\DataHelper;
use common\models\CollegeProgramStudentEnrollment;
use yii\helpers\Html;
use yii\grid\GridView;
use yii\helpers\ArrayHelper;

/* @var $this yii\web\View */
/* @var $searchModel backend\models\CollegeProgramStudentEnrollmentSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'College Program Student Enrollments';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="college-program-student-enrollment-index box box-primary">
    <div class="box-header with-border">
        <?= Html::a('Create College Program Student Enrollment', ['create'], ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?php // echo $this->render('_search', ['model' => $searchModel]);
        ?>
        <?= GridView::widget([
            'dataProvider' => $dataProvider,
            'filterModel' => $searchModel,
            'layout' => "{items}\n{summary}\n{pager}",
            'columns' => [
                ['class' => 'yii\grid\SerialColumn'],

                [
                    'attribute' => 'college_name',
                    'label' => 'College',
                    'value' => function ($model) {
                        return !empty($model->college->name) ? $model->college->name : '';
                    }
                ],
                [
                    'attribute' => 'course_name',
                    'label' => 'Course',
                    'value' => function ($model) {
                        return !empty($model->course->name) ? $model->course->name : '';
                    }
                ],
                [
                    'attribute' => 'program_name',
                    'label' => 'Program',
                    'value' => function ($model) {
                        return !empty($model->program->name) ? $model->program->name : '';
                    }
                ],
                'department',
                [
                    'attribute' => 'status',
                    'value' => function ($model) {
                        return ArrayHelper::getValue(DataHelper::getConstantList('STATUS', CollegeProgramStudentEnrollment::class), $model->status);
                    },
                    'filter' => DataHelper::getConstantList('STATUS', CollegeProgramStudentEnrollment::class)
                ],

                ['class' => 'yii\grid\ActionColumn', 'template' => '{view} {update}'],
            ],
        ]); ?>
    </div>
</div>