<?php

use common\helpers\CollegeHelper;
use common\helpers\DataHelper;
use common\models\College;
use common\models\CollegeProgramStudentEnrollment;
use common\models\Course;
use common\models\Program;
use kartik\select2\Select2;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\web\JsExpression;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model common\models\CollegeProgramStudentEnrollment */
/* @var $form yii\widgets\ActiveForm */

$college_data =  ArrayHelper::map(College::find()->andWhere(['id' => $model->college_id])->all(), 'id', 'name');
$course_data =  ArrayHelper::map(Course::find()->andWhere(['id' => $model->course_id])->all(), 'id', 'name');
$program_data =  ArrayHelper::map(Program::find()->andWhere(['id' => $model->program_id])->all(), 'id', 'name');
?>

<div class="college-program-student-enrollment-form box box-primary">
    <?php $form = ActiveForm::begin(); ?>
    <div class="box-body table-responsive">

        <?= $form->field($model, 'college_id')->widget(Select2::classname(), [
            'disabled' => !$model->isNewRecord,
            'data' => $college_data,
            'options' => [
                'placeholder' => '--Select College--',
                'multiple' => false,
            ],
            'pluginOptions' => [
                'allowClear' => true,
                'minimumInputLength' => 3,
                'language' => [
                    'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                ],
                'ajax' => [
                    'url' => ['../ajax/college-list'],
                    'dataType' => 'json',
                    'data' => new JsExpression('function(params) {return {q:params.term}; }')
                ],
                'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                'templateResult' => new JsExpression('function(data) { return data.text; }'),
                'templateSelection' => new JsExpression('function (data) { return data.text; }'),
            ],
        ])->label('College Name');
?>

        <?= $form->field($model, 'course_id')->widget(Select2::classname(), [
            'disabled' => !$model->isNewRecord,
            'data' => $course_data,
            'options' => [
                'placeholder' => '--Select Course--',
                'multiple' => false,
            ],
            'pluginOptions' => [
                'allowClear' => true,
                'minimumInputLength' => 3,
                'language' => [
                    'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                ],
                'ajax' => [
                    'url' => ['../ajax/course-list'],
                    'dataType' => 'json',
                    'data' => new JsExpression('function(params) {return {query:params.term}; }')
                ],
                'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                'templateResult' => new JsExpression('function(data) { return data.text; }'),
                'templateSelection' => new JsExpression('function (data) { return data.text; }'),
            ],
        ])->label('Course Name');
?>

        <?= $form->field($model, 'program_id')->widget(Select2::classname(), [
            'disabled' => !$model->isNewRecord,
            'data' => $program_data,
            'options' => [
                'placeholder' => '--Select Program--',
                'multiple' => false,
            ],
            'pluginOptions' => [
                'allowClear' => true,
                'minimumInputLength' => 3,
                'language' => [
                    'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                ],
                'ajax' => [
                    'url' => ['../ajax/program-list'],
                    'dataType' => 'json',
                    'data' => new JsExpression('function(params) {return {query:params.term}; }')
                ],
                'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                'templateResult' => new JsExpression('function(data) { return data.text; }'),
                'templateSelection' => new JsExpression('function (data) { return data.text; }'),
            ],
        ])->label('Program Name');
?>

        <?= $form->field($model, 'department')->textInput(['maxlength' => true]) ?>

        <?= $form->field($model, 'financing_type')->textInput(['maxlength' => true]) ?>

        <?= $form->field($model, 'gender')->dropDownList(array_flip(CollegeHelper::$cutoffGender))->label('Gender') ?>

        <?= $form->field($model, 'year')->textInput() ?>

        <?= $form->field($model, 'count')->textInput() ?>

        <?= $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', CollegeProgramStudentEnrollment::class)); ?>

    </div>
    <div class="box-footer">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <?php ActiveForm::end(); ?>
</div>