<?php

use common\helpers\DataHelper;
use common\models\SaCourse;
use yii\helpers\Html;
use yii\grid\GridView;
use yii\helpers\ArrayHelper;

/* @var $this yii\web\View */
/* @var $searchModel backend\models\SaCourseSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Sa Courses';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="sa-course-index box box-primary">
    <div class="box-header with-border">
        <?= Html::a('Create Sa Course', ['create'], ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?php // echo $this->render('_search', ['model' => $searchModel]); ?>
        <?= GridView::widget([
            'dataProvider' => $dataProvider,
            'filterModel' => $searchModel,
            'layout' => "{items}\n{summary}\n{pager}",
            'columns' => [
                ['class' => 'yii\grid\SerialColumn'],

                'name',
                'slug',
                [
                    'attribute' => 'status',
                    'value' => function ($model) {
                        return ArrayHelper::getValue(DataHelper::getConstantList('STATUS', SaCourse::class), $model->status);
                    },
                    'filter' => DataHelper::getConstantList('STATUS', SaCourse::class)
                ],

                ['class' => 'yii\grid\ActionColumn', 'template' => '{view} {update}'],
            ],
        ]); ?>
    </div>
</div>
