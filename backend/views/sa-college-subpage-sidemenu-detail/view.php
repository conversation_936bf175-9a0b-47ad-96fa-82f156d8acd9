<?php

use common\helpers\DataHelper;
use common\models\SaCollegeSubpageSidemenuDetail;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model common\models\SaCollegeSubpageSidemenuDetail */

$this->title = $model->saCollege->name;
$this->params['breadcrumbs'][] = ['label' => 'Sa College', 'url' => '/sa-college/index?SaCollegeSearch[slug]=' . $model->saCollege->slug];
$this->params['breadcrumbs'][] = ['label' => 'Sa College Subpage Sidemenu Details', 'url' => '/sa-college-subpage-sidemenu-detail/index?SaCollegeSubpageSidemenuDetailSearch[sa_college_name]=' . $model->saCollege->name];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="sa-college-subpage-sidemenu-detail-view box box-primary">
    <div class="box-header">
        <?= Html::a('Update', ['update', 'id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?= DetailView::widget([
            'model' => $model,
            'attributes' => [
                [
                    'attribute' => 'sa_college_subpage_id',
                    'value' => function ($model) {
                        return !empty($model->saCollegeSubpage) ? $model->saCollegeSubpage->name : '';
                    },
                    'label' => 'Subpage',
                ],
                [
                    'attribute' => 'sa_college_id',
                    'value' => function ($model) {
                        return !empty($model->saCollege) ? $model->saCollege->name : '';
                    },
                    'label' => 'College',
                ],
                [
                    'attribute' => 'sa_college_subpage_sidemenu_id',
                    'value' => function ($model) {
                        return !empty($model->saCollegeSubpageSidemenu) ? $model->saCollegeSubpageSidemenu->name : '';
                    },
                    'label' => 'Subpage Sidemenu',
                ],
                [
                    'label' => 'Content',
                    'format' => 'raw',
                    'value' => html_entity_decode($model->content),
                ],
                [
                    'label' => 'Status',
                    'attribute' => 'status',
                    'value' => ArrayHelper::getValue(DataHelper::getConstantList('STATUS', SaCollegeSubpageSidemenuDetail::class), $model->status)
                ],
                [
                    'attribute' => 'created_by',
                    'value' => function ($model) {
                        return !empty($model->author) ? $model->author->name : '';
                    }
                ],
                [
                    'attribute' => 'updated_by',
                    'value' => function ($model) {
                        return $model->updater->name ?? '';
                    }
                ],
                'created_at:datetime',
                'updated_at:datetime',
            ],
        ]) ?>
    </div>
</div>