<?php

use common\helpers\DataHelper;
use common\models\SaCollegeSubpageSidemenuDetail;
use yii\helpers\Html;
use yii\grid\GridView;
use yii\helpers\ArrayHelper;

/* @var $this yii\web\View */
/* @var $searchModel backend\models\SaCollegeSubpageSidemenuDetailSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Sa College Subpage Sidemenu Details';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="sa-college-subpage-sidemenu-detail-index box box-primary">
    <div class="box-header with-border">
        <?= Html::a('Create Sa College Subpage Sidemenu Detail', ['create'], ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?php // echo $this->render('_search', ['model' => $searchModel]);
        ?>
        <?= GridView::widget([
            'dataProvider' => $dataProvider,
            'filterModel' => $searchModel,
            'layout' => "{items}\n{summary}\n{pager}",
            'columns' => [
                ['class' => 'yii\grid\SerialColumn'],

                [
                    'attribute' => 'sa_college_name',
                    'label' => 'College Name',
                    'value' => function ($m) {
                        return !empty($m->saCollege->name) ? $m->saCollege->name : '';
                    }
                ],
                [
                    'attribute' => 'sa_subpage_name',
                    'label' => 'Subpage Name',
                    'value' => function ($m) {
                        return !empty($m->saCollegeSubpage->name) ? $m->saCollegeSubpage->name : '';
                    }
                ],
                [
                    'attribute' => 'sa_sidemenu_name',
                    'label' => 'Sidemenu Name',
                    'value' => function ($m) {
                        return !empty($m->saCollegeSubpageSidemenu->name) ? $m->saCollegeSubpageSidemenu->name : '';
                    }
                ],
                [
                    'attribute' => 'status',
                    'value' => function ($model) {
                        return ArrayHelper::getValue(DataHelper::getConstantList('STATUS', SaCollegeSubpageSidemenuDetail::class), $model->status);
                    },
                    'filter' => DataHelper::getConstantList('STATUS', SaCollegeSubpageSidemenuDetail::class)
                ],

                ['class' => 'yii\grid\ActionColumn', 'template' => '{view} {update}',],
            ],
        ]); ?>
    </div>
</div>