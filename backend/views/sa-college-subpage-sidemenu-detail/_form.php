<?php

use common\helpers\DataHelper;
use common\models\SaCollege;
use common\models\SaCollegeSubpage;
use common\models\SaCollegeSubpageSidemenu;
use common\models\SaCollegeSubpageSidemenuDetail;
use common\models\User;
use kartik\select2\Select2;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\web\JsExpression;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model common\models\SaCollegeSubpageSidemenuDetail */
/* @var $form yii\widgets\ActiveForm */

$collegeList = ArrayHelper::map(SaCollege::find()->andWhere(['id' => $model->sa_college_id])->all(), 'id', 'name');
?>

<div class="sa-college-subpage-sidemenu-detail-form box box-primary">
    <?php $form = ActiveForm::begin(); ?>
    <div class="box-body table-responsive">

        <div class="col-md-6">
            <?= $form->field($model, 'created_by')->dropDownList(
                ArrayHelper::map(User::find()->all(), 'id', 'name'),
                [
                    'options' => [
                        Yii::$app->user->identity->id => ['Selected' => true], // Set the selected value
                    ],
                    'disabled' => true // Disable the entire dropdown
                ]
            )->label('Author'); ?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'sa_college_id')->widget(Select2::classname(), [
                'disabled' => !$model->isNewRecord ? true : false,
                'data' => $collegeList, // array of text to show in the tag for the selected items
                'options' => [
                    'placeholder' => '--Select--',
                    'multiple' => false,
                ],
                'pluginOptions' => [
                    'allowClear' => true,
                    'minimumInputLength' => 3,
                    'language' => [
                        'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                    ],
                    'ajax' => [
                        'url' => ['../ajax/sa-college'],
                        'dataType' => 'json',
                        'data' => new JsExpression('function(params) {return {q:params.term}; }')
                    ],
                    'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                    'templateResult' => new JsExpression('function(data) { return data.text; }'),
                    'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                ],
            ])->label('SA College Name');
?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'sa_college_subpage_id')->dropDownList(
                ArrayHelper::map(SaCollegeSubpage::find()->all(), 'id', 'name'),
                [
                    'prompt' => '-- Select Subpage --',
                    'disabled' => !$model->isNewRecord ? true : false
                ]
            )->label('Subpage') ?>
        </div>

        <div class="col-md-6">
            <?= $form->field($model, 'sa_college_subpage_sidemenu_id')->dropDownList(
                ArrayHelper::map(SaCollegeSubpageSidemenu::find()->all(), 'id', 'name'),
                [
                    'prompt' => '-- Select Subpage Sidemenu --',
                    'disabled' => !$model->isNewRecord ? true : false
                ]
            )->label('Subpage Sidemenu') ?>
        </div>

        <div class="col-md-12">
            <?=
            $this->render('/widget/tinymce', [
                'form' => $form,
                'model' => $model,
                'type' => '',
                'entity' => 'content'
            ])
            ?>
        </div>

        <div class="col-md-12">
            <?= $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', SaCollegeSubpageSidemenuDetail::class)) ?>
        </div>

    </div>
    <div class="box-footer">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <?php ActiveForm::end(); ?>
</div>