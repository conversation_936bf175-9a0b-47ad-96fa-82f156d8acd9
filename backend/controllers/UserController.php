<?php

namespace backend\controllers;

use Yii;
use common\models\User;
use backend\models\UserForm;
use backend\models\UserSearch;
use common\helpers\BdCenterlHelper;
use common\models\Profile;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use common\services\S3Service;
use common\helpers\DataHelper;
use yii\rbac\DbManager;

/**
 * UserController implements the CRUD actions for User model.
 */
class UserController extends Controller
{
    const SCENARIO_IMPORTER = 'importer';

    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all User models.
     * @return mixed
     */
    public function actionIndex()
    {
        
        $searchModel = new UserSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single User model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new User model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new UserForm();
        $model->setScenario('create');

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['index']);
        } else {
            return $this->render('create', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Updates an existing User model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = new UserForm();
        $model->setModel($this->findModel($id));

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            if ($model->status == User::STATUS_DELETED) {
                $tableNames = ['Article', 'NcertArticles', 'BoardContent', 'BoardSamplePaper', 'CareerContent', 'CollegeContent', 'NewsContentSubdomain', 'ScholarshipContent', 'StreamContent', 'CourseContent', 'ExamContent', 'GmuMetaCategory'];
                foreach ($tableNames as $tableName) {
                    $userModel = User::find()->where(['slug' => 'getmyuni-content-team'])->one();
                    $tableModel = '\common\models\\' . $tableName;
                    $query = $tableModel::find()->select(['author_id', 'id'])->where(['author_id' => $model->model->id]);
                    foreach ($query->batch(50, Yii::$app->db) as $values) {
                        if (empty($values)) {
                            continue;
                        }
                        
                        foreach ($values as $value) {
                            $value->scenario = self::SCENARIO_IMPORTER;
                            $value->author_id = $userModel->id;
                            $value->skipAfterSave = true;

                            if ($value->save(false)) {
                            } else {
                                print_r($value->getErrors());
                            }
                        }
                    }
                }
            }

            return $this->redirect(['index']);
        } else {
            return $this->render('update', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Finds the User model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return User the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = User::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

    /**
     * Finds the User model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return User the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModelBySlug($slug)
    {
        if (($model = User::findOne(['slug' => $slug])) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }


    public function actionProfile($slug)
    {
        $model = $this->findModelBySlug($slug);
        $profile = $model->profile ?? new Profile();

        //if ($model->slug != Yii::$app->user->identity->slug) {
        //    throw new UnauthorizedHttpException();
        //}

        if ($model->load(Yii::$app->request->post()) && $profile->load(Yii::$app->request->post())) {
            $profile->user_id = $model->id;
            $model->name = $profile->name;

            $profileImage = \yii\web\UploadedFile::getInstance($profile, 'image');

            if ($profileImage) {
                $profile->image = $profileImageName = $model->username . '.' . $profileImage->extension;
                (new S3Service())->uploadFile(DataHelper::s3Path($profileImageName, 'user'), $profileImage->tempName);
            }

            if ($profile->image == '') {
                unset($profile->image);
            }

            // @todo: manage through RBAC
            if (Yii::$app->user->id == $model->id && $model->save() && $profile->save()) {
                $this->refresh();
            } elseif (Yii::$app->user->can('Webmaster') && $model->save() && $profile->save()) {
                $this->refresh();
            }
        }

        return $this->render('profile', [
            'model' => $model,
            'profile' => $profile
        ]);
    }

    public function actionImageDelete($userId)
    {
        $model = Profile::find()->where(['user_id' => $userId])->one();
        $model->image = null;
        $model->save();
        return $this->redirect(Yii::$app->request->referrer);
    }


    /**
     * Create User models.
     * @return mixed
     */
    public function actionCreateNewUser()
    {
        $model = new UserForm();
        $model->setScenario('create');

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            $user_id = Yii::$app->db->getLastInsertId();
            $role = Yii::$app->request->post('UserForm')['role'];

            $auth = new DbManager;
            $auth->init();

            foreach ($role as $r) {
                $role = $auth->getRole($r);
                $auth->assign($role, $user_id);
            }

            return $this->redirect(['user-mapping/index']);
        } else {
            return $this->render('createUser', [
                'model' => $model
            ]);
        }
    }

    /**
     * uUpdate User models.
     * @return mixed
     */
    public function actionUpdateNewUser($user_id)
    {
        $model = new UserForm();

        $model->setModel($this->findModel($user_id));
        $user_role = array_keys(Yii::$app->authManager->getRolesByUser($user_id));
        if (!empty($user_role)) {
            $model->role = $user_role;
        }

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            $role = Yii::$app->request->post('UserForm')['role'];
            $auth = new DbManager();
            $auth->init();
            if (!empty($model->role)) {
                $diff = array_intersect($model->role, BdCenterlHelper::$UserRoles);

                if (count($diff) > 0) {
                    foreach ($diff as $d) {
                        $oldRole = $auth->getRole($d);
                        $auth->revoke($oldRole, $user_id);
                    }
                }
            }

            if (!empty($role)) {
                foreach ($role as $r) {
                    $newRole = $auth->getRole($r);
                    $auth->assign($newRole, $user_id);
                }
            }
            return $this->redirect(['user-mapping/index']);
        } else {
            return $this->render('createUser', [
                'model' => $model
            ]);
        }
    }

    public function actionResetPassword($id)
    {
        if (!empty($id)) {
            $model = new UserForm();
            $model->setModel($this->findModel($id));
            if (empty($model)) {
                Yii::$app->session->setFlash('error', 'Something went wrong.');
                return $this->redirect(['index']);
            }
            if ($model->setNewPassword()) {
                Yii::$app->session->setFlash('success', 'Password Reset Successfully');
                return $this->redirect(['index']);
            } else {
                Yii::$app->session->setFlash('error', 'Something went wrong.');
                return $this->redirect(['index']);
            }
        } else {
            Yii::$app->session->setFlash('error', 'Something went wrong.');
            return $this->redirect(['index']);
        }
    }
}
