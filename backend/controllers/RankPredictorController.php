<?php

namespace backend\controllers;

use Yii;
use common\models\RankPredictor;
use backend\models\RankPredictorSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * RankPredictorController implements the CRUD actions for RankPredictor model.
 */
class RankPredictorController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all RankPredictor models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new RankPredictorSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single RankPredictor model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new RankPredictor model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new RankPredictor();
        $postData = Yii::$app->request->post();
        if (isset($_FILES['csv_upload'])) {
            $filePath = $_FILES['csv_upload']['tmp_name'];
        }

        if ($postData && file_exists($filePath)) {
            //MIME Check
            $csvMimes = ['text/x-comma-separated-values', 'text/comma-separated-values', 'application/octet-stream', 'application/vnd.ms-excel', 'application/x-csv', 'text/x-csv', 'text/csv', 'application/csv', 'application/excel', 'application/vnd.msexcel', 'text/plain'];

            $finfo = finfo_open(FILEINFO_MIME_TYPE);
            $mime = finfo_file($finfo, $filePath);
            finfo_close($finfo);
            if (in_array($mime, $csvMimes) === false) {
                Yii::$app->session->setFlash('error', 'Invalid CSV File -  Mime Type Mismatch');
                return $this->redirect(['index']);
            }

            $csvFile = fopen($filePath, 'r');
            $row = 1;
            RankPredictor::deleteAll('exam_id =' . $postData['exam_id']);
            while (($data = fgetcsv($csvFile, 1000, ',')) !== false) {
                if ($row>1) {
                    $importModel=new RankPredictor;
                    $importModel->exam_id=$postData['exam_id'];
                    $importModel->marks=$data[0];
                    if (isset($data[1]) && str_contains($data[1], '-')) {
                        $dataExpload = explode('-', $data[1]);
                        $importModel->rankFrom = $dataExpload[0];
                        $importModel->rankTo= $dataExpload[1];
                    } else {
                        if (isset($data[1])) {
                            $importModel->rankFrom=$data[1];
                        }
                        if (isset($data[2])) {
                            $importModel->rankTo=$data[2];
                        }
                    }

                    if (!$importModel->save()) {
                        Yii::$app->session->setFlash('error', sprintf('CSV Import failed at line %d', $row));
                        return $this->redirect(['index']);
                    }
                }
                $row++;
            }

            fclose($csvFile);
            Yii::$app->session->setFlash('success', 'CSV Import was successful.');
            return $this->redirect(['index']);
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }

    /**
     * Updates an existing RankPredictor model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    // public function actionUpdate($id)
    // {
    //     $model = $this->findModel($id);

    //     if ($model->load(Yii::$app->request->post()) && $model->save()) {
    //         return $this->redirect(['view', 'id' => $model->id]);
    //     } else {
    //         return $this->render('update', [
    //             'model' => $model,
    //         ]);
    //     }
    // }

    /**
     * Deletes an existing RankPredictor model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the RankPredictor model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return RankPredictor the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = RankPredictor::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }
}
