<?php

namespace backend\controllers;

use Yii;
use common\models\DynamicCta;
use backend\models\DynamicCtaSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\web\UploadedFile;
use common\services\S3Service;
use common\helpers\DataHelper;

/**
 * DynamicCtaController implements the CRUD actions for DynamicCta model.
 */
class DynamicCtaController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all DynamicCta models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new DynamicCtaSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single DynamicCta model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new DynamicCta model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new DynamicCta();
        if ($model->load(Yii::$app->request->post())) {
            if (isset($model->document) || isset($model->content_image)) {
                if (isset($model->document)) {
                    $document = UploadedFile::getInstance($model, 'document');
                    if (isset($document)) {
                        $documentName = md5(uniqid(rand(), true)) . '.' . $document->extension;
                        $saveFile =  (new S3Service())->uploadFile(DataHelper::s3Path($documentName, 'dynamic_cta_file'), $document->tempName);
                        if ($saveFile) {
                            $model->document = $documentName;
                        }
                    }
                }

                if (isset($model->content_image)) {
                    $contentImage = UploadedFile::getInstance($model, 'content_image');
                    if (isset($contentImage)) {
                        $contentImageName = md5(uniqid(rand(), true)) . '.' . $contentImage->extension;
                        $saveImage =  (new S3Service())->uploadFile(DataHelper::s3Path($contentImageName, 'dynamic_cta_image'), $contentImage->tempName);
                        if ($saveImage) {
                            $model->content_image = $contentImageName;
                        }
                    }
                }

                if ($model->save()) {
                    return $this->redirect(['view', 'id' => $model->id]);
                } else {
                    print_r($model->getErrors());
                }
            }
        } else {
            return $this->render('create', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Updates an existing DynamicCta model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post())) {
            if (empty($model->document)) {
                unset($model->document);
            }
            if (empty($model->content_image)) {
                unset($model->content_image);
            }
            if ($model->save()) {
                $document = UploadedFile::getInstance($model, 'document');
                $contentImage = UploadedFile::getInstance($model, 'content_image');

                if ($document) {
                    $newFileName = md5(uniqid(rand(), true)) . '.' . $document->extension;
                    $saveFile =  (new S3Service())->uploadFile(DataHelper::s3Path($newFileName, 'dynamic_cta_file'), $document->tempName);
                    if ($saveFile) {
                        $model->document = $newFileName;
                    }
                }

                if ($contentImage) {
                    $newImageName = md5(uniqid(rand(), true)) . '.' . $contentImage->extension;
                    $saveImage =  (new S3Service())->uploadFile(DataHelper::s3Path($newImageName, 'dynamic_cta_image'), $contentImage->tempName);
                    if ($saveImage) {
                        $model->content_image = $newImageName;
                    }
                }

                if ($model->save()) {
                    return $this->redirect(['view', 'id' => $model->id]);
                } else {
                    return $this->render('update', [
                        'model' => $model,
                    ]);
                }
            }
            return $this->redirect(['view', 'id' => $model->id]);
        } else {
            return $this->render('update', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Deletes an existing DynamicCta model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the DynamicCta model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return DynamicCta the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = DynamicCta::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }
}
