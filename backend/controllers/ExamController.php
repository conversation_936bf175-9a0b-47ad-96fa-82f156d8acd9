<?php

namespace backend\controllers;

use Yii;
use common\models\Exam;
use backend\models\ExamSearch;
use common\models\Course;
use common\models\Stream;
use common\services\ImageUploadService;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\web\Response;
use yii\helpers\ArrayHelper;
use common\services\S3Service;
use common\helpers\DataHelper;

/**
 * ExamController implements the CRUD actions for Exam model.
 */
class ExamController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all Exam models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new ExamSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single Exam model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new Exam model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new Exam();

        $courseIds = [];
        $streamIds = [];
        if (!empty($_POST)) {
            $streamIds = $_POST['Exam']['streams'];
            $courseIds = $_POST['Exam']['courses'];
            $newsIds = !empty($_POST['Exam']['news']) ? $_POST['Exam']['news'] : [];
            $articleIds = !empty($_POST['Exam']['article']) ? !empty($_POST['Exam']['article']) : [];
            $translationIds = !empty($_POST['Exam']['translation']) ? $_POST['Exam']['translation'] : [];
        }

        if ($model->load(Yii::$app->request->post()) && $model->save() && $model->saveStreams($streamIds) && $model->saveCourses($courseIds)) {
            if (isset($model->cover_image)) {
                $examImage = \yii\web\UploadedFile::getInstance($model, 'cover_image');
                $model->lang_code = empty($model->lang_code) ? DataHelper::$languageCode['en'] : $model->lang_code;

                if (isset($examImage)) {
                    $examImageName = $model->slug;
                    $response = (new S3Service())->uploadFile(DataHelper::s3Path($examImageName . '.' . $examImage->extension, 'exam_genral'), $examImage->tempName);
                    if ($response) {
                        $model->cover_image = $examImageName . '.' . $examImage->extension;
                        $model->saveNews($newsIds);
                        $model->saveArticle($articleIds);
                        $model->saveTranslation($translationIds);
                        $model->save();
                        return $this->redirect(['view', 'id' => $model->id]);
                    } else {
                        print_r($model->getErrors());
                    }
                } else {
                    $model->saveNews($newsIds);
                    $model->saveArticle($articleIds);
                    $model->saveTranslation($translationIds);
                    $model->save();
                    return $this->redirect(['view', 'id' => $model->id]);
                }
            }
        } else {
            return $this->render('create', [
                'model' => $model,
                'streams' => Stream::find()->list(),
                'courses' => '',
            ]);
        }
    }

    /**
     * Updates an existing Exam model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);
        $oldExamImage = $model->cover_image;
        $courseIds = [];
        $streamIds = [];
        $newsIds = [];
        $articleIds = [];
        if (!empty($_POST)) {
            $streamIds = !empty($_POST['Exam']['streams']) ? $_POST['Exam']['streams'] : [];
            $courseIds = !empty($_POST['Exam']['courses']) ? $_POST['Exam']['courses'] : [];
            $newsIds = !empty($_POST['Exam']['news']) ? $_POST['Exam']['news'] : [];
            $articleIds = !empty($_POST['Exam']['article']) ? $_POST['Exam']['article'] : [];
            $translationIds = !empty($_POST['Exam']['translation']) ? $_POST['Exam']['translation'] : [];
        }

        if ($model->load(Yii::$app->request->post()) && $model->save() && $model->saveStreams($streamIds) && $model->saveCourses($courseIds)) {
            if (isset($model->cover_image)) {
                $examImage = \yii\web\UploadedFile::getInstance($model, 'cover_image');
                if (isset($examImage)) {
                    $examImageName = $model->slug;
                    $response = (new S3Service())->uploadFile(DataHelper::s3Path($examImageName . '.' . $examImage->extension, 'exam_genral'), $examImage->tempName);
                    if ($response) {
                        $model->cover_image = $examImageName . '.' . $examImage->extension;
                        $model->saveNews($newsIds);
                        $model->saveArticle($articleIds);
                        $model->saveTranslation($translationIds);
                        $model->save();

                        return $this->redirect(['view', 'id' => $model->id]);
                    } else {
                        print_r($model->getErrors());
                    }
                } else {
                    $model->cover_image = $oldExamImage;
                    $model->saveNews($newsIds);
                    $model->saveArticle($articleIds);
                    $model->saveTranslation($translationIds);
                    $model->save();

                    return $this->redirect(['view', 'id' => $model->id]);
                }
            }
        } else {
            $stream_ids = $model->getStreams()->asArray()->all();
            $courses = Course::find()->where(['stream_id' => array_column($stream_ids, 'id')])->all();
            return $this->render('update', [
                'model' => $model,
                'streams' => Stream::find()->list(),
                'courses' => ArrayHelper::map($courses, 'id', 'name'),
            ]);
        }
    }

    // public function actionDelete($id)
    // {
    //     $this->findModel($id)->delete();
    //     return $this->redirect(['index']);
    // }

    /**
     * Undocumented function
     *
     * @param [type] $query
     * @return void
     */
    public function actionCoursesList()
    {
        $options = '';
        Yii::$app->response->format = Response::FORMAT_JSON;
        if ($stream_ids = Yii::$app->request->post('ids')) {
            $courses = Course::find()->where(['stream_id' => $stream_ids])->all();
            $course_ids = Yii::$app->request->post('courses');
            if (count($courses) > 0) {
                foreach ($courses as $course) {
                    if (!empty($course_ids)) {
                        if (in_array($course->id, $course_ids)) {
                            $options .= "<option value='" . $course->id . "' selected>" . $course->name . '</option>';
                        } else {
                            $options .= "<option value='" . $course->id . "'>" . $course->name . '</option>';
                        }
                    } else {
                        $options .= "<option value='" . $course->id . "'>" . $course->name . '</option>';
                    }
                }
            } else {
                $options .= "'<option>-</option>'";
            }
        }
        return $options;
    }

    /**
     * Finds the Exam model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return Exam the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = Exam::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

    public function actionUploadEditorImage()
    {
        $response = Yii::$app->response;
        $response->format = \yii\web\Response::FORMAT_JSON;
        $image = (object) $_FILES['file'];
        $image->tempName = $image->tmp_name;

        $upload = (new ImageUploadService())->upload($image, 'examEditor');

        if ($upload) {
            $response->data = [
                'file' => [
                    'url' => \Yii::$app->params['siteUrl'] . \Yii::getAlias('@examCoverImage') . '/' . $upload->response->filename,
                    'id' => 'lfaskdfjlasdf934893r'
                ]
            ];
        } else {
            $response->data = [
                'error' => true,
                'message' => 'something went wrong'
            ];
        }
    }
}
