<?php

namespace backend\controllers;

use Yii;
use common\models\CollegeListingNotification;
use common\models\CollegeListingNotificationSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use common\models\ExamNotification;
use common\models\ProgramCourseMapping;
use common\models\ExamCourse;
use common\models\CollegeNotificationUpdate;
use common\models\College;
use common\models\CollegeExam;
use common\models\CollegeProgram;
use common\models\CollegeProgramExam;
use common\models\Stream;
use common\models\ExamStream;
use backend\models\ExamNotificationSearch;

/**
 * CollegeListingNotificationController implements the CRUD actions for CollegeListingNotification model.
 */
class CollegeListingNotificationController extends Controller
{
    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all CollegeListingNotification models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new CollegeListingNotificationSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single CollegeListingNotification model.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new CollegeListingNotification model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
       
        $model = new CollegeListingNotification();
        $postData = Yii::$app->request->post();
        if ($model->load($postData)) {
            $examIds = array_filter((array) $model->exam_id);
            $courseIds = array_filter((array) $model->course_id);
            if (empty($postData['CollegeListingNotification']['state_id'])) {
               // $model->addError('state_id', 'Please select location.');
            }
            // Validate that either exam or course is selected
            if (empty($courseIds) && empty($examIds) && empty($model->stream_id)) {
                Yii::$app->session->setFlash('error', 'Select at least one stream,course or exam');
               // $model->addError('exam_id', 'Either Course or Exam must be selected.');
            } else {
                $result = $this->processExamNotificationCreation($model, $postData);
                
                if ($result['success']) {
                    Yii::$app->session->set('savedRecords', $result['savedRecords']);
                    return $this->redirect(['view', 'id' => $result['modelId']]);
                }
            }
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }

    /**
     * Updates an existing CollegeListingNotification model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);
        $model->updated_at =  new \yii\db\Expression('NOW()');
        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->id]);
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * Deletes an existing CollegeListingNotification model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the CollegeListingNotification model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return CollegeListingNotification the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = CollegeListingNotification::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }

      /**
     * Process exam notification creation with optimized logic
     * @param ExamNotification $model
     * @param array $postData
     * @return array
     */
    private function processExamNotificationCreation($model, $postData)
    {
        $courseIds = array_filter((array) $model->course_id);
        $programIds = array_filter((array) $model->program_id);
        $examIds = array_filter((array) $model->exam_id);
        $streamIds = array_filter((array) $model->stream_id);
        $stateIds = json_encode(array_filter((array)$postData['CollegeListingNotification']['state_id']));
        // Determine what combinations to create
        $combinations = $this->getExamCourseProgramStreamCombinations($examIds, $courseIds, $programIds, $streamIds);
        
        // Create records for all combinations
        $result = $this->createNotificationRecords($combinations, $postData, $stateIds);

        return $result;
    }

    /**
     * Get exam-course-program-stream combinations based on selection
     * @param array $examIds
     * @param array $courseIds
     * @param array $programIds
     * @param array $streamIds
     * @return array
     */
    private function getExamCourseProgramStreamCombinations($examIds, $courseIds, $programIds, $streamIds)
    {
        $combinations = [];

        // If only exams are selected (no courses), create records with null course and program
        if (!empty($examIds) && empty($courseIds)) {
            foreach ($examIds as $examId) {
                // If streams are selected, validate exam-stream combinations
                if (!empty($streamIds)) {
                    $validExamStreamCombinations = $this->getValidExamStreamCombinations([$examId], $streamIds);
                    foreach ($validExamStreamCombinations as $examStream) {
                        $combinations[] = [
                            'exam_id' => $examStream['exam_id'],
                            'course_id' => null,
                            'program_id' => null,
                            'stream_id' => $examStream['stream_id']
                        ];
                    }

                    // If no valid stream combinations found, create record with null stream
                    if (empty($validExamStreamCombinations)) {
                        $combinations[] = [
                            'exam_id' => $examId,
                            'course_id' => null,
                            'program_id' => null,
                            'stream_id' => null
                        ];
                    }
                } else {
                    $combinations[] = [
                        'exam_id' => $examId,
                        'course_id' => null,
                        'program_id' => null,
                        'stream_id' => null
                    ];
                }
            }
            return $combinations;
        }

        // If both exams and courses are selected, check exam-course mapping
        if (!empty($examIds) && !empty($courseIds)) {
            $validExamCourseCombinations = $this->getValidExamCourseCombinations($examIds, $courseIds);

            foreach ($validExamCourseCombinations as $examCourse) {
                $examId = $examCourse['exam_id'];
                $courseId = $examCourse['course_id'];

                // Get course-program combinations for this course
                $courseProgramCombinations = $this->getCourseProgramCombinations([$courseId], $programIds);

                foreach ($courseProgramCombinations as $courseProgram) {
                    // If streams are selected, validate combinations
                    if (!empty($streamIds)) {
                        // Validate exam-stream and course-stream combinations
                        $validExamStreamCombinations = $this->getValidExamStreamCombinations([$examId], $streamIds);
                        $validCourseStreamCombinations = $this->getValidCourseStreamCombinations([$courseProgram['course_id']], $streamIds);

                        // Get streams that are valid for both exam and course
                        $validExamStreamIds = array_column($validExamStreamCombinations, 'stream_id');
                        $validCourseStreamIds = array_column($validCourseStreamCombinations, 'stream_id');
                        $commonStreamIds = array_intersect($validExamStreamIds, $validCourseStreamIds);

                        foreach ($commonStreamIds as $streamId) {
                            $combinations[] = [
                                'exam_id' => $examId,
                                'course_id' => $courseProgram['course_id'],
                                'program_id' => $courseProgram['program_id'],
                                'stream_id' => $streamId
                            ];
                        }

                        // If no common streams found, create record with null stream
                        if (empty($commonStreamIds)) {
                            $combinations[] = [
                                'exam_id' => $examId,
                                'course_id' => $courseProgram['course_id'],
                                'program_id' => $courseProgram['program_id'],
                                'stream_id' => null
                            ];
                        }
                    } else {
                        $combinations[] = [
                            'exam_id' => $examId,
                            'course_id' => $courseProgram['course_id'],
                            'program_id' => $courseProgram['program_id'],
                            'stream_id' => null
                        ];
                    }
                }
            }

            // Handle exams that don't have valid course mappings
            $mappedExamIds = array_unique(array_column($validExamCourseCombinations, 'exam_id'));
            $unmappedExamIds = array_diff($examIds, $mappedExamIds);

            foreach ($unmappedExamIds as $examId) {
                // If streams are selected, validate exam-stream combinations
                if (!empty($streamIds)) {
                    $validExamStreamCombinations = $this->getValidExamStreamCombinations([$examId], $streamIds);
                    foreach ($validExamStreamCombinations as $examStream) {
                        $combinations[] = [
                            'exam_id' => $examStream['exam_id'],
                            'course_id' => null,
                            'program_id' => null,
                            'stream_id' => $examStream['stream_id']
                        ];
                    }

                    // If no valid stream combinations found, create record with null stream
                    if (empty($validExamStreamCombinations)) {
                        $combinations[] = [
                            'exam_id' => $examId,
                            'course_id' => null,
                            'program_id' => null,
                            'stream_id' => null
                        ];
                    }
                } else {
                    $combinations[] = [
                        'exam_id' => $examId,
                        'course_id' => null,
                        'program_id' => null,
                        'stream_id' => null
                    ];
                }
            }

            return $combinations;
        }

        // If only courses are selected (no exams), use existing logic
        if (empty($examIds) && !empty($courseIds)) {
            $courseProgramCombinations = $this->getCourseProgramCombinations($courseIds, $programIds);

            foreach ($courseProgramCombinations as $courseProgram) {
                // If streams are selected, validate course-stream combinations
                if (!empty($streamIds)) {
                    $validCourseStreamCombinations = $this->getValidCourseStreamCombinations([$courseProgram['course_id']], $streamIds);
                    foreach ($validCourseStreamCombinations as $courseStream) {
                        $combinations[] = [
                            'exam_id' => null,
                            'course_id' => $courseStream['course_id'],
                            'program_id' => $courseProgram['program_id'],
                            'stream_id' => $courseStream['stream_id']
                        ];
                    }

                    // If no valid stream combinations found, create record with null stream
                    if (empty($validCourseStreamCombinations)) {
                        $combinations[] = [
                            'exam_id' => null,
                            'course_id' => $courseProgram['course_id'],
                            'program_id' => $courseProgram['program_id'],
                            'stream_id' => null
                        ];
                    }
                } else {
                    $combinations[] = [
                        'exam_id' => null,
                        'course_id' => $courseProgram['course_id'],
                        'program_id' => $courseProgram['program_id'],
                        'stream_id' => null
                    ];
                }
            }
            return $combinations;
        }

        // Fallback: return single record with all null values, but include streams if selected
        if (!empty($streamIds)) {
            $combinations = [];
            // For fallback case, just include all selected streams without validation
            foreach ($streamIds as $streamId) {
                $combinations[] = [
                    'exam_id' => null,
                    'course_id' => null,
                    'program_id' => null,
                    'stream_id' => $streamId
                ];
            }
            return $combinations;
        }

        return [['exam_id' => null, 'course_id' => null, 'program_id' => null, 'stream_id' => null]];
    }

    /**
     * Get course-program combinations based on selection
     * @param array $courseIds
     * @param array $programIds
     * @return array
     */
    private function getCourseProgramCombinations($courseIds, $programIds)
    {
        // If no courses selected, return single record with null course_id and program_id
        if (empty($courseIds)) {
            return [['course_id' => null, 'program_id' => null]];
        }

        // If no programs selected, return courses with null program_id
        if (empty($programIds)) {
            return array_map(function ($courseId) {
                return ['course_id' => $courseId, 'program_id' => null];
            }, $courseIds);
        }

        // Get valid combinations from mapping table
        $validCombinations = $this->getValidCourseProgramCombinations($courseIds, $programIds);

        // Get courses that have valid programs
        $coursesWithPrograms = array_unique(array_column($validCombinations, 'course_id'));

        // Get courses that don't have valid programs
        $coursesWithoutPrograms = array_diff($courseIds, $coursesWithPrograms);

        // Add courses without programs (with null program_id)
        foreach ($coursesWithoutPrograms as $courseId) {
            $validCombinations[] = ['course_id' => $courseId, 'program_id' => null];
        }

        // If no valid combinations at all, fallback to all courses with null program_id
        if (empty($validCombinations)) {
            return array_map(function ($courseId) {
                return ['course_id' => $courseId, 'program_id' => null];
            }, $courseIds);
        }

        return $validCombinations;
    }

    /**
     * Get valid exam-course combinations from database
     * @param array $examIds
     * @param array $courseIds
     * @return array
     */
    private function getValidExamCourseCombinations($examIds, $courseIds)
    {
        return ExamCourse::find()
            ->select(['exam_id', 'course_id'])
            ->where(['exam_id' => $examIds, 'course_id' => $courseIds])
            ->asArray()
            ->all();
    }

    /**
     * Get valid course-program combinations from database
     * @param array $courseIds
     * @param array $programIds
     * @return array
     */
    private function getValidCourseProgramCombinations($courseIds, $programIds)
    {
        return ProgramCourseMapping::find()
            ->select(['course_id', 'program_id'])
            ->where(['course_id' => $courseIds, 'program_id' => $programIds])
            ->asArray()
            ->all();
    }

    /**
     * Get valid exam-stream combinations from database
     * @param array $examIds
     * @param array $streamIds
     * @return array
     */
    private function getValidExamStreamCombinations($examIds, $streamIds)
    {
        return ExamStream::find()
            ->select(['exam_id', 'stream_id'])
            ->where(['exam_id' => $examIds, 'stream_id' => $streamIds])
            ->asArray()
            ->all();
    }

    /**
     * Get valid course-stream combinations from database
     * @param array $courseIds
     * @param array $streamIds
     * @return array
     */
    private function getValidCourseStreamCombinations($courseIds, $streamIds)
    {
        // Course table has direct stream_id relationship
        return \common\models\Course::find()
            ->select(['id as course_id', 'stream_id'])
            ->where(['id' => $courseIds, 'stream_id' => $streamIds])
            ->asArray()
            ->all();
    }

    /**
     * Create notification records for given combinations
     * @param array $combinations
     * @param array $postData
     * @return array
     */
    private function createNotificationRecords($combinations, $postData, $stateIds)
    {
        $savedRecords = [];
        $hasErrors = false;

        foreach ($combinations as $combination) {
            $newModel = new CollegeListingNotification();
            $newModel->load($postData);
            $newModel->exam_id = $combination['exam_id'];
            $newModel->course_id = $combination['course_id'];
            $newModel->program_id = $combination['program_id'];
            $newModel->stream_id = $combination['stream_id'];
            $newModel->state_id = $stateIds;

            if ($newModel->status == 1 && $newModel->start_date == new \yii\db\Expression('NOW()')) {
                $newModel->publish_at = new \yii\db\Expression('NOW()');
            } else {
                $newModel->publish_at = $newModel->start_date;
            }
            $newModel->created_at =  new \yii\db\Expression('NOW()');
            $newModel->updated_at =  new \yii\db\Expression('NOW()');
            if ($newModel->validate() && $newModel->save()) {
                $savedRecords[] = $newModel->id;
            } else {
                $hasErrors = true;
                print_r($newModel->getErrors());
            }
        }

        return [
            'success' => !empty($savedRecords),
            'savedCount' => count($savedRecords),
            'hasErrors' => $hasErrors,
            'modelId' => $savedRecords[0] ?? '',
            'savedRecords' => $savedRecords
        ];
    }
}
