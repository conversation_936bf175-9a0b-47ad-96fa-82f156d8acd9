<?php

namespace backend\controllers;

use Yii;
use common\models\College;
use backend\models\CollegeSearch;
use common\models\Article;
use common\models\News;
use common\services\AzureService;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\web\UploadedFile;
use common\services\S3Service;
use common\helpers\DataHelper;
use common\models\CollegeCourseMapping;
use common\models\CutOff;
use common\models\CutoffDetailH1Description;
use yii\base\Model;

/**
 * CollegeController implements the CRUD actions for College model.
 */
class CollegeController extends Controller
{
    protected $azureService;

    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }



    public function __construct($id, $module, AzureService $azureService, $config = [])
    {
        $this->azureService = $azureService;
        parent::__construct($id, $module, $config);
    }

    /**
     * Lists all College models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new CollegeSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single College model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id)
    {
        $model = $this->findModel($id);

        return $this->render('view', [
            'model' => $model,
        ]);
    }

    /**
     * Creates a new College model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new College();
        $newsIds = [];
        $articleIds = [];

        $postRequest = Yii::$app->request->post();

        if (!empty($postRequest['College']['news'])) {
            $newsIds = $postRequest['College']['news'];
        }
        if (!empty($postRequest['College']['article'])) {
            $articleIds = $postRequest['College']['article'];
        }

        if ($model->load($postRequest) && $model->save()) {
            if (isset($model->cover_image) || isset($model->logo_image)) {
                if (isset($model->cover_image)) {
                    $coverImage = UploadedFile::getInstance($model, 'cover_image');
                    if (isset($coverImage)) {
                        $newImageName = $model->slug;
                        // $saveImage = $this->azureService->upload($coverImage, 'college-image/big/', $newImageName);
                        $saveImage =  (new S3Service())->uploadFile(DataHelper::s3Path($newImageName . '.' . $coverImage->extension, 'college_genral'), $coverImage->tempName);
                        if ($saveImage) {
                            $model->cover_image = $newImageName . '.' . $coverImage->extension;
                        }
                    }
                }

                if (isset($model->logo_image)) {
                    $logoImage = UploadedFile::getInstance($model, 'logo_image');
                    if (isset($logoImage)) {
                        $newImageName = $model->slug;
                        //$saveImage = $this->azureService->upload($logoImage, 'college-image/small/', $newImageName);
                        $saveImage =  (new S3Service())->uploadFile(DataHelper::s3Path($newImageName . '.' . $logoImage->extension, 'college_logo'), $logoImage->tempName);
                        if ($saveImage) {
                            $model->logo_image = $newImageName . '.' . $logoImage->extension;
                        }
                    }
                }

                if ($model->save()) {
                    $model->saveArticle($articleIds);
                    $model->saveNews($newsIds);

                    return $this->redirect(['view', 'id' => $model->id]);
                }
            }
            return $this->redirect(['view', 'id' => $model->id]);
        } else {
            return $this->render('create', [
                'model' => $model,
            ]);
        }
    }


    /**
     * Updates an existing College model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $newsIds = [];
        $articleIds = [];
        $model = $this->findModel($id);
        $postRequest = Yii::$app->request->post();
        if (!empty($postRequest['College']['news'])) {
            $newsIds = $postRequest['College']['news'];
        }
        if (!empty($postRequest['College']['article'])) {
            $articleIds = $postRequest['College']['article'];
        }
        if ($model->load($postRequest)) {
            if (empty($model->cover_image)) {
                unset($model->cover_image);
            }
            if (empty($model->logo_image)) {
                unset($model->logo_image);
            }
            if ($model->save()) {
                $model->saveArticle($articleIds);
                $model->saveNews($newsIds);

                $coverImage = UploadedFile::getInstance($model, 'cover_image');
                $logoImage = UploadedFile::getInstance($model, 'logo_image');

                if ($coverImage) {
                    $newImageName = $model->slug . '.' . $coverImage->extension;
                    // $saveImage = $this->azureService->upload($coverImage, 'college-image/big', $newImageName);
                    $saveImage =  (new S3Service())->uploadFile(DataHelper::s3Path($newImageName, 'college_genral'), $coverImage->tempName);
                    if ($saveImage) {
                        $model->cover_image = $newImageName;
                    }
                }

                if ($logoImage) {
                    $newImageName = $model->slug . '.' . $logoImage->extension;
                    //$saveImage = $this->azureService->upload($logoImage, 'college-image/small', $newImageName);
                    $saveImage =  (new S3Service())->uploadFile(DataHelper::s3Path($newImageName, 'college_logo'), $logoImage->tempName);
                    if ($saveImage) {
                        $model->logo_image = $newImageName;
                    }
                }
                $model->url = $postRequest['College']['url'];
                if ($model->save()) {
                    return $this->redirect(['view', 'id' => $model->id]);
                } else {
                    return $this->render('update', [
                        'model' => $model,
                    ]);
                }
            }
        } else {
            return $this->render('update', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Adding Multiple exams for existing college in junction table college_exam
     * @param integer $id is College Id
     * @return mixed
     */
    public function actionExam($id)
    {
        $model = $this->findModel($id);

        if (Yii::$app->request->post()) {
            $examIds = Yii::$app->request->post('College')['exam_id'];

            if (empty($examIds)) {
                $examIds = [];
            }
            if ($model->saveExams($examIds)) {
                return $this->redirect(['view', 'id' => $model->id]);
            }
        }

        return $this->render('_exam', [
            'model' => $model,
            'exams' => $model->getExams()->asArray()->all() ?? []
        ]);
    }

    /**
     * Deletes an existing College model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    // public function actionDelete($id)
    // {
    //     $this->findModel($id)->delete();

    //     return $this->redirect(['index']);
    // }

    /**
     * Finds the College model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return College the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = College::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

    public function actionTabularForm($college_id = '')
    {
        $models = $this->findCutoffDetailH1DescriptionModel($college_id);
        $college = College::findOne(['id' => $college_id]);
        $collegeName = $college->name ?? '';

        if (empty($models)) {
            $model = new CutoffDetailH1Description();
            $model->college_id = $college_id;
            $models[] = $model;
        }

        $request = Yii::$app->request;

        if ($request->isPost) {
            $postData = $request->post('CutoffDetailH1Description', []);
            $models = [];

            foreach ($postData as $data) {
                $model = !empty($data['id'])
                    ? CutoffDetailH1Description::findOne($data['id'])
                    : new CutoffDetailH1Description();
                $model->college_id = $college_id;
                $models[] = $model;
            }

            if (Model::loadMultiple($models, $request->post()) && Model::validateMultiple($models)) {
                foreach ($models as $model) {
                    if (!$model->save()) {
                        Yii::$app->session->setFlash('error', 'Error saving: ' . json_encode($model->getErrors()));
                        return $this->render('view', [
                            'cutOffModels' => $models,
                            'college_id' => $college_id,
                            'collegeName' => $collegeName
                        ]);
                    }
                }

                Yii::$app->session->setFlash('success', 'Data added successfully');
                return $this->render('view', [
                    'cutOffModels' => $models,
                    'college_id' => $college_id,
                    'collegeName' => $collegeName
                ]);
            }

            Yii::$app->session->setFlash('error', 'Validation failed.');
        }

        return $this->render('view', [
            'cutOffModels' => $models,
            'college_id' => $college_id,
            'collegeName' => $collegeName
        ]);
    }

    public function findCutoffDetailH1DescriptionModel($college_id)
    {
        return CutoffDetailH1Description::find()->where(['college_id' => $college_id])->all();
    }
}
