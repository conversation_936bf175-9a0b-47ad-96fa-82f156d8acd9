<?php

namespace backend\controllers;

use Yii;
use common\models\LeadBucket;
use common\models\LeadBucketCta;
use common\models\LeadBucketCtaDetail;
use backend\models\LeadBucketSearch;
use common\components\EmailTarget;
use yii\web\Controller;
use yii\base\Model;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use common\helpers\DataHelper;
use common\models\CtaThankYou;

/**
 * LeadBucketController implements the CRUD actions for LeadBucket model.
 */
class LeadBucketController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    public function actionHome()
    {
        $searchModel = new LeadBucketSearch();

        $dataProvider = $searchModel->home(Yii::$app->request->queryParams);

        return $this->render('home', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }


    /**
     * Lists all LeadBucket models.
     * @return mixed
     */
    public function actionIndex()
    {
        if (empty($_GET) || empty($_GET['entity_id'])) {
            throw new NotFoundHttpException();
        }

        $searchModel = new LeadBucketSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single LeadBucket model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id)
    {
        $models = LeadBucketCta::find()->where(['bucket_id' => $id])->all() ?? [];
        return $this->render('view', [
            'model' => $this->findModel($id),
            'models' => $models
        ]);
    }

    /**
     * Creates a new LeadBucket model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $paramEntityId = '';
        if (!empty($_GET) && !empty($_GET['entity_id'])) {
            $paramEntityId = $_GET['entity_id'];
        }
        $request = Yii::$app->getRequest();
        $model = new LeadBucket();
        $model->entity_id = $paramEntityId;
        $models = [];
        $data = [];
        $leadBucketArray = [];
        if ($request->isPost && isset($request->post()['LeadBucket'])) {
            $leadBucketArray = Yii::$app->request->post()['LeadBucket'];
            $data['LeadBucket'][0] = $leadBucketArray;
            if (empty($leadBucketArray['id'])) {
                $model = new LeadBucket();
                $model->template_id = 2;
            }
            $models[0] = $model;
            $data['LeadBucket'][0]['entity_id'] = Yii::$app->request->post()['LeadBucket']['entity_id'] ?? ($paramEntityId ?? '');
            if (Model::loadMultiple($models, $data) && Model::validateMultiple($models)) {
                foreach ($models as $model) {
                    if (!$model->save()) {
                        Yii::error($model->getError());
                    } else {
                        $modelCta = new LeadBucketCta();
                        $modelCtaVal = new LeadBucketCta();
                        $modelCtas = [];
                        $dataCta = [];
                        $modelBucketId = $model->id;
                        if (isset($request->post()['LeadBucketCta'])) {
                            $leadBucketCtaArray = Yii::$app->request->post()['LeadBucketCta'];
                            $dataCta['LeadBucketCta'] = $leadBucketCtaArray;
                            foreach ($leadBucketCtaArray as $key => $value) {
                                if (!empty($value['id'])) {
                                    continue;
                                } else {
                                    $modelCta = new LeadBucketCta();
                                }
                                $modelCtas[$key] = $modelCta;
                                $dataCta['LeadBucketCta'][$key]['bucket_id'] = $modelBucketId;
                                $dataCta['LeadBucketCta'][$key]['cta_position'] = DataHelper::$cta_positions[$value['cta_position']];
                                $dataCta['LeadBucketCta'][$key]['web'] = null;
                                $dataCta['LeadBucketCta'][$key]['wap'] = null;
                                $dataCta['LeadBucketCta'][$key]['status'] = 1;
                                if ($paramEntityId == LeadBucket::LEAD_ENTITY_NEWS_AND_ARTICLE) {
                                    $ctaData = LeadBucketCtaDetail::find()->where(['id' => $value['cta_id']])->one();
                                    $bucket_slug = $this->createSlug($model->bucket);
                                    if (!empty($ctaData)) {
                                        $dataCta['LeadBucketCta'][$key]['cta_id'] = $value['cta_id'];
                                        $dataCta['LeadBucketCta'][$key]['cta_text'] = $ctaData->cta_text;
                                        $dataCta['LeadBucketCta'][$key]['cta_title'] = $ctaData->cta_title;
                                        $dataCta['LeadBucketCta'][$key]['lead_form_title'] = $ctaData->lead_form_title;
                                        $dataCta['LeadBucketCta'][$key]['lead_form_description'] = $ctaData->lead_form_description;
                                    }
                                    $value['cta_slug'] = 'news_article_{slug}_' . $bucket_slug . '_' . $dataCta['LeadBucketCta'][$key]['cta_position'];
                                    $dataCta['LeadBucketCta'][$key]['cta_slug'] = $value['cta_slug'];
                                }
                                if ($value['platform'] == LeadBucketCta::PLATFORM_WEB && !empty($value['cta_slug'])) {
                                    $dataCta['LeadBucketCta'][$key]['web'] = $value['cta_slug'] . '_web';
                                } else if ($value['platform'] == LeadBucketCta::PLATFORM_WAP && !empty($value['cta_slug'])) {
                                    $dataCta['LeadBucketCta'][$key]['wap'] = $value['cta_slug'] . '_wap';
                                } else if ($value['platform'] == LeadBucketCta::PLATFORM_BOTH && !empty($value['cta_slug'])) {
                                    $dataCta['LeadBucketCta'][$key]['web'] = $value['cta_slug'] . '_web';
                                    $dataCta['LeadBucketCta'][$key]['wap'] = $value['cta_slug'] . '_wap';
                                }
                            }
                            if (Model::loadMultiple($modelCtas, $dataCta) && Model::validateMultiple($modelCtas)) {
                                foreach ($modelCtas as $modelCtaVal) {
                                    if (!$modelCtaVal->save()) {
                                        Yii::error($modelCtaVal->getError());
                                    }
                                }
                                Yii::$app->session->setFlash('success', 'Success');
                                
                                return $this->redirect(['update', 'id' => $model->id, 'entity_id' => $paramEntityId]);
                            } else {
                                return $this->render('create', [
                                    'model' => $modelCtaVal,
                                    'models' => $modelCtas
                                ]);
                            }
                        } else {
                            return $this->render('create', [
                                'model' => $model,
                                'models' => $models
                            ]);
                        }
                    }
                }
                Yii::$app->session->setFlash('success', 'Success');
               
                return $this->redirect(['update', 'id' => $model->id, 'entity_id' => $paramEntityId]);
            } else {
                return $this->render('create', [
                    'model' => $model,
                    'models' => $models
                ]);
            }
        } else {
            return $this->render('create', [
                'model' => $model,
                'models' => $models
            ]);
        }
    }

    /**
     * Creates a new LeadBucket model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionBucket()
    {
        $model = new LeadBucket();

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->id]);
        } else {
            return $this->render('create', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Updates an existing LeadBucket model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);
        $models = LeadBucketCta::find()->where(['bucket_id' => $id])->andWhere(['not', ['cta_position' => 'popup_cta']])->all();
        // dd(Yii::$app->request->post(), $_GET['entity_id']);
        if (isset(Yii::$app->request->post()['CtaThankYou'])) {
            self::addThankYouMessage(Yii::$app->request->post()['CtaThankYou']);
        } else {
            $leadBucketCta = isset(Yii::$app->request->post()['LeadBucketCta']) ? Yii::$app->request->post()['LeadBucketCta'] : [];
            if (empty($leadBucketCta)) {
                $data = $models;
            } else {
                $data = $leadBucketCta;
            }
            self::addCtaThankYouText($data, $_GET['entity_id']);
        }

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            $modelCtas = [];
            $dataCta = [];
            $modelCtaVal = new LeadBucketCta();
            $dataCtaModel = new LeadBucketCta();
            if (isset(Yii::$app->request->post()['LeadBucketCta'])) {
                $leadBucketCtaArray = Yii::$app->request->post()['LeadBucketCta'];
                $dataCta['LeadBucketCta'] = $leadBucketCtaArray;
                foreach ($leadBucketCtaArray as $key => $value) {
                    if (!empty($value['id'])) {
                        $modelCta = LeadBucketCta::find()->where(['id' => $value['id']])->one();
                        $modelCta->load($value, '');
                    } else {
                        $modelCta = new LeadBucketCta();
                    }
                    $modelCtas[$key] = $modelCta;
                    $dataCta['LeadBucketCta'][$key]['bucket_id'] = $id;
                    $dataCta['LeadBucketCta'][$key]['cta_position'] = (is_int($value['cta_position']) || ctype_digit($value['cta_position'])) ? DataHelper::$cta_positions[$value['cta_position']] : $value['cta_position'];
                    $dataCta['LeadBucketCta'][$key]['status'] = 1;
                    if ($model->entity_id == LeadBucket::LEAD_ENTITY_NEWS_AND_ARTICLE) {
                        $ctaData = LeadBucketCtaDetail::find()->where(['id' => $value['cta_id']])->one();
                        $bucket_slug = $this->createSlug($model->bucket);
                        if (!empty($ctaData)) {
                            $dataCta['LeadBucketCta'][$key]['cta_id'] = $value['cta_id'];
                            $dataCta['LeadBucketCta'][$key]['cta_text'] = $ctaData->cta_text;
                            $dataCta['LeadBucketCta'][$key]['cta_title'] = $ctaData->cta_title;
                            $dataCta['LeadBucketCta'][$key]['lead_form_title'] = $ctaData->lead_form_title;
                            $dataCta['LeadBucketCta'][$key]['lead_form_description'] = $ctaData->lead_form_description;
                        }
                        $value['cta_slug'] = 'news_article_{slug}_' . $bucket_slug . '_' . $dataCta['LeadBucketCta'][$key]['cta_position'];
                        $dataCta['LeadBucketCta'][$key]['cta_slug'] = $value['cta_slug'];
                    }
                    // since nomenclature is disabled for any updates
                    // $dataCta['LeadBucketCta'][$key]['web'] = null;
                    // $dataCta['LeadBucketCta'][$key]['wap'] = null;
                    // if ($value['platform'] == LeadBucketCta::PLATFORM_WEB) {
                    //     $dataCta['LeadBucketCta'][$key]['web'] = $value['cta_slug'] . '_web';
                    // } else if ($value['platform'] == LeadBucketCta::PLATFORM_WAP) {
                    //     $dataCta['LeadBucketCta'][$key]['wap'] = $value['cta_slug'] . '_wap';
                    // } else {
                    //     $dataCta['LeadBucketCta'][$key]['web'] = $value['cta_slug'] . '_web';
                    //     $dataCta['LeadBucketCta'][$key]['wap'] = $value['cta_slug'] . '_wap';
                    // }
                }
                if (Model::loadMultiple($modelCtas, $dataCta) && Model::validateMultiple($modelCtas)) {
                    foreach ($modelCtas as $modelCtaVal) {
                        if (!empty($modelCtaVal->cta_text)) {
                            // $ctaThankyou = self::checkCtaText($modelCtaVal->cta_text);
                        }
                        if (!$modelCtaVal->save()) {
                            Yii::error($modelCtaVal->getError());
                        }
                    }
                }
                // Yii::$app->session->setFlash('success', 'Success');

                // return $this->render('/cta-thank-you/_tabular-form-cta_thank_you', [
                //     'models' => $modelCtas,
                // ]);

                // return $this->refresh();
                Yii::$app->session->setFlash('success', 'Success');
                Yii::$app->session->setFlash('refresh_tabular', true); // Store flag for rendering tabular data after refresh

                return $this->refresh(); // Refresh the page
            } else {
                return $this->render('update', [
                    'model' => $model,
                    'models' => $models,
                ]);
            }
            return $this->redirect([
                'view',
                'id' => $model->id
            ]);
        }
        // Reload models after refresh
        $models = LeadBucketCta::find()->where(['bucket_id' => $id])->andWhere(['not', ['cta_position' => 'popup_cta']])->all();

        // If refresh flag is set, show tabular form
        if (Yii::$app->session->hasFlash('refresh_tabular')) {
            Yii::$app->session->removeFlash('refresh_tabular');
            // dd($model, $models);
            return $this->render('update', [
                'model' => $model,
                'models' => $models,
            ]);
        } else {
            return $this->render('update', [
                'model' => $model,
                'models' => $models
            ]);
        }
    }

    /**
     * Deletes an existing LeadBucket model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    /* public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }*/

    /**
     * Finds the LeadBucket model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return LeadBucket the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = LeadBucket::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

    private function createSlug($string)
    {
        $string = str_replace(' ', '_', $string); // Replaces all spaces with hyphens.
        return str_replace('__', '_', strtolower(preg_replace('/[^A-Za-z0-9\_]+/', '', $string))); //removes special character
    }

    public function checkCtaText($ctaText)
    {
        $ctaThankYou = CtaThankYou::find()->where(['cta_text' => $ctaText])->one();

        if ($ctaThankYou) {
            $results[] = [
                'cta_text' => $ctaText,
                'exists' => true,
                'cta_text_returned' => $ctaThankYou->cta_text
            ];
        } else {
            // If not found, save the new CTA text
            $ctaThankYou = new CtaThankYou();
            $ctaThankYou->cta_text = $ctaText;
            $ctaThankYou->save();

            $results[] = [
                'cta_text' => $ctaText,
                'exists' => false,
                'cta_text_returned' => $ctaText
            ];
        }
    }

    public function addThankYouMessage($postRequest)
    {
        foreach ($postRequest as $value) {
            $model = CtaThankYou::find()
                ->where(['entity_type' => $value['entity_type']])
                ->andWhere(['cta_text' => $value['cta_text']])
                ->one();
            if (!$model) {
                $model = new CtaThankYou();
            }

            $model->entity_type = $value['entity_type'];
            $model->cta_text = $value['cta_text'];
            $model->thank_you_text = $value['thank_you_text'];
            $model->status = LeadBucket::STATUS_ACTIVE;
            $model->save();
        }
    }

    public function addCtaThankYouText($leadBucket, $entity_id)
    {
        if (empty($leadBucket) || empty($entity_id)) {
            return [];
        }
        if ($entity_id == LeadBucket::LEAD_ENTITY_NEWS_AND_ARTICLE) {
            $ctaTexts = array_column($leadBucket, 'cta_id');
        } else {
            $ctaTexts = array_column($leadBucket, 'cta_text');
        }

        foreach ($ctaTexts as $cta_text) {
            if ($entity_id == LeadBucket::LEAD_ENTITY_NEWS_AND_ARTICLE) {
                $text = LeadBucketCtaDetail::find()->select(['cta_text'])->where(['id' => $cta_text])->one();
                $cta_text = $text->cta_text;
            }

            $model = new CtaThankYou();
            $model->entity_type = (int) $entity_id;
            $model->cta_text = $cta_text;
            $model->thank_you_text = '';
            $model->status = LeadBucket::STATUS_ACTIVE;
            $model->save();
        }
    }
}
