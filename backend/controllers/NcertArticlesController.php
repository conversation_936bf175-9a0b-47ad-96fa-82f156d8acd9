<?php

namespace backend\controllers;

use Yii;
use common\models\NcertArticles;
use backend\models\NcertArticlesSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use common\services\S3Service;
use common\helpers\DataHelper;
use yii\db\Query;
use yii\helpers\ArrayHelper;

/**
 * NcertArticlesController implements the CRUD actions for NcertArticles model.
 */
class NcertArticlesController extends Controller
{
    /**
     * Lists all NcertArticles models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new NcertArticlesSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
            'userList' => $this->getUser()
        ]);
    }

    /**
     * Displays a single NcertArticles model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new NcertArticles model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new NcertArticles();

        if ($model->load(Yii::$app->request->post())) {
            $data = Yii::$app->request->post();
            /**** Check for cache and restricted urls in content ****/
            if (!empty($data['NcertArticles']['content'])) {
                $restrictedUrl = DataHelper::checkRestrictedUrl($data['NcertArticles']['content']);
            }
            if (!empty($restrictedUrl)) {
                return $this->redirect(Yii::$app->request->referrer);
            }
            /*********************************************************/

            if (isset($model->cover_image)) {
                $articleImage = \yii\web\UploadedFile::getInstance($model, 'cover_image');
                if (isset($articleImage)) {
                    $articleImageName = NcertArticles::ENTITY_NCERT . '-' . md5(uniqid(rand(), true)) . '.' . $articleImage->getExtension();
                    $model->cover_image = $articleImageName;
                    $saveImage =  (new S3Service())->uploadFile(DataHelper::s3Path($articleImageName, 'article_general'), $articleImage->tempName);
                    if ($saveImage) {
                        $model->cover_image = $articleImageName;
                    }
                }
            }

            $model->save();
            return $this->redirect(['view', 'id' => $model->id]);
        } else {
            return $this->render('create', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Updates an existing NcertArticles model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);
        $oldNcertArticleImage = $model->cover_image;

        if ($model->load(Yii::$app->request->post())) {
            $data = Yii::$app->request->post();
            /**** Check for cache and restricted urls in content ****/
            if (!empty($data['NcertArticles']['content'])) {
                $restrictedUrl = DataHelper::checkRestrictedUrl($data['NcertArticles']['content']);
            }
            if (!empty($restrictedUrl)) {
                return $this->redirect(Yii::$app->request->referrer);
            }
            /*********************************************************/
            if (isset($model->cover_image)) {
                $articleImage = \yii\web\UploadedFile::getInstance($model, 'cover_image');
                if (isset($articleImage)) {
                    $newFileName = $model->slug . '.' . $articleImage->getExtension();
                    $model->cover_image = $newFileName;
                    $saveImage =  (new S3Service())->uploadFile(DataHelper::s3Path($newFileName, 'article_general'), $articleImage->tempName);
                    if ($saveImage) {
                        $model->cover_image = $newFileName;
                        $model->save();
                        return $this->redirect(['view', 'id' => $model->id]);
                    }
                }
            }
            $model->cover_image = $oldNcertArticleImage;

            $model->save();
            return $this->redirect(['view', 'id' => $model->id]);
        } else {
            return $this->render('update', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Deletes an existing NcertArticles model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the NcertArticles model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return NcertArticles the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = NcertArticles::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

    public function getUser()
    {
        $query = new Query();
        $query->select(['u.id', 'u.name'])
            ->from('user as u')
            ->innerJoin('ncert_articles as ns', 'ns.author_id = u.id');
        $result = $query->all();

        if (empty($result)) {
            return [];
        }

        return ArrayHelper::map($result, 'id', 'name');
    }
    
    /**
     * Displays a single NcertContent value for preview.
     * @param integer $id
     * @return mixed
     */
    public function actionPreview($id)
    {
        return $this->render('preview', [
            'model' => $this->findModel($id),
        ]);
    }
}
