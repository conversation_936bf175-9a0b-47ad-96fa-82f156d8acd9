<?php

namespace backend\controllers;

use Yii;
use common\models\MediaDrive;
use backend\models\MediaDriveSearch;
use yii\web\Controller;
use yii\web\UploadedFile;
use common\services\S3Service;
use common\helpers\DataHelper;
use common\models\MediaDriveUploadType;
use Faker\Core\Number;
use yii\web\NotFoundHttpException;

use function PHPSTORM_META\type;

/**
 * MediaDriveController implements the CRUD actions for MediaDrive model.
 */
class MediaDriveController extends Controller
{
    /**
     * Lists all MediaDrive models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new MediaDriveSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Creates a new MediaDrive model.
     * @return mixed
     */
    public function actionUpload()
    {
        $request = Yii::$app->request;
        $model = new MediaDrive();
        if ($request->isAjax) {
            $fileId = $_POST['dzuuid'];
            $chunkIndex = $_POST['dzchunkindex'] + 1;
            $chunkTotal = $_POST['dztotalchunkcount'];
            // file path variables
            $ds = DIRECTORY_SEPARATOR;
            $targetPath = '/tmp' . '/';
            $fileType = strtolower(pathinfo($_FILES['file']['name'], PATHINFO_EXTENSION));
            $fileSize = $_FILES['file']['size'];
            $filename = "{$fileId}-{$chunkIndex}.{$fileType}";
            $targetFile = $targetPath . $filename;

            // change directory permissions
            // chmod(realpath($targetPath), 0777) or die("Could not modify directory permissions.");

            $returnResponse = function ($info = null, $filelink = null, $status = 'error') {
                die(json_encode([
                    'status' => $status,
                    'info' => $info,
                    'file_link' => $filelink
                ]));
            };
            move_uploaded_file($_FILES['file']['tmp_name'], $targetFile);
            // Be sure that the file has been uploaded
            if (!file_exists($targetFile)) {
                $returnResponse("An error occurred and we couldn't upload the requested file.");
            }
            // chmod($targetFile, 0777) or $returnResponse('Could not reset permissions on uploaded chunk.');

            $returnResponse(null, null, 'success');
        } else {
            return $this->render('upload', [
                'model' => $model,
            ]);
        }
    }


    public function actionChunkConcat()
    {
        $model = new MediaDrive();
        $binary = '';
        // get variables
        $fileId = $_GET['dzuuid'];
        $chunkTotal = $_GET['dztotalchunkcount'];

        // file path variables
        $ds = DIRECTORY_SEPARATOR;
        //$targetPath = dirname(__FILE__) . "{$ds}uploads{$ds}";
        $targetPath = '/tmp' . '/';
        $fileType = $_GET['fileName'];

        /* ========================================
        DEPENDENCY FUNCTIONS
        ======================================== */

        $returnResponse = function ($info = null, $filelink = null, $status = 'error') {
            die(json_encode([
                'status' => $status,
                'info' => $info,
                'file_link' => $filelink
            ]));
        };
        /* ========================================
        CONCATENATE UPLOADED FILES
        ======================================== */

        // loop through temp files and grab the content
        for ($i = 1; $i <= $chunkTotal; $i++) {
            // target temp file
            $temp_file_path = realpath("{$targetPath}{$fileId}-{$i}.{$fileType}") or $returnResponse('Your chunk was lost mid-upload.');
            // copy chunk
            $chunk = file_get_contents($temp_file_path);
            if (empty($chunk)) {
                $returnResponse('Chunks are uploading as empty strings.');
            }
            $binary .= $chunk;

            // add chunk to main file
            //file_put_contents("{$targetPath}{$fileId}.{$fileType}", $chunk, FILE_APPEND);

            // delete chunk
            unlink($temp_file_path);
            if (file_exists($temp_file_path)) {
                $returnResponse('Your temp files could not be deleted.');
            }
        }
        $model->entity = $_GET['entity'];
        $entity = $_GET['entity'];
        $subPage = $_GET['sub_page'];
        if ($model->entity == 'articles') {
            $entity = 'article';
            $subPage = null;
        }
        if ($model->entity == 'ncert') {
            $entity = 'ncertArticles';
            $subPage = null;
        }
        if (($model->entity !== 'others') && ($model->entity !== 'exam')) {
            $model->scenario = 'chunk-concat';
            $entityModel = '\common\models\\' . ucfirst($entity);
            $model->description = $_GET['desc'];
            $model->entity_id = $_GET['entity_id'];
            $model->page = $this->findSlug($entityModel, $model->entity, $model->entity_id);
            $model->sub_page = $subPage;
        } else if ($model->entity == 'exam') {
            $currentYear = date('Y', time());
            $year = array_reverse(range(($currentYear - 10), date('Y')));
            $entityModel = '\common\models\\' . ucfirst($entity);
            $model->description = $_GET['desc'];
            $model->entity_id = $_GET['entity_id'];
            $model->page = $this->findSlug($entityModel, $model->entity, $model->entity_id);
            $model->sub_page = $subPage;
            $model->year = $year[$_GET['year']];
            $model->slot = $_GET['slot'];
            $model->source = $_GET['source'];
            $model->section = $_GET['section'];
            $model->subject = $_GET['subject'];
        } else {
            $model->file_name = $fileId . '.' . $fileType;
            $model->description = $_GET['desc'];
            $model->entity_id = $_GET['entity_id'];
        }
        $model->status = $_GET['status'];
        if (!$model->validate()) {
            $returnResponse($this->getValidationErrors($model->errors));
        }
        $uploadName = '';
        $path = null;
        if (($model->entity !== 'others')) {
            $uploadObj = MediaDriveUploadType::find()
                ->select(['upload_type'])
                ->where(['entity' => $model->entity, 'id' => $subPage, 'status' => MediaDrive::STATUS_ACTIVE])
                ->one();
            if (!empty($uploadObj->upload_type)) {
                $uploadName = $this->slugify($uploadObj->upload_type);
            }

            $path = $model->page . '-' . $uploadName;
            $model->file_name = $path . '-' . $fileId . '.' . $fileType;
        }
        $path = DataHelper::s3Path($model->file_name, 'downloadables', false);
        $saveImage =  (new S3Service())->uploadBinaryFile($path, $binary, 'application/pdf');
        if ($saveImage) {
            if ($model->save()) {
                $returnResponse(null, null, 'success');
            } else {
                $returnResponse('There was an error saving your file.');
            }
        } else {
            $returnResponse('There was an error uploading your file.');
        }
    }

    private function getValidationErrors($errors)
    {
        $validationErrros = [];
        foreach (array_keys($errors) as $error) {
            $validationErrros = array_merge($validationErrros, array_values($errors[$error]));
        }
        return reset($validationErrros);
    }

    protected function findSlug($entityModel, $entity, $entityId)
    {
        $pageSlug = $entityModel::find()->select(['slug'])->where(['id' => $entityId])->one();
        return $pageSlug->slug ?? null;
    }

    // public function beforeAction($action)
    // {
    //     $this->enableCsrfValidation = false;
    //     return parent::beforeAction($action);
    // }

    public function actionUploadOptionsPage()
    {
        $requestParam = Yii::$app->request->post('depdrop_parents');
        $subpage = [];
        if (!empty($requestParam)) {
            $entity = $requestParam[0];
            $subPageArr = MediaDriveUploadType::find()
                ->select(['id', 'upload_type'])
                ->where(['entity' => $entity, 'status' => MediaDrive::STATUS_ACTIVE])
                ->asArray()
                ->all();
            foreach ($subPageArr as $key => $value) {
                $name = $value['upload_type'];

                if (in_array($name, ['Unofficial and Official Answer Keys'])) {
                    $name = 'Answer Keys';
                }

                $subpage[$value['id']] = $name;
            }

            $subpage = array_map(function ($key, $value) {
                return [
                    'id' => $key,
                    'name' => $value
                ];
            }, array_keys($subpage), array_values($subpage));

            if (!empty($subpage)) {
                return json_encode(['output' => $subpage, 'selected' => '']);
            } else {
                return false;
            }
        }
    }

    public function actionUpdate($id)
    {
        $model = $this->findModel($id);
        $data = Yii::$app->request->post();
        if (!empty($model) && $model->load(Yii::$app->request->post())) {
            $model->status = $data['MediaDrive']['status'];
            $model->save();
            return $this->redirect(['index']);
        } else {
            return $this->render('update', [
                'model' => $model,
            ]);
        }
    }

    protected function findModel($id)
    {
        if (($model = MediaDrive::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

    protected function slugify($string)
    {
        $string = strtolower($string);
        $string = preg_replace('/[\s_]+/', '-', $string);
        $string = preg_replace('/[^a-z0-9\-]/', '', $string);
        $string = preg_replace('/-+/', '-', $string);
        return trim($string, '-');
    }
}
