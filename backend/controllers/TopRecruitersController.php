<?php

namespace backend\controllers;

use Yii;
use common\models\TopRecruiters;
use backend\models\TopRecruitersSearch;
use common\helpers\DataHelper;
use common\models\College;
use common\services\S3Service;
use yii\base\Model;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\web\UploadedFile;

/**
 * TopRecruitersController implements the CRUD actions for TopRecruiters model.
 */
class TopRecruitersController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all TopRecruiters models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new TopRecruitersSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single TopRecruiters model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new TopRecruiters model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new TopRecruiters();

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->id]);
        } else {
            return $this->render('create', [
                'model' => $model,
            ]);
        }
    }

    public function actionTabularForm($college_id = '', $clp_id = '')
    {
        if (empty($college_id)) {
            $model = new TopRecruiters();
            $models[] = $model;
        }

        $models = $this->findRecruitersModel($college_id);
        $college = College::findOne(['id' => $college_id]);
        $collegeName = $college->name ?? '';

        if (empty($models)) {
            $model = new TopRecruiters();
            $model->college_id = $college_id;
            $models[] = $model;
        }

        $request = Yii::$app->getRequest();

        if ($request->isPost) {
            $postData = Yii::$app->request->post('TopRecruiters', []);

            $models = [];
            foreach ($postData as $i => $data) {
                $model = isset($data['id']) && !empty($data['id']) ? TopRecruiters::findOne($data['id']) : new TopRecruiters();
                $model->college_id = $college_id;
                $models[] = $model;
            }
            $hasError = false;
            if (Model::loadMultiple($models, Yii::$app->request->post()) && Model::validateMultiple($models)) {
                foreach ($models as $index => $model) {
                    $file = UploadedFile::getInstanceByName("TopRecruiters[$index][recruiter_image]");

                    // Check if no file and model image attribute is empty (no previously saved image)
                    if (empty($file) && empty($model->recruiter_image)) {
                        Yii::$app->session->setFlash('error', 'Please upload image for recruiter #' . ($index + 1));
                        $hasError = true;
                        break;
                    }

                    if ($file && !$file->hasError && !$hasError) {
                        $newImageName = uniqid('logo_', true) . '.' . $file->extension;

                        $s3Path = DataHelper::s3Path($newImageName, 'clp_recruiter_logos');

                        // Upload the file to S3, passing the local temp file path
                        $saveImage = (new S3Service())->uploadFile($s3Path, $file->tempName);

                        if ($saveImage) {
                            $model->recruiter_image = $newImageName;
                        }
                    }

                    if (!$model->save()) {
                        Yii::$app->session->setFlash('error', 'Error saving: ' . json_encode($model->getErrors()));
                        return $this->render('_tabular_form', [
                            'models' => $models ?? [],
                            'collegeId' => $college_id,
                            'collegeName' => $collegeName,
                            'clp_id' => $clp_id
                        ]);
                    }
                }
                if (!$hasError) {
                    Yii::$app->session->setFlash('success', 'Data added successfully');
                    // return $this->refresh();
                    // Get college_id from first model (all should be same)
                    $redirectCollegeId = isset($models[0]) ? $models[0]->college_id : '';

                    return $this->redirect(['tabular-form', 'college_id' => $redirectCollegeId]);
                }
            }
        }

        return $this->render('_tabular_form', [
            'models' => $models ?? [],
            'collegeId' => $college_id,
            'collegeName' => $collegeName,
            'clp_id' => $clp_id
        ]);
    }

    /**
     * Updates an existing TopRecruiters model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->id]);
        } else {
            return $this->render('update', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Deletes an existing TopRecruiters model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the TopRecruiters model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return TopRecruiters the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = TopRecruiters::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

    protected function findRecruitersModel($college_id)
    {
        return TopRecruiters::find()
            ->where(['college_id' => $college_id])
            ->all(); // Will return an empty array [] if no records found
    }
}
