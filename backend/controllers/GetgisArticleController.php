<?php

namespace backend\controllers;

use Yii;
use common\models\GetgisArticle;
use backend\models\GetgisArticleSearch;
use common\helpers\DataHelper;
use common\models\GetgisArticleCategory;
use common\services\S3Service;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\helpers\ArrayHelper;

/**
 * GetgisArticleController implements the CRUD actions for GetgisArticle model.
 */
class GetgisArticleController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all GetgisArticle models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new GetgisArticleSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        $categories = ArrayHelper::map(GetgisArticleCategory::find()->select(['id', 'name'])->where(['status' => GetgisArticleCategory::STATUS_ACTIVE])->all(), 'id', 'name');

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
            'categories' => $categories
        ]);
    }

    /**
     * Displays a single GetgisArticle model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new GetgisArticle model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */

    public function actionCreate()
    {
        $model = new GetgisArticle();
        $model->scenario = 'create';
        $postRequest = Yii::$app->request->post();

        if ($model->load($postRequest)) {
            $model->created_by = $model->updated_by = Yii::$app->user->identity->id;

            $categoryImage = \yii\web\UploadedFile::getInstance($model, 'cover_image');
            if ($categoryImage) {
                $categoryImageName = 'Getgis-article-' . md5(uniqid(rand(), true)) . '.' . $categoryImage->getExtension();
                $model->cover_image = $categoryImageName;
                $saveImage = (new S3Service())->uploadFile(DataHelper::s3Path($categoryImageName, 'article_general'), $categoryImage->tempName);
                if ($saveImage) {
                    $model->cover_image = $categoryImageName;
                }
            }

            $categoryThumbnailImage = \yii\web\UploadedFile::getInstance($model, 'thumbnail_image');
            if ($categoryThumbnailImage) {
                $categoryThumbnailImageName = 'Getgis-article-' . md5(uniqid(rand(), true)) . '.' . $categoryThumbnailImage->getExtension();
                $model->thumbnail_image = $categoryThumbnailImageName;
                $saveImage = (new S3Service())->uploadFile(DataHelper::s3Path($categoryThumbnailImageName, 'article_general'), $categoryThumbnailImage->tempName);
                if ($saveImage) {
                    $model->thumbnail_image = $categoryThumbnailImageName;
                }
            }

            if ($model->validate()) {
                if ($model->save()) {
                    return $this->redirect(['view', 'id' => $model->id]);
                }
            }

            return $this->render('create', [
                'model' => $model,
            ]);
        } else {
            return $this->render('create', [
                'model' => $model,
            ]);
        }
    }


    /**
     * Updates an existing GetgisArticle model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);
        $postRequest = Yii::$app->request->post();
        $model->scenario = 'update';

        if ($model->load($postRequest)) {
            $categoryCoverImage = \yii\web\UploadedFile::getInstance($model, 'cover_image');
            if ($categoryCoverImage) {
                $newFileName = $model->slug . '.' . $categoryCoverImage->getExtension();
                $model->cover_image = $newFileName;
                if ((new S3Service())->uploadFile(DataHelper::s3Path($newFileName, 'article_general'), $categoryCoverImage->tempName)) {
                    $model->save();
                    return $this->redirect(['view', 'id' => $model->id]);
                }
            } else {
                $model->cover_image = $model->getOldAttribute('cover_image');
            }

            $categoryThumbnailImage = \yii\web\UploadedFile::getInstance($model, 'thumbnail_image');
            if ($categoryThumbnailImage) {
                $newFileName = $model->slug . '.' . $categoryThumbnailImage->getExtension();
                $model->thumbnail_image = $newFileName;
                if ((new S3Service())->uploadFile(DataHelper::s3Path($newFileName, 'article_general'), $categoryThumbnailImage->tempName)) {
                    $model->save();
                    return $this->redirect(['view', 'id' => $model->id]);
                }
            } else {
                $model->thumbnail_image = $model->getOldAttribute('thumbnail_image');
            }

            $model->save();
            return $this->redirect(['view', 'id' => $model->id]);
        } else {
            return $this->render('update', [
                'model' => $model,
            ]);
        }
    }

    public function actionPublishArticle()
    {
        if (Yii::$app->request->isPost) {
            \Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
            // if (!Yii::$app->user->can('ContentPublishPermission')) {
            //     Yii::$app->session->addFlash('error', 'Permission Denied!');
            //     return ['message' => 'Not Authorized'];
            // }
            $id = Yii::$app->request->post('id');
            $blog = GetgisArticle::findOne(['id' => $id]);
            if ($blog->is_published) {
                Yii::$app->session->addFlash('error', 'Article already published');
                return ['message' => 'Already Published'];
            }

            $res = $blog->updateAttributes([
                'is_published' => GetgisArticle::PUBLISHED_YES,
                'status' => GetgisArticle::STATUS_ACTIVE,
                'published_by' => Yii::$app->user->identity->id,
                'published_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
            if ($res >= 1) {
                Yii::$app->session->addFlash('success', 'Article Published');
                return ['message' => 'Published'];
            }
            Yii::$app->session->addFlash('error', 'Article Publish Failed');

            return ['message' => 'Not Published'];
        }
    }

    /**
     * Displays a single ArticleContent value for preview.
     * @param integer $id
     * @return mixed
     */
    public function actionPreview($id)
    {
        return $this->render('preview', [
            'model' => $this->findModel($id),
        ]);
    }


    /**
     * Deletes an existing GetgisArticle model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the GetgisArticle model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return GetgisArticle the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = GetgisArticle::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }
}
