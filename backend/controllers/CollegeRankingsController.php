<?php

namespace backend\controllers;

use Yii;
use common\models\CollegeRankings;
use common\models\CollegeRankingsSearch;
use common\models\College;
use common\models\Course;
use common\models\CollegeCourse;
use common\models\Stream;
use common\models\Specialization;
use common\models\OtherRankings;
use common\models\CollegeRankingPublisher;
use common\models\old\CollegesRank;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\base\Model;
use common\helpers\CollegeRanksDataHelper;

/**
 * CollegeRankingsController implements the CRUD actions for CollegeRankings model.
 */
class CollegeRankingsController extends Controller
{
    protected static $entity = [
        'course' => 'course',
        'stream' => 'stream',
        'specialization' => 'specialization_new',
        'other' => 'other_rankings'
    ];

    protected static $fields = [
        'course' => ['id', 'name as display_name'],
        'stream' => ['id', 'name as display_name'],
        'specialization' => ['id', 'display_name'],
        'other' => ['id', 'name as display_name']
    ];

    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all CollegeRankings models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new CollegeRankingsSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single CollegeRankings model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }



    /**
     * Deletes an existing CollegeRankings model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the CollegeRankings model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return CollegeRankings the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = CollegeRankings::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }
    /**
     * Updates an existing CollegeRankings model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */

    public function actionUpdateCollegeRank()
    {
        $request = Yii::$app->request;
        if (empty($request->get('college_id'))) {
            throw new NotFoundHttpException();
        }
        $college = College::find()->where(['id' => $request->get('college_id')])->one();
        $models = CollegeRankings::find()->where(['college_id' => $college->id])->all();
        if ($request->isPost && isset($request->post()['CollegeRankings'])) {
            $collegeID = $request->get('college_id');
            $data = $request->post();
            $allRankID =  CollegeRankings::find()->select(['id'])->where(['college_id' => $collegeID])->all();
            foreach ($data['CollegeRankings'] as $value) {
                $value['college_id'] =  $collegeID;
                if ($value['id'] != '') {
                    if ($value['criteria_id'] == '') {
                        $value['criteria_id'] =  $value['criteria_id_update'];
                    }
                }
                $this->saveRanks($value);
            }
            $this->deleteAllList($allRankID);
            Yii::$app->session->setFlash('success', 'Success');
            return $this->redirect('/college-rankings/update-college-rank?college_id=' . $collegeID);
        } else {
            return $this->render('update-college-rank', [
                'models' => $models,
                'college' => $college
            ]);
        }
    }

    /*
     Import Rank Record from csv file
    */
    public function actionImportCollegeRank()
    {
        $request = Yii::$app->request;

        $models = [];
        if (!empty($_FILES['file']['tmp_name']) && $request->isPost) {
            if (($handle = fopen($_FILES['file']['tmp_name'], 'r')) == false) {
                echo "Unable to open the file \n";
                die(__FILE__);
            }
            $i = 0;
            while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
                if ($i == 0 || empty($fileop[1])) {
                    $i++;
                    continue;
                }
                if (isset($fileop[0]) && $fileop[0] != '') {
                    $publisherID = CollegeRankingPublisher::find()->select(['id'])->where(['slug' => $fileop[0]])->one();
                    $collegeID = College::find()->select(['id'])->where(['slug' => $fileop[1]])->one();
                    $criteriaId = null;
                    $where = [];
                    if (isset($fileop[2])  &&  $fileop[2] != '') {
                        $where['slug'] = $fileop[3];
                        if ($fileop[2] == CollegeRankings::CRITERIA_OTHER) {
                            $where['publisher_id'] = $publisherID->id;
                        }
                        $model = self::$entity[$fileop[2]];
                        if (isset($model) && $model != '') {
                            list($id) = self::$fields[$fileop[2]];
                        }
                        $data = (new \yii\db\Query())
                            ->select([$id])
                            ->from($model)
                            ->where($where)
                            ->one();
                        if (!empty($data)) {
                            $criteriaId = $data['id'];
                        } else {
                            if ($fileop[2] == CollegeRankings::CRITERIA_OTHER) {
                                $rankModel = new OtherRankings();
                                $rankModel->publisher_id = $publisherID->id;
                                $rankModel->name = ucfirst($fileop[3]);
                                $rankModel->slug = $fileop[3];
                                $rankModel->save();
                                $criteriaId = $rankModel->id;
                            }
                        }
                    } else {
                        continue;
                    }
                    $this->saveORUpdateRank(
                        $publisherID->id,
                        $collegeID->id,
                        $fileop[2],
                        $criteriaId,
                        $fileop[4],
                        $fileop[5],
                        $fileop[6],
                        $id = ''
                    );
                }
            }
            Yii::$app->session->setFlash('success', 'Success');
            return $this->redirect(['college-rankings/index']);
        } else {
            return $this->render('import-college-rank', [
                'models' => $models
            ]);
        }
    }

    /*
     Get Entity Value for Backend Panel
    */
    public function actionGetCriteriaValue()
    {
        $request = Yii::$app->request;
        $type = $request->get('depdrop_parents');
        $publisherID = $request->get('publisher_id');
        $inputext = $request->get('q');
        $model = self::$entity[$type];
        list($id, $name) = self::$fields[$type];
        $query = (new \yii\db\Query())
            ->select([$id, $name])
            ->from($model)
            ->where(['like', 'name', '%' . $inputext . '%', false]);
        if ($type == CollegeRankings::CRITERIA_OTHER) {
            $query->andWhere(['=', 'publisher_id', $publisherID]);
        }
        $data = $query->all();
        if (!empty($data)) {
            $data = array_map(function ($list) {
                return [
                    'id' => $list['id'],
                    'text' => $list['display_name']
                ];
            }, $data);
            \Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
            $out['results'] = array_values($data);
            return $out;
        }
        return '';
    }

    /*
        Save OR Update Rank Panel from file
    */
    public function saveORUpdateRank(
        $publisherID,
        $collegeID,
        $criteria,
        $criteria_id,
        $rank,
        $year,
        $status,
        $id
    ) {
        if ($id != '') {
            $checkDuplicate = CollegeRankings::find()->where(['id' => $id])->one();
        } else {
            $checkDuplicate = CollegeRankings::find()->where(['publisher_id' =>  $publisherID])
                ->andWhere(['college_id' => $collegeID])
                ->andWhere(['criteria' => $criteria])
                ->andWhere(['year' => $year])
                ->andWhere(['criteria_id' => $criteria_id])->one();
        }
        if (!empty($checkDuplicate)) {
            $checkDuplicate->college_id = $collegeID;
            $checkDuplicate->publisher_id =  $publisherID;
            $checkDuplicate->criteria = $criteria;
            $checkDuplicate->criteria_id = $criteria_id;
            $checkDuplicate->rank = $rank;
            $checkDuplicate->year = $year;
            if ($status == CollegeRankings::STATUS_ACTIVE) {
                $checkDuplicate->status = CollegeRankings::STATUS_ACTIVE;
            } else {
                $checkDuplicate->status = CollegeRankings::STATUS_INACTIVE;
            }

            if ($checkDuplicate->update()) {
                return true;
            } else {
                return false;
            }
        } else {
            $model = new CollegeRankings();
            $model->college_id = $collegeID;
            $model->publisher_id = $publisherID;
            $model->criteria = $criteria;
            $model->criteria_id =  $criteria_id;
            $model->rank =  $rank;
            $model->year = $year;
            if ($status == CollegeRankings::STATUS_ACTIVE) {
                $model->status = CollegeRankings::STATUS_ACTIVE;
            } else {
                $model->status = CollegeRankings::STATUS_INACTIVE;
            }
            if ($model->save()) {
                return true;
            } else {
                return false;
            }
        }
    }

    /*
        Save Rank Panel from  Backend
    */
    public function saveRanks($data)
    {
        $model = new CollegeRankings();
        $model->college_id = $data['college_id'];
        $model->publisher_id = $data['publisher_id'];
        $model->criteria = $data['criteria'];
        $model->criteria_id =  $data['criteria_id'];
        $model->rank =   $data['rank'];
        $model->year = $data['year'];
        $model->status =  $data['status'] ?? CollegeRankings::STATUS_INACTIVE;
        if ($model->save()) {
            return true;
        } else {
            print_r($model->getErrors());
            exit();
        }
    }

    /*
        Delete Rank Panel from  Backend
    */
    public function deleteAllList($models)
    {
        if ($models) {
            foreach ($models as $model) {
                $model->delete($model->id);
            }
            return true;
        }
        return true;
    }

    public function actionExportExcel()
    {
        $exportData = CollegeRankings::find()
        ->where(['college_id' => 2544])
        ->all();
        $data['col'][] = 'publisher_slug';
        $data['col'][] = 'college_slug';
        $data['col'][] = 'criteria';
        $data['col'][] = 'criteria_value';
        $data['col'][] = 'rank';
        $data['col'][] = 'status';
        header('Content-type: text/csv');
        header('Content-Disposition: attachment; filename="college-rankings-export' . date('YmdHi') . '.csv"');
        $i=0;
        foreach ($exportData as $valueRow) {
            $data['row'][$i] =  [$valueRow->publisher->slug,$valueRow->college->slug,$valueRow->criteria,CollegeRanksDataHelper::getCriteria($valueRow->criteria, $valueRow->criteria_id),$valueRow->rank,$valueRow->year,$valueRow->status];
            $i++;
        }
       // echo "<pre>"; print_r($data); die;
        foreach ($data['col'] as $key => $value) {
            echo $value . ', ';
        }
        echo "\n";
        foreach ($data['row'] as $line) {
            foreach ($line as $key => $value) {
                echo $value . ', ';
            }
            echo "\n";
        }
    }
}
