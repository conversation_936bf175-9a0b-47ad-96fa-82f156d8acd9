<?php

namespace backend\controllers;

use Yii;
use common\models\SaCollege;
use backend\models\SaCollegeSearch;
use common\helpers\DataHelper;
use common\services\AzureService;
use common\services\S3Service;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\web\UploadedFile;

/**
 * SaCollegeController implements the CRUD actions for SaCollege model.
 */
class SaCollegeController extends Controller
{
    protected $azureService;

    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }


    public function __construct($id, $module, AzureService $azureService, $config = [])
    {
        $this->azureService = $azureService;
        parent::__construct($id, $module, $config);
    }

    /**
     * Lists all SaCollege models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new SaCollegeSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single SaCollege model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new SaCollege model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new SaCollege();
        $postRequest = Yii::$app->request->post();

        if ($model->load($postRequest)) {
            if (isset($model->cover_image)) {
                $coverImage = UploadedFile::getInstance($model, 'cover_image');
                if (isset($coverImage)) {
                    $newImageName = $model->slug;
                    $saveImage =  (new S3Service())->uploadFile(DataHelper::s3Path($newImageName . '.' . $coverImage->extension, 'sa_college_general'), $coverImage->tempName);
                    if ($saveImage) {
                        $model->cover_image = $newImageName . '.' . $coverImage->extension;
                    }
                }
            }

            if (isset($model->logo_image)) {
                $logoImage = UploadedFile::getInstance($model, 'logo_image');
                if (isset($logoImage)) {
                    $newImageName = $model->slug;
                    $saveImage =  (new S3Service())->uploadFile(DataHelper::s3Path($newImageName . '.' . $logoImage->extension, 'sa_college_logo'), $logoImage->tempName);
                    if ($saveImage) {
                        $model->logo_image = $newImageName . '.' . $logoImage->extension;
                    }
                }
            }
            $model->created_by = $model->updated_by = Yii::$app->user->identity->id;

            if ($model->save()) {
                return $this->redirect(['view', 'id' => $model->id]);
            }

            return $this->redirect(['view', 'id' => $model->id]);
        } else {
            return $this->render('create', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Updates an existing SaCollege model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);
        $postRequest = Yii::$app->request->post();

        if ($model->load($postRequest)) {
            if (isset($model->cover_image)) {
                if (empty($model->cover_image)) {
                    unset($model->cover_image);
                }
                $coverImage = UploadedFile::getInstance($model, 'cover_image');
                if (isset($coverImage)) {
                    $newImageName = $model->slug;
                    $saveImage =  (new S3Service())->uploadFile(DataHelper::s3Path($newImageName . '.' . $coverImage->extension, 'sa_college_general'), $coverImage->tempName);
                    if ($saveImage) {
                        $model->cover_image = $newImageName . '.' . $coverImage->extension;
                    }
                }
            }

            if (isset($model->logo_image)) {
                if (empty($model->logo_image)) {
                    unset($model->logo_image);
                }
                $logoImage = UploadedFile::getInstance($model, 'logo_image');
                if (isset($logoImage)) {
                    $newImageName = $model->slug;
                    $saveImage =  (new S3Service())->uploadFile(DataHelper::s3Path($newImageName . '.' . $logoImage->extension, 'sa_college_logo'), $logoImage->tempName);
                    if ($saveImage) {
                        $model->logo_image = $newImageName . '.' . $logoImage->extension;
                    }
                }
            }

            $model->updated_by = Yii::$app->user->identity->id;

            if ($model->save()) {
                return $this->redirect(['view', 'id' => $model->id]);
            }

            return $this->redirect(['view', 'id' => $model->id]);
        } else {
            return $this->render('update', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Deletes an existing SaCollege model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the SaCollege model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return SaCollege the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = SaCollege::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }
}
