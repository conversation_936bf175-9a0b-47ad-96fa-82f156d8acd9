<?php

namespace backend\controllers;

use Yii;
use common\models\ExamDate;
use backend\models\ExamDateSearch;
use common\helpers\DataHelper;
use common\models\Exam;
use yii\web\Response;
use yii\base\Model;
use yii\bootstrap\ActiveForm;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * ExamDateController implements the CRUD actions for ExamDate model.
 */
class ExamDateController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all ExamDate models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new ExamDateSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    public function actionTest()
    {
        $examDate = new ExamDate();

        if ($examDate->load(Yii::$app->request->post()) && $examDate->save()) {
            return $this->redirect(['test']);
        }

        $searchModel = new ExamDateSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('test', [
            'model' => $examDate,
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single ExamDate model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new ExamDate model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate($examId)
    {
        $examModel = $this->findExamModel($examId);
        $models = $this->getItems($examModel);
        $request = Yii::$app->getRequest();

        if ($request->isPost) {
            // Load data into the models
            if (Model::loadMultiple($models, Yii::$app->request->post())) {
                // Create an empty array to hold the filtered models
                $filteredModels = [];

                // Filter models and assign required models
                foreach ($models as $model) {
                    if (in_array($model->slug, ['exam-start', 'result-date'])) {
                        $model->allExamDates = $models;
                        $filteredModels[] = $model;
                    }
                }

                // Validate and save only the filtered models
                if (Model::validateMultiple($models)) {
                    foreach ($models as $model) {
                        $model->save(false);
                    }
                    return $this->refresh();
                }
            }
            return $this->render('create', [
                'models' => $models,
            ]);
        }

        return $this->render('create', [
            'models' => $models,
        ]);
    }

    /**
     * Deletes an existing ExamDate model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    // public function actionDelete($id)
    // {
    //     $this->findModel($id)->delete();

    //     return $this->redirect(['index']);
    // }

    /**
     * Finds the ExamDate model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return ExamDate the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = ExamDate::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

    /**
     * Finds the Exam model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return Exam the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findExamModel($id)
    {
        if (($model = Exam::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

    private function getItems(Exam $exam)
    {
        $items = [];
        $examDates = $exam->examDates;

        $data = DataHelper::examDateList();
        foreach ($data as $slug => $name) {
            $item = new ExamDate();
            $item->setAttributes(['slug' =>  $slug, 'name' => $name, 'exam_id' => $exam->id]);
            $items[$slug] = $item;
        }

        foreach ($examDates as $examDate) {
            $items[$examDate->slug] = $examDate;
        }

        return $items;
    }
}
