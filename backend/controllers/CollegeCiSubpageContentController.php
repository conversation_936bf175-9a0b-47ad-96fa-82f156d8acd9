<?php

namespace backend\controllers;

use Yii;
use common\models\CollegeCiSubpageContent;
use backend\models\CollegeCiSubpageContentSearch;
use common\helpers\DataHelper;
use common\models\CollegeCourseContent;
use yii\base\Model;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * CollegeCiSubpageContentController implements the CRUD actions for CollegeCiSubpageContent model.
 */
class CollegeCiSubpageContentController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all CollegeCiSubpageContent models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new CollegeCiSubpageContentSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single CollegeCiSubpageContent model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new CollegeCiSubpageContent model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new CollegeCiSubpageContent();

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->id]);
        } else {
            return $this->render('create', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Updates an existing CollegeCiSubpageContent model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->id]);
        } else {
            return $this->render('update', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Deletes an existing CollegeCiSubpageContent model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the CollegeCiSubpageContent model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return CollegeCiSubpageContent the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = CollegeCiSubpageContent::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

    public function actionAddSubpageContent($college_course_content_id)
    {
        $models = $this->getSubpageItems($college_course_content_id);
        $request = Yii::$app->request;

        if ($request->isPost) {
            $postData = Yii::$app->request->post('CollegeCiSubpageContent', []);

            foreach ($postData as $key => $data) {
                if (empty($data['content'])) {
                    continue;
                }

                // Normalize subpage key (convert 'cut-off-1', 'cut-off-2', etc., to 'cut-off')
                $normalizedKey = preg_replace('/^cut-off-\d+$/', 'cut-off', $key);

                // Find existing model or create a new one
                $query = CollegeCiSubpageContent::find()
                    ->where([
                        'college_course_content_id' => $college_course_content_id,
                        'subpage' => $normalizedKey
                    ]);

                if (!empty($data['year'])) {
                    $query->andWhere(['year' => $data['year']]);
                }

                $model = $query->one();

                if (!$model) {
                    $model = new CollegeCiSubpageContent();
                }

                // Set attributes from form data
                $model->setAttributes([
                    'college_course_content_id' => $college_course_content_id,
                    'subpage' => $normalizedKey, // Store only 'cut-off'
                    'content' => $data['content'] ?? null,
                    'year' => $data['year'] ?? null,
                    'h1' => $data['h1'] ?? null,
                    'meta_title' => $data['meta_title'] ?? null,
                    'meta_description' => $data['meta_description'] ?? null,
                    'status' => $data['status'] ?? null,
                ]);

                if (!$model->save()) {
                    Yii::$app->session->setFlash('error', 'Error saving subpage: ' . json_encode($model->getErrors()));
                    return $this->render('add-subpage-content', [
                        'models' => $models,
                        'college_course_content' => $this->getCollegeCourseContent($college_course_content_id),
                    ]);
                }
            }

            Yii::$app->session->setFlash('success', 'Subpage content added successfully.');
            return $this->refresh();
        } else {
            return $this->render('add-subpage-content', [
                'models' => $models,
                'college_course_content' => $this->getCollegeCourseContent($college_course_content_id),
            ]);
        }
    }

    protected function findSubpageContentModels($college_course_content_id)
    {
        $models = CollegeCiSubpageContent::find()->where(['college_course_content_id' => $college_course_content_id])->all();
        return !empty($models) ? $models : [];
    }

    protected function getCollegeCourseContent($college_course_content_id)
    {
        $model = CollegeCourseContent::findOne($college_course_content_id);
        if ($model !== null) {
            return $model;
        }
        throw new NotFoundHttpException('The requested page does not exist.');
    }

    private function getSubpageItems($college_course_content_id)
    {
        $items = [];
        $defaultSubpages = array_column(DataHelper::$courseCiPiSubpageItems, 'id');

        // Fetch all existing models for the given college_course_content_id
        $existingModels = CollegeCiSubpageContent::find()
            ->where(['college_course_content_id' => $college_course_content_id])
            ->all();

        foreach ($defaultSubpages as $subpage) {
            // If the subpage is 'cut-off', handle multiple entries
            if ($subpage === 'cut-off') {
                $cutOffModels = [];

                foreach ($existingModels as $model) {
                    if (preg_match('/^cut-off(-\d+)?$/', $model->subpage)) {
                        $cutOffModels[] = $model;
                    }
                }

                if (!empty($cutOffModels)) {
                    foreach ($cutOffModels as $index => $model) {
                        $key = $index === 0 ? 'cut-off' : "cut-off-$index"; // No -0, just "cut-off"
                        $items[$key] = $model;
                    }
                } else {
                    // If no cut-off records exist, create a default entry
                    $item = new CollegeCiSubpageContent();
                    $item->setAttributes([
                        'college_course_content_id' => $college_course_content_id,
                        'subpage' => 'cut-off'
                    ]);
                    $items['cut-off'] = $item;
                }
            } else {
                // Find the existing model for the subpage
                $model = null;
                foreach ($existingModels as $m) {
                    if ($m->subpage === $subpage) {
                        $model = $m;
                        break;
                    }
                }

                if ($model) {
                    $items[$subpage] = $model;
                } else {
                    $item = new CollegeCiSubpageContent();
                    $item->setAttributes([
                        'college_course_content_id' => $college_course_content_id,
                        'subpage' => $subpage
                    ]);
                    $items[$subpage] = $item;
                }
            }
        }

        return $items;
    }
}
