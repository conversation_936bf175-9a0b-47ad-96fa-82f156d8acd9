<?php

namespace backend\controllers;

use Yii;
use common\models\ClpCollegeAboutUs;
use backend\models\ClpCollegeAboutUsSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * ClpCollegeAboutUsController implements the CRUD actions for ClpCollegeAboutUs model.
 */
class ClpCollegeAboutUsController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all ClpCollegeAboutUs models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new ClpCollegeAboutUsSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single ClpCollegeAboutUs model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new ClpCollegeAboutUs model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate($college_id, $clp_id)
    {
        $model = ClpCollegeAboutUs::findOne(['college_id' => $college_id]);

        if (!$model) {
            $model = new ClpCollegeAboutUs();
        }

        $request = Yii::$app->request->post();

        $model->college_id = $college_id;

        if ($model->load($request)) {
            if ($model->save()) {
                return $this->redirect([
                    'custom-landing-page/view',
                    'id' => $clp_id,
                ]);
            }

            Yii::$app->session->setFlash('error', 'College has already been taken.');
        } else {
            return $this->render('create', [
                'model' => $model,
                'clp_id' => $clp_id
            ]);
        }
    }

    /**
     * Updates an existing ClpCollegeAboutUs model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->id]);
        } else {
            return $this->render('update', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Deletes an existing ClpCollegeAboutUs model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the ClpCollegeAboutUs model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return ClpCollegeAboutUs the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = ClpCollegeAboutUs::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }
}
