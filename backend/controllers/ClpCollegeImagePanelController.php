<?php

namespace backend\controllers;

use common\helpers\DataHelper;
use common\models\ClpCollegeImagePanel;
use common\services\S3Service;
use Yii;
use backend\models\ClpCollegeImagePanelSearch;
use common\models\CustomLandingPage;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * ClpCollegeImagePanelController implements the CRUD actions for ClpCollegeImagePanel model.
 */
class ClpCollegeImagePanelController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all ClpCollegeImagePanel models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new ClpCollegeImagePanelSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single ClpCollegeImagePanel model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id, $clp_id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
            'clp_id' => $clp_id
        ]);
    }

    /**
     * Creates a new ClpCollegeImagePanel model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate($college_id, $clp_id)
    {
        $model = ClpCollegeImagePanel::findOne(['college_id' => $college_id]);
        $clpModel = CustomLandingPage::find()->where(['id' => $clp_id])->one();

        if (!$model) {
            $model = new ClpCollegeImagePanel();
        }

        $request = Yii::$app->request->post();

        $model->college_id = $college_id;
        $oldImage = $model->logo_image;
        $oldBannerImage = $model->banner_image;

        if ($model->load($request)) {
            $timestamp = time();
            if (isset($model->logo_image)) {
                $logoImage = \yii\web\UploadedFile::getInstance($model, 'logo_image');
                if (isset($logoImage)) {
                    $logoImageName = $clpModel->slug . '_' . $timestamp . '_logo.' . $logoImage->getExtension();
                    $model->logo_image = $logoImageName;
                    $saveImage =  (new S3Service())->uploadFile(DataHelper::s3Path($logoImageName, 'clp_college_logo'), $logoImage->tempName);
                    if ($saveImage) {
                        $model->logo_image = $logoImageName;
                    }
                }
            }


            if (isset($model->banner_image_web) || isset($model->banner_image_wap)) {
                $bannerWeb = \yii\web\UploadedFile::getInstance($model, 'banner_image_web');
                $bannerWap = \yii\web\UploadedFile::getInstance($model, 'banner_image_wap');

                $webPath = $oldBannerImage[0]['web'] ?? '';
                $wapPath = $oldBannerImage[0]['wap'] ?? '';

                if ($bannerWeb) {
                    $webImageName = $clpModel->slug . '_' . $timestamp . '_web.' . $bannerWeb->getExtension();
                    $uploaded = (new S3Service())->uploadFile(DataHelper::s3Path($webImageName, 'clp_college_banner'), $bannerWeb->tempName);
                    if ($uploaded) {
                        $webPath = $webImageName;
                    }
                }

                if ($bannerWap) {
                    $wapImageName = $clpModel->slug . '_' . $timestamp . '_wap.' . $bannerWap->getExtension();
                    $uploaded = (new S3Service())->uploadFile(DataHelper::s3Path($wapImageName, 'clp_college_banner'), $bannerWap->tempName);
                    if ($uploaded) {
                        $wapPath = $wapImageName;
                    }
                }

                if (empty($webPath) && empty($wapPath)) {
                    $model->banner_image = null;
                } else {
                    $model->banner_image = [[
                        'web' => $webPath,
                        'wap' => $wapPath,
                    ]];
                }
            }

            if (empty($model->logo_image)) {
                $model->logo_image = $oldImage;
            }

            if (empty($webPath) && empty($wapPath)) {
                $oldWeb = $oldBannerImage[0]['web'] ?? '';
                $oldWap = $oldBannerImage[0]['wap'] ?? '';
                if (empty($oldWeb) && empty($oldWap)) {
                    $model->banner_image = null;
                } else {
                    // Retain previous values
                    $model->banner_image = $oldBannerImage;
                }
            }

            if ($model->save()) {
                return $this->redirect([
                    'custom-landing-page/view',
                    'id' => $clp_id,
                ]);
            }

            Yii::$app->session->setFlash('error', 'College has already been taken.');
        } else {
            return $this->render('create', [
                'model' => $model,
                'college_id' => $college_id,
                'clp_id' => $clp_id
            ]);
        }
    }

    /**
     * Updates an existing ClpCollegeImagePanel model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->id]);
        } else {
            return $this->render('update', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Deletes an existing ClpCollegeImagePanel model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Remove image from ClpCollegeImagePanel
     * @return array JSON response
     */
    public function actionRemoveImage()
    {
        \Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;

        $request = \Yii::$app->request;
        $id = $request->post('id');
        $imageType = $request->post('imageType');

        if (!$id || !in_array($imageType, ['logo', 'banner_web', 'banner_wap'])) {
            return ['success' => false, 'message' => 'Invalid parameters'];
        }

        $model = ClpCollegeImagePanel::findOne($id);

        if (!$model) {
            return ['success' => false, 'message' => 'Image panel not found'];
        }

        try {
            if ($imageType === 'logo') {
                $model->logo_image = null;
            } elseif (in_array($imageType, ['banner_web', 'banner_wap'])) {
                $banner = is_array($model->banner_image) ? $model->banner_image[0] : [];

                if ($imageType === 'banner_web') {
                    $banner['web'] = '';
                } else {
                    $banner['wap'] = '';
                }

                if (empty($banner['web']) && empty($banner['wap'])) {
                    $model->banner_image = null;
                } else {
                    $model->banner_image = [$banner];
                }
            }

            if ($model->save()) {
                return ['success' => true, 'message' => 'Image removed successfully'];
            } else {
                return ['success' => false, 'message' => 'Failed to save changes'];
            }
        } catch (\Exception $e) {
            return ['success' => false, 'message' => 'Error: ' . $e->getMessage()];
        }
    }

    /**
     * Finds the ClpCollegeImagePanel model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return ClpCollegeImagePanel the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = ClpCollegeImagePanel::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }
}
