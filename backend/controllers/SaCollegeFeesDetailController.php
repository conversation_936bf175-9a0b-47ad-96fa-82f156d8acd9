<?php

namespace backend\controllers;

use Yii;
use common\models\SaCollegeFeesDetail;
use backend\models\SaCollegeFeesDetailSearch;
use yii\base\Model;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * SaCollegeFeesDetailController implements the CRUD actions for SaCollegeFeesDetail model.
 */
class SaCollegeFeesDetailController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all SaCollegeFeesDetail models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new SaCollegeFeesDetailSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single SaCollegeFeesDetail model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new SaCollegeFeesDetail model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $request = Yii::$app->getRequest();
        $collegeId = $request->get('college_id');
        $models = SaCollegeFeesDetail::find()->where(['sa_college_id' => $collegeId])->all();

        if ($request->isPost && isset($request->post()['SaCollegeFeesDetail'])) {
            foreach (Yii::$app->request->post()['SaCollegeFeesDetail'] as $key => $value) {
                if (!empty($value['id'])) {
                    continue;
                } else {
                    $model = new SaCollegeFeesDetail();
                }
                $models[$key] = $model;
            }

            if (Model::loadMultiple($models, Yii::$app->request->post()) && Model::validateMultiple($models)) {
                foreach ($models as $model) {
                    // if (empty($model->fees)) {
                    //     Yii::$app->session->setFlash('success', 'Success');
                    // }
                    $model->created_by = $model->updated_by = Yii::$app->user->identity->id;
                    $model->sa_college_id = isset($_GET['college_id']) ? $_GET['college_id'] : $model->sa_college_id;

                    if (!$model->save()) {
                        Yii::error($model->getError());
                    }
                }

                Yii::$app->session->setFlash('success', 'Success');
                return $this->refresh();
            } else {
                return $this->render('create', [
                    'models' => $models,
                ]);
            }
        } else {
            return $this->render('create', [
                'models' => $models,
            ]);
        }
    }

    /**
     * Updates an existing SaCollegeFeesDetail model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);
        $model->updated_by = Yii::$app->user->identity->id;

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->id]);
        } else {
            return $this->render('update', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Deletes an existing SaCollegeFeesDetail model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the SaCollegeFeesDetail model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return SaCollegeFeesDetail the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = SaCollegeFeesDetail::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }
}
