<?php

namespace backend\controllers;

use backend\models\RecentActivity;
use backend\models\RecentActivityTracker;
use yii\web\NotFoundHttpException;
use Yii;
use common\helpers\DataHelper;
use common\helpers\CollegeHelper;
use common\helpers\CourseHelper;
use common\models\BoardPages;
use common\helpers\BoardHelper;
use yii\helpers\ArrayHelper;
use yii\db\Query;

class RecentActivityController extends \yii\web\Controller
{

    public function actionIndex()
    {
        $searchModel = new RecentActivity();
        $dataProvider = $searchModel->getRecentActivity(Yii::$app->request->queryParams);
        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    public function actionView($id)
    {
        $model = $this->findModel($id);
        return $this->render('view', [
            'model' => $model,
        ]);
    }

    public function actionCreate()
    {
        $model = new RecentActivity();
        $postRequest = Yii::$app->request->post();
        if ($model->load($postRequest)) {
            if ($postRequest['RecentActivity']['text'] == '') {
                Yii::$app->session->setFlash('error', 'Text field should not be blank');
                return $this->redirect('/recent-activity/create');
            }

            if (!empty($model->expiry)) {
                $model->expiry = date('Y-m-d H:i:s', strtotime($model->expiry . '23:59:59'));
            } else {
                $model->expiry = date('Y-m-d H:i:s', strtotime('tomorrow 23:59:59'));
            }
            $status = $model->status;
            if ($model->save()) {
                if ($status == 1) {
                    return $this->redirect(['/recent-activity/link', 'id' => $model->id]);
                } else {
                    Yii::$app->session->setFlash('success', 'Success');
                    return $this->redirect('/recent-activity/create');
                }
            }
        } else {
            return $this->render('create', [
                'model' => $model,
            ]);
        }
    }

    public function actionUpdate($id)
    {
        $model = $this->findModel($id);
        $postRequest = Yii::$app->request->post();
        if ($postRequest) {
            if ($postRequest['RecentActivity']['text'] == '') {
                Yii::$app->session->setFlash('error', 'Text field should not be blank');
                return $this->redirect(['/recent-activity/update', 'id' => $id]);
            }
            $id = (int)$id;

            if ($model) {
                if (!empty($postRequest['RecentActivity']['expiry'])) {
                    $date =  date('Y-m-d', strtotime($postRequest['RecentActivity']['expiry']));
                    $model->expiry = date('Y-m-d H:i:s', strtotime($date . '23:59:59'));
                } else {
                    $model->expiry = date('Y-m-d H:i:s', strtotime('tomorrow' . '23:59:59'));
                }
                $model->text = $postRequest['RecentActivity']['text'];
                $model->status = $postRequest['RecentActivity']['status'];
                $model->scheduled_at = $postRequest['RecentActivity']['scheduled_at'];
                $model->updated_at = date('Y-m-d H:i:s');
                if ($model->save()) {
                    Yii::$app->session->setFlash('success', 'Success');
                    return $this->redirect(['/recent-activity/update', 'id' => $id]);
                }
            } else {
                return $this->render('update', [
                    'model' => $model,
                ]);
            }
        } else {
            return $this->render('update', [
                'model' => $model,
            ]);
        }
    }

    public function actionLink($id)
    {
        $model = $this->findModel($id);
        if ($model->status == 0) {
            Yii::$app->session->setFlash('error', 'Please active the link!');
            return $this->redirect('/recent-activity/index');
        }
        $model_tracker = $this->findModelTracker($id);
        $postRequest = Yii::$app->request->post();

        if ($postRequest) {
            $bulkInsertArray = [];
            if (!isset($postRequest['RecentActivityTracker'])) {
                Yii::$app->session->setFlash('error', 'Please Link atleast one entity!');
                return $this->redirect(['/recent-activity/link', 'id' => $id]);
            }
            foreach ($postRequest['RecentActivityTracker'] as $entity) {
                if ($entity['entity'] == '') {
                    Yii::$app->session->setFlash('error', 'Entity field should not be empty');
                    return $this->redirect(['/recent-activity/link', 'id' => $id]);
                }
                if (is_array($entity['entity_id'])) {
                    foreach ($entity['entity_id'] as $entity_id) {
                        if (empty($entity_id)) {
                            Yii::$app->session->setFlash('error', 'Entity Name field should not be empty');
                            return $this->redirect(['/recent-activity/link', 'id' => $id]);
                        } else {
                            $entity_id = (int)$entity_id;
                        }
                        if (is_array($entity['page'])) {
                            foreach ($entity['page'] as $page) {
                                $bulkInsertArray[] = [
                                    'recent_activity_id' => $entity['recent_activity_id'],
                                    'entity' => (int)$entity['entity'],
                                    'entity_id' => $entity_id,
                                    'page' => empty($page) ? null : $page
                                ];
                            }
                        } else {
                            $bulkInsertArray[] = [
                                'recent_activity_id' => $entity['recent_activity_id'],
                                'entity' => (int)$entity['entity'],
                                'entity_id' => $entity_id,
                                'page' => empty($entity['page']) ? null : $entity['page']
                            ];
                        }
                        // if ($entity['entity'] == 3) {
                        //     $exam_tagged_entity = $this->examTaggedEntity($entity_id, $entity['recent_activity_id'], $entity['entity']);
                        //     $latest_update = array_merge($bulkInsertArray,$exam_tagged_entity);
                        // }
                    }
                } else {
                    if (empty($entity['entity_id'])) {
                        Yii::$app->session->setFlash('error', 'Entity Name field should not be empty');
                        return $this->redirect(['/recent-activity/link', 'id' => $id]);
                    } else {
                        $entity['entity_id'] = (int)$entity['entity_id'];
                    }
                    if (is_array($entity['page'])) {
                        foreach ($entity['page'] as $page) {
                            $bulkInsertArray[] = [
                                'recent_activity_id' => (int)$entity['recent_activity_id'],
                                'entity' => (int)$entity['entity'],
                                'entity_id' => $entity['entity_id'],
                                'page' => empty($page) ? null : $page
                            ];
                        }
                    } else {
                        $bulkInsertArray[] = [
                            'recent_activity_id' => (int)$entity['recent_activity_id'],
                            'entity' => (int)$entity['entity'],
                            'entity_id' => $entity['entity_id'],
                            'page' => empty($entity['page']) ? null : $entity['page']
                        ];
                    }
                    // if ($entity['entity'] == 3) {
                    //     $exam_tagged_entity = $this->examTaggedEntity($entity['entity_id'], $entity['recent_activity_id'], $entity['entity']);
                    //     $latest_update = array_merge($bulkInsertArray,$exam_tagged_entity);
                    // }
                }
            }
            RecentActivityTracker::deleteAll('recent_activity_id = :id', [':id' => $id]);
            $tableName = 'recent_activity_tracker';
            if (count($bulkInsertArray) > 0) {
                $columnNameArray = ['recent_activity_id', 'entity', 'entity_id', 'page'];
                $insertCount = Yii::$app->db->createCommand()
                    ->batchInsert(
                        $tableName,
                        $columnNameArray,
                        $bulkInsertArray
                    )
                    ->execute();
                if ($insertCount) {
                    Yii::$app->session->setFlash('success', 'Success');
                    return $this->redirect(['/recent-activity/link', 'id' => $id]);
                }
            }
        } else {
            $recentActivityTrackerData = RecentActivityTracker::find()->where(['recent_activity_id' => $id])->all();

            if (!empty($recentActivityTrackerData)) {
                $data = array_map(function ($list) {
                    $entityFieldsArr = [
                        'article' => ['title as name'],
                        'board' => ['display_name as name'],
                        'college' => ['name as name'],
                        'exam' => ['display_name as name'],
                        'course' => ['name as name'],
                    ];
                    $entity = ArrayHelper::getValue(RecentActivity::entityType(), $list->entity);
                    list($name) = $entityFieldsArr[$entity];
                    $model_dlt = '\common\models\\' . ucfirst($entity);
                    $entity_name = $model_dlt::find()->select([$name])->where(['id' => $list->entity_id])->one();
                    return [
                        'id' => $list->entity_id,
                        'name' => $entity_name->name,
                    ];
                }, $recentActivityTrackerData);

                $model_tracker->entity_name = $data ?? [];

                $sub_data = array_map(function ($list) {
                    return [
                        'id' => $list->page,
                        'name' => $list->page,
                    ];
                }, $recentActivityTrackerData);

                $model_tracker->subPage = $sub_data ?? [];
                return $this->render('recentActivityTracker', [
                    'recent_act_id' => $id,
                    'model' => $model_tracker,
                    'recentActivityTrackerData' => $recentActivityTrackerData,
                ]);
            } else {
                return $this->render('recentActivity', [
                    'recent_act_id' => $id,
                    'model' => $model_tracker,
                    'recentActivityTrackerData' => [],
                ]);
            }
        }
    }

    public function actionDelete($id)
    {
        $model_recent_activity = RecentActivity::findOne($id);
        if ($model_recent_activity->status == 1) {
            Yii::$app->session->setFlash('error', 'Unable to delete an active record');
            return $this->redirect('/recent-activity/index');
        }
        $model_recent_activity->is_trash = 1;
        if ($model_recent_activity->save()) {
            Yii::$app->session->setFlash('success', 'Success');
            return $this->redirect('/recent-activity/index');
        }
    }

    protected function findModelTracker($recent_activity_id)
    {
        $recent_activity_id = (int)$recent_activity_id;
        if (($model = RecentActivityTracker::find()->where(['recent_activity_id' => $recent_activity_id])->one()) !== null) {
            return $model;
        } else {
            $model = new RecentActivityTracker();
        }
    }

    protected function findModel($id)
    {
        if (($model = RecentActivity::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

    public function actionGetEntityName()
    {
        $entityFieldsArr = [
            'article' => ['id', 'title as name', 'title'],
            'board' => ['id', 'display_name as name', 'display_name'],
            'college' => ['id', 'name as name', 'name'],
            'exam' => ['id', 'display_name as name', 'display_name'],
            'course' => ['id', 'name as name', 'name'],
        ];
        $requestParam = Yii::$app->request->get('depdrop_parents');
        $requestParamQuery = Yii::$app->request->get('q');
        if ($requestParam != '') {
            $entity_info = RecentActivity::entityType();
            $entity = $entity_info[$requestParam];
            list($id, $name, $column) = $entityFieldsArr[$entity];

            $model = '\common\models\\' . ucfirst($entity);


            $data = $model::find()->select([$id, $name])
                ->where(['like', $column, '%' . $requestParamQuery . '%', false])
                ->andWhere(['status' => 1])
                ->all();

            $data = array_map(function ($list) {
                return [
                    'id' => $list['id'],
                    'text' => $list['name']
                ];
            }, $data);
            \Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
            $out['results'] = array_values($data);
            return $out;
        }
    }

    public function actionGetSubPage()
    {
        $allBoardSubpages =  ArrayHelper::map(BoardPages::find()->where(['status'=>BoardPages::STATUS_ACTIVE])->andWhere(['is', 'parent_id', new \yii\db\Expression('null')])->all(), 'slug', 'name');
        $entitySubPageArr = [
            'article' => '',
            'board' => $allBoardSubpages,
            'college' => CollegeHelper::$subPages,
            'exam' => DataHelper::examContentList(),
            'course' => CourseHelper::$subPages,
        ];

        $requestParam = Yii::$app->request->get('depdrop_parents');

        if ($requestParam != '') {
            $entity_info = RecentActivity::entityType();
            $entity = $entity_info[$requestParam];
            if ($entity == 'article') {
                return '';
            }

            $page = $entitySubPageArr[$entity];
            foreach ($page as $key => $val) {
                $subpage = ['books', 'sample-papers', 'reference-books', 'previous-years-papers', 'mock-sample-tests', 'images-videos', 'qna', 'reviews'];
                $data_exist = array_keys($subpage, $key);
                if (!empty($data_exist)) {
                    unset($page[$key]);
                }
            }
            $page = array_map(function ($key, $value) {
                return [
                    'id' => $key,
                    'text' => $value,
                ];
            }, array_keys($page), array_values($page));

            return json_encode(['results' => $page]);
        }
    }
    
    public function actionStatusChange()
    {
        $postRequest = Yii::$app->request->post();
        if ($postRequest) {
            $ids = json_encode($postRequest['data']);
            $ids = str_replace('[', '', $ids);
            $ids = str_replace(']', '', $ids);
            $ids = str_replace('ID', '', $ids);
            $ids = str_replace('"', '', $ids);
            $ids = explode(',', $ids);
            $criteria = ['in', 'id', $ids];
            if ($postRequest['id'] == 'active') {
                $update_active_rc =  RecentActivity::updateAll(['status' => RecentActivity::RECENT_ACTIVITY_STATUS_ACTIVE], $criteria);
                if ($update_active_rc) {
                    Yii::$app->session->setFlash('success', 'Success');
                    return $this->redirect('/recent-activity/index');
                } else {
                    Yii::$app->session->setFlash('error', 'Unable to active record');
                    return $this->redirect('/recent-activity/index');
                }
            } else if ($postRequest['id'] == 'inactive') {
                $update_inactive_rc =  RecentActivity::updateAll(['status' => RecentActivity::RECENT_ACTIVITY_STATUS_INACTIVE], $criteria);
                if ($update_inactive_rc) {
                    Yii::$app->session->setFlash('success', 'Success');
                    return $this->redirect('/recent-activity/index');
                } else {
                    Yii::$app->session->setFlash('error', 'Unable to in-active record');
                    return $this->redirect('/recent-activity/index');
                }
            } else if ($postRequest['id'] == 'delete') {
                $update_delete_rc =  RecentActivity::updateAll(['status' => RecentActivity::RECENT_ACTIVITY_STATUS_INACTIVE,'is_trash'=>RecentActivity::RECENT_ACTIVITY_STATUS_ACTIVE], $criteria);
                if ($update_delete_rc) {
                    Yii::$app->session->setFlash('success', 'Success');
                    return $this->redirect('/recent-activity/index');
                } else {
                    Yii::$app->session->setFlash('error', 'Unable to delete record');
                    return $this->redirect('/recent-activity/index');
                }
            }
        }
    }
}
