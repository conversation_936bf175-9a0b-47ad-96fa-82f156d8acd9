<?php

namespace backend\controllers;

use Yii;
use common\models\SponsorCollege;
use backend\models\SponsorCollegeSearch;
use common\models\College;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * SponsorCollegeController implements the CRUD actions for SponsorCollege model.
 */
class SponsorCollegeController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
            ],
        ];
    }

    /**
     * Lists all SponsorCollege models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new SponsorCollegeSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single SponsorCollege model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($college_id)
    {
        return $this->render('view', [
            'model' => $this->findModel($college_id),
        ]);
    }

    /**
     * Creates a new SponsorCollege model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new SponsorCollege();

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->id]);
        } else {
            return $this->render('create', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Updates an existing SponsorCollege model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($college_id)
    {
        $model = $this->findModel($college_id);

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->id]);
        } else {
            return $this->render('update', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Deletes an existing SponsorCollege model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
    public function actionDelete($college_id)
    {
        $this->findModel($college_id)->delete();

        return $this->redirect(['index']);
    }
     */

    /**
     * Finds the SponsorCollege model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return SponsorCollege the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($college_id)
    {
        if (($model = SponsorCollege::findOne(['college_id' => $college_id])) !== null) {
            return $model;
        } else {
            return false;
        }
    }

    public function actionGetTags()
    {
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
        $request = Yii::$app->request;
        $collegeId = $request->get('college-id');

        return $this->redirect('sponsor?&college-id=' . $collegeId);
    }

    public function actionSponsor()
    {
        $request = Yii::$app->request;
        $model = new SponsorCollege();
        if (empty($request->get('college-id'))) {
            throw new NotFoundHttpException();
        }

        $college = College::find()->where(['id' => $request->get('college-id')])->one();
        $models = SponsorCollege::find()->where(['college_id' => $college->id])->all();
        if ($request->isPost && isset($request->post()['SponsorCollege'])) {
            $this->deleteAllList($models);
            foreach ($request->post('SponsorCollege') as $sponsorCollege) {
                if ($this->saveSponsorTags($sponsorCollege)) {
                    Yii::$app->session->setFlash('success', 'Success');
                }
            }
            $college->sponser_college_view = $request->post('sponser_college_view');
            $college->save();
            Yii::$app->session->setFlash('success', 'Success');
            return $this->redirect('/sponsor-college/sponsor?college-id=' . $college->id);
        }

        return $this->render('sponsor', [
            'models' => $models,
            'college' => $college
        ]);
    }

    public function saveSponsorTags($data)
    {
        $model = new SponsorCollege();

        $model->college_id = $data['college_id'];
        $model->state = $data['state'] ?? null;
        $model->stream = $data['stream'] ?? null;
        $model->course = $data['course'] ?? null;
        $model->position = $data['position'];
        $model->status = $data['status'] ?? SponsorCollege::STATUS_INACTIVE;

        if ($model->save()) {
            return true;
        } else {
            print_r($model->getErrors());
            exit();
        }
    }

    public function deleteAllList($models)
    {
        if ($models) {
            foreach ($models as $model) {
                $model->delete($model->college_id);
            }
            return true;
        }

        return true;
    }
}
