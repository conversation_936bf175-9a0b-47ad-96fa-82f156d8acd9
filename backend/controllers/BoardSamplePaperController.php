<?php

namespace backend\controllers;

use Yii;
use common\models\BoardSamplePaper;
use backend\models\BoardSamplePaperSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\web\UploadedFile;
use common\services\S3Service;
use common\helpers\DataHelper;

/**
 * BoardSamplePaperController implements the CRUD actions for BoardSamplePaper model.
 */
class BoardSamplePaperController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all BoardSamplePaper models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new BoardSamplePaperSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single BoardSamplePaper model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new BoardSamplePaper model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new BoardSamplePaper();

        if ($model->load(Yii::$app->request->post())) {
            if (isset($model->cover_image)) {
                $coverImage = UploadedFile::getInstance($model, 'cover_image');
                if ($coverImage) {
                    $coverImageName = $model->board->slug . '-' . $model->subject_slug . '.' . $coverImage->extension;
                    $saveImage =  (new S3Service())->uploadFile(DataHelper::s3Path($coverImageName, 'board_sample_papers'), $coverImage->tempName);

                    if ($saveImage) {
                        $model->cover_image = $coverImageName;
                    }
                }
            }
            if ($model->save()) {
                return $this->redirect(['view', 'id' => $model->id]);
            }
            return $this->redirect(['view', 'id' => $model->id]);
        } else {
            return $this->render('create', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Updates an existing BoardSamplePaper model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post())) {
            if (empty($model->cover_image)) {
                unset($model->cover_image);
            }

            $coverImage = UploadedFile::getInstance($model, 'cover_image');

            if ($coverImage) {
                $coverImageName = $model->board->slug . '-' . $model->subject_slug . '.' . $coverImage->extension;
                $saveImage =  (new S3Service())->uploadFile(DataHelper::s3Path($coverImageName, 'board_sample_papers'), $coverImage->tempName);

                if ($saveImage) {
                    $model->cover_image = $coverImageName;
                }
            }

            if ($model->save()) {
                return $this->redirect(['view', 'id' => $model->id]);
            }
        } else {
            return $this->render('update', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Deletes an existing BoardSamplePaper model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    /*public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }*/

    /**
     * Finds the BoardSamplePaper model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return BoardSamplePaper the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = BoardSamplePaper::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }
}
