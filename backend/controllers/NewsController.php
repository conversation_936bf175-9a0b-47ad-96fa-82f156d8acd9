<?php

namespace backend\controllers;

use Yii;
use common\models\News;
use backend\models\NewsSearch;
use common\models\NewsContent;
use Carbon\Carbon;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\web\UploadedFile;
use common\services\S3Service;
use common\helpers\DataHelper;

/**
 * NewsController implements the CRUD actions for News model.
 */
class NewsController extends Controller
{

    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all News models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new NewsSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single News model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id)
    {
        $model = $this->findModel($id);
        if (!empty($model->audio)) {
            $model->audio =  DataHelper::s3Path($model->audio, 'news_audio', true);
        }
        return $this->render('view', [
            'model' => $model,
        ]);
    }

    /**
     * Creates a new News model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new News();
        $model->setScenario('create');
        $postRequest = Yii::$app->request->post();
        $currentTimestamp = Carbon::now()->toDateTimeString();
        $articleIds = [];
        $collegeIds = [];
        $examIds = [];
        $boardIds = [];
        $newsIds = [];
        $courseIds = [];
        $tagsIds = [];
        $translationIds = [];

        if (!empty($postRequest['News']['tags'])) {
            $tagsIds = $postRequest['News']['tags'];
        }
        if (!empty($postRequest['News']['news'])) {
            $newsIds = $postRequest['News']['news'];
        }
        if (!empty($postRequest['News']['article'])) {
            $articleIds = $postRequest['News']['article'];
        }
        if (!empty($postRequest['News']['college'])) {
            $collegeIds = $postRequest['News']['college'];
        }
        if (!empty($postRequest['News']['exam'])) {
            $examIds = $postRequest['News']['exam'];
        }

        if (!empty($postRequest['News']['board'])) {
            $boardIds = $postRequest['News']['board'];
        }
        if (!empty($postRequest['News']['course'])) {
            $courseIds = $postRequest['News']['course'];
        }
        if (!empty($postRequest['News']['translation'])) {
            $translationIds = $postRequest['News']['translation'];
        }

        if ($model->load($postRequest)) {
            $model->lang_code = empty($model->lang_code) ? News::DEFAULT_LANG_CODE : $model->lang_code;
            /**
             * Below code is used for audio file upload in the s3
             * <AUTHOR> Date: 04-04-2023
             */
            if (isset($model->audio)) {
                $newsAudio = \yii\web\UploadedFile::getInstance($model, 'audio');
                if (isset($newsAudio)) {
                    $newFileName = md5($currentTimestamp) . '.' . $newsAudio->getExtension();
                    $model->audio = $newFileName ?? '';
                    $s3 = new S3Service();
                    $saveAudio = $s3->uploadFile(DataHelper::s3Path($newFileName, 'news_audio'), $newsAudio->tempName);
                    if ($saveAudio) {
                        $model->audio = $newFileName ?? '';
                    }
                }
            }
            //end of audio file capture
            if (isset($model->banner_image)) {
                $bannerImage = UploadedFile::getInstance($model, 'banner_image');
                if (isset($bannerImage)) {
                    $newImageName = md5($currentTimestamp) . '.' . $bannerImage->getExtension();
                    $saveImage =  (new S3Service())->uploadFile(DataHelper::s3Path($newImageName, 'news_genral'), $bannerImage->tempName);
                    if ($saveImage) {
                        $model->banner_image = $newImageName;
                    }
                }
            }
            if ($model->save()) {
                $model->saveTags($tagsIds);
                $model->saveArticle($articleIds);
                $model->saveNews($newsIds);
                $model->saveCollege($collegeIds);
                $model->saveExam($examIds);
                $model->saveBoard($boardIds);
                $model->saveCourse($courseIds);
                $model->saveTranslation($translationIds);

                return $this->redirect(['view', 'id' => $model->id]);
            } else {
                print_r($model->getErrors());
            }
        } else {
            return $this->render('create', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Updates an existing News model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);
        $postRequest = Yii::$app->request->post();
        $currentTimestamp = Carbon::now()->toDateTimeString();
        $articleIds = [];
        $collegeIds = [];
        $examIds = [];
        $boardIds = [];
        $newsIds = [];
        $courseIds = [];
        $tagsIds = [];
        $translationIds = [];

        if (!empty($postRequest['News']['tags'])) {
            $tagsIds = $postRequest['News']['tags'];
        }

        if (!empty($postRequest['News']['article'])) {
            $articleIds = $postRequest['News']['article'];
        }
        if (!empty($postRequest['News']['news'])) {
            $newsIds = $postRequest['News']['news'];
        }
        if (!empty($postRequest['News']['college'])) {
            $collegeIds = $postRequest['News']['college'];
        }
        if (!empty($postRequest['News']['exam'])) {
            $examIds = $postRequest['News']['exam'];
        }

        if (!empty($postRequest['News']['board'])) {
            $boardIds = $postRequest['News']['board'];
        }
        if (!empty($postRequest['News']['course'])) {
            $courseIds = $postRequest['News']['course'];
        }
        if (!empty($postRequest['News']['translation'])) {
            $translationIds = $postRequest['News']['translation'];
        }

        $oldNewsImage = $model->banner_image;
        $oldNewsAudio = $model->audio;
        if ($model->load($postRequest)) {
            /**
             * Below code is used for audio file upload in the s3
             * <AUTHOR> Date: 04-04-2023
             */
            if (isset($model->audio)) {
                $newsAudio = \yii\web\UploadedFile::getInstance($model, 'audio');
                if (isset($newsAudio)) {
                    $newFileName = md5($currentTimestamp) . '.' . $newsAudio->getExtension();
                    $model->audio = $newFileName ?? '';
                    $s3 = new S3Service();
                    $saveAudio = $s3->uploadFile(DataHelper::s3Path($newFileName, 'news_audio'), $newsAudio->tempName);
                    if ($saveAudio) {
                        $model->audio = $newFileName ?? '';
                    }
                }
            }
            //end of audio file capture
            $bannerImage = UploadedFile::getInstance($model, 'banner_image');
            if (!empty($bannerImage)) {
                $newImageName = md5($currentTimestamp) . '.' . $bannerImage->getExtension();
                $saveImage =  (new S3Service())->uploadFile(DataHelper::s3Path($newImageName, 'news_genral'), $bannerImage->tempName);
                if ($saveImage) {
                    $model->banner_image = $newImageName;
                    $model->saveTags($tagsIds);
                    $model->saveArticle($articleIds);
                    $model->saveNews($newsIds);
                    $model->saveCollege($collegeIds);
                    $model->saveExam($examIds);
                    $model->saveBoard($boardIds);
                    $model->saveCourse($courseIds);
                    $model->saveTranslation($translationIds);
                    $model->save();

                    return $this->redirect(['view', 'id' => $model->id]);
                }
            } else {
                $model->banner_image = $oldNewsImage ?? '';
                if (empty($model->audio)) {
                    $model->audio = $oldNewsAudio ?? '';
                }
            }
            $model->saveTags($tagsIds);
            $model->saveArticle($articleIds);
            $model->saveNews($newsIds);
            $model->saveCollege($collegeIds);
            $model->saveExam($examIds);
            $model->saveBoard($boardIds);
            $model->saveCourse($courseIds);
            $model->saveTranslation($translationIds);

            $model->save();

            return $this->redirect(['view', 'id' => $model->id]);
        } else {
            if (!empty($model->audio)) {
                $model->audio =  DataHelper::s3Path($model->audio, 'news_audio', true);
            }
            return $this->render('update', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Deletes an existing News model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    /*public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }*/

    /**
     * Finds the News model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return News the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = News::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

    /**
     * Displays a single NewsContent value for preview.
     * @param integer $id
     * @return mixed
     */
    public function actionPreview($id)
    {
        $modelContent = NewsContent::find()->where(['news_id'=>$id])->one();
        if (!empty($modelContent)) {
            return $this->render('preview', [
                'model' => $modelContent,
            ]);
        } else {
            throw new NotFoundHttpException('News Content not found');
        }
    }
}
