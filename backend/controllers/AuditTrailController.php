<?php

namespace backend\controllers;

use Yii;
use common\models\AuditTrail;
use common\models\User;
use backend\models\AuditTrailSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\db\Query;
use common\helpers\DataHelper;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Diff\Differ;
use yii\data\ArrayDataProvider;

/**
 * AuditTrailController implements the CRUD actions for AuditTrail model.
 */
class AuditTrailController extends Controller
{
    public static $model_column = [
        'news_content' => 'news_id',
        'exam_content' => 'exam_id',
        'board_content' => 'board_id',
        'college_content' => 'entity_id',
        'course_content' => 'course_id',
        'career_content' => 'career_id',
        'news_content_subdomain' => 'news_id',
    ];

    public static $page_column = [
        'exam_content' => 'exam_content.name as page',
        'board_content' => 'board_content.page as page',
        'college_content' => 'college_content.sub_page as page',
        'course_content' => 'course_content.page as page',
        'career_content' => 'career_content.page as page'
    ];

    public static $entityFieldsArr = [
        'article' => ['article.title as name', 'article.title'],
        'news' => ['news.name as name', 'news.name'],
        'news_subdomain' => ['news_subdomain.name as name', 'news_subdomain.name'],
        'board' => ['board.display_name as name', 'board.display_name'],
        'college' => ['college.name as name', 'college.name'],
        'exam' => ['exam.display_name as name', 'exam.display_name'],
        'course' => ['course.name as name', 'course.name'],
        'career' => ['career.name as name', 'career.name'],
        'ncert_articles' => ['ncert_articles.title as name', 'ncert_articles.title'],
        'gmu_meta_category' => ['gmu_meta_category.category_name as name', 'gmu_meta_category.category_name'],
    ];

    public static $status = [
        '0' => 'In Active',
        '1' => 'Active',
    ];

    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all AuditTrail models.
     * @return mixed
     */
    public function actionIndex()
    {
        $getRequest = Yii::$app->request->get();

        if (!empty($getRequest)) {
            $model = $getRequest['entity'];
            $model_id = DataHelper::$audit_entity[$model];
            $model_info = $model;
            $model_name = str_replace(' ', '', ucwords(str_replace('_', ' ', $model_id)));

            if ($model == 'ncert') {
                $model = 'ncert_articles';
                $model_id = 'ncert_articles';
                $model_name = 'NcertArticles';
            }

            $dbName = Yii::$app->db->createCommand('SELECT DATABASE()')->queryScalar();

            $subQuery = (new \yii\db\Query())
                ->select(['model_id', 'MAX(created) AS max_created'])
                ->from('gmu_log.audit_trail')
                ->where([
                    'or',
                    ['and', ['like', 'model', $model_name], ['field' => 'content']],
                    ['field' => 'description']
                ])
                ->groupBy('model_id');

            $query = new Query();

            $query->innerJoin(
                ['latest' => $subQuery],
                'audit_trail.model_id = latest.model_id AND audit_trail.created = latest.max_created'
            );

            $query->from('gmu_log.audit_trail')
                ->innerJoin($dbName . '.' . $model_id, $model_id . '.id = audit_trail.model_id');
            if ($model_id == 'college') {
                $query->innerJoin($dbName . '.sponsor_college', 'sponsor_college.college_id = audit_trail.model_id');
            }
            if (strpos($model_id, 'content') !== false) {
                $model_clm = self::$model_column[$model_id];
                $query->innerJoin($dbName . '.' . $model, $model . '.id = ' . $model_id . '.' . $model_clm);
                if ($model_id !== 'news_content' && $model_id !== 'news_content_subdomain') {
                    array_push(self::$entityFieldsArr[$model], self::$page_column[$model_id]);
                }
            }

            array_push(self::$entityFieldsArr[$model], 'audit_trail.id');
            array_push(self::$entityFieldsArr[$model], 'audit_trail.model_id');
            array_push(self::$entityFieldsArr[$model], 'audit_trail.old_value');
            array_push(self::$entityFieldsArr[$model], 'audit_trail.new_value');
            array_push(self::$entityFieldsArr[$model], 'audit_trail.field');
            array_push(self::$entityFieldsArr[$model], 'audit_trail.user_id');
            array_push(self::$entityFieldsArr[$model], 'audit_trail.created');
            array_push(self::$entityFieldsArr[$model], 'user.name as user_name');

            $query->innerJoin($dbName . '.user', 'user.id = audit_trail.user_id')
                ->select(self::$entityFieldsArr[$model])
                ->where(['like', 'model', $model_name]);

            if ($model_id == 'college') {
                $query->orWhere(['like', 'model', 'SponsorCollege']);
            }

            $query->andWhere(['or', ['field' => 'content'], ['field' => 'description']]);

            $audit_trail = $query->orderBy(['audit_trail.id' => SORT_DESC])
                ->limit(100)
                ->all();

            $outputArray = [];
            $page = '';
            foreach ($audit_trail as $item) {
                if (isset($item['page']) && !empty($item['page'])) {
                    $key = $item['name'] . '|' . $item['user_name'] . '|' . $item['page'];
                    $page = $item['page'];
                } else {
                    $key = $item['name'] . '|' . $item['user_name'];
                }

                if (!isset($outputArray[$key])) {
                    $outputArray[$key] = [
                        'name' => $item['name'],
                        'entity' => $model,
                        'entity_id' => $item['model_id'],
                        'user_name' => $item['user_name'],
                        'page' => $page,
                        'audit' => []
                    ];
                }

                $outputArray[$key]['audit'][] = ['created' => $item['created']];
            }

            $searchModel = new AuditTrailSearch();
            $dataProvider = $searchModel->search(Yii::$app->request->queryParams, $outputArray);

            return $this->render('index', [
                'model' => new AuditTrail(),
                'searchModel' => $searchModel,
                'dataProvider' => $dataProvider,
                'model_info' => $model_info
            ]);
        } else {
            return $this->render('index', [
                'searchModel' => new AuditTrailSearch(),
                'model' => new AuditTrail(),
                'model_info' => ''
            ]);
        }
    }

    /**
     * Displays a single AuditTrail model.
     * @param integer $id
     * @return mixed
     */
    public function actionView()
    {
        $postRequest = Yii::$app->request->get();

        if (!empty($postRequest) && Yii::$app->request->isGet) {
            $model = $postRequest['entity'];
            $entity_id = $postRequest['id'];
            $model_id = DataHelper::$audit_entity[$model];
            $model_name = str_replace(' ', '', ucwords(str_replace('_', ' ', $model_id)));

            if ($model == 'ncert') {
                $model = 'ncert_articles';
                $model_id = 'ncert_articles';
                $model_name = 'NcertArticles';
            }

            $dbName = Yii::$app->db->createCommand('SELECT DATABASE()')->queryScalar();

            $query = new Query();
            $query->from('gmu_log.audit_trail')
                ->innerJoin($dbName . '.' . $model_id, $model_id . '.id = audit_trail.model_id');
            if ($model_id == 'college') {
                $query->innerJoin($dbName . '.sponsor_college', 'sponsor_college.college_id = audit_trail.model_id');
            }
            if (strpos($model_id, 'content') !== false) {
                $model_clm = self::$model_column[$model_id];
                $query->innerJoin($dbName . '.' . $model, $model . '.id = ' . $model_id . '.' . $model_clm);
            }

            $query->select(array_merge(self::$entityFieldsArr[$model], ['audit_trail.id', 'audit_trail.model_id as entity_id', 'audit_trail.created']))
                ->where(['like', 'model', $model_name])
                ->andWhere(['model_id' => $entity_id]);

            if ($model_id == 'college') {
                $query->orWhere(['like', 'model', 'SponsorCollege']);
            }

            $audit_trail = $query->andWhere(['or', ['field' => 'content'], ['field' => 'description']])
                ->orderBy(['audit_trail.id' => SORT_DESC])
                ->limit(50)
                ->all();

            $entity_name = '';

            if (!empty($audit_trail)) {
                $entity_name = $audit_trail[0]['name'];
            }

            $dataProvider = new ArrayDataProvider([
                'allModels' => $audit_trail,
                'pagination' => [
                    'pageSize' => 20,
                    'totalCount' => count($audit_trail),
                ],
            ]);

            return $this->render('view', [
                'dataProvider' => $dataProvider,
                'model_info' => $model,
                'entity_name' => $entity_name,
                'model' => new AuditTrail()
            ]);
        } else {
            return $this->render('view', [
                'model' => new AuditTrail(),
                'model_info' => '',
            ]);
        }
    }

    /**
     * Finds the AuditTrail model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return AuditTrail the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = AuditTrail::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

    public function actionAuthorDetails()
    {
        $requestParam = Yii::$app->request->post('depdrop_parents');
        if ($requestParam[0] == 'articles') {
            $table_name = 'article';
        } else if ($requestParam[0] == 'ncert') {
            $table_name = 'ncert_articles';
        } else if ($requestParam[0] == 'news_subdomain') {
            $table_name = 'news_content_subdomain';
        } else {
            $table_name = $requestParam[0] . '_content';
        }

        $query = new Query();
        $author_info = $query
            ->select(['user.id', 'user.name'])
            ->from([$table_name])
            ->innerJoin('user', 'user.id = ' . $table_name . '.author_id')
            ->distinct('user.id')
            ->all();


        $data = array_map(function ($list) {
            return [
                'id' => $list['id'],
                'name' => $list['name']
            ];
        }, $author_info);

        \Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
        return (['output' => $data]);
    }

    public function actionDiff($id)
    {
        $model = new AuditTrail();
        $model->load(Yii::$app->request->post());
        $entity = Yii::$app->request->get('entity');

        $startDate = $model->start_date;
        $endDate = $model->end_date;

        $query = new Query();
        $query->select(['old_value', 'new_value', 'created'])
            ->from('gmu_log.audit_trail')
            ->where(['model_id' => $id])
            ->andWhere(['or', ['field' => 'content'], ['field' => 'description']]);

        if ($startDate && $endDate) {
            $query->andWhere(['created' => [$startDate, $endDate]])
                ->orderBy(['created' => SORT_DESC]);
            $result = $query->limit(2)->all();
            if (!empty($result)) {
                $oldContent = $result[1]['new_value'] ?? '';
                $newContent = $result[0]['new_value'] ?? '';
            }
        } else {
            $query->orderBy(['created' => SORT_DESC])->limit(1);
            $result = $query->one();
            if (!empty($result)) {
                $oldContent = $result['old_value'] ?? '';
                $newContent = $result['new_value'] ?? '';
            }
        }

        if (!$result) {
            Yii::$app->session->setFlash('error', 'No audit trail entries found for the selected conditions.');
            return $this->redirect(['audit-trail/view', 'id' => $id, 'entity' => $entity]);
        }

        $differ = new Differ();
        $diff = $differ->diff($oldContent, $newContent);

        return $this->render('diff', [
            'model' => $model,
            'diff' => html_entity_decode($diff),
            'id' => $id
        ]);
    }

    /**
     * Displays a single log model.
     * @param integer $id
     * @return mixed
     */
    public function actionLogDetail($id)
    {
        $audit = $this->findModel($id);
        $stats = [];

        if (!empty($audit)) {
            $user = User::find()->select(['name', 'id'])->where(['id' => $audit->user_id])->one();
            $old_value = trim((string) $audit->old_value);
            $new_value = trim((string) $audit->new_value);

            // Always normalize new_value
            $new_words = $new_value === '' ? [] : preg_split('/\s+/', strtolower($new_value));
            $new_words = array_filter($new_words);

            // If old_value is empty, don't compute removed words
            if ($old_value === '') {
                $removed = [];
            } else {
                $old_words = preg_split('/\s+/', strtolower($old_value));
                $old_words = array_filter($old_words);
                $removed = array_diff($old_words, $new_words);
            }

            // Added words are always computed
            $added = array_diff($new_words, $old_words ?? []);

            // Set stats
            $stats['removed_words'] = $removed;
            $stats['user_name'] = !empty($user) ? $user->name : '';
            $stats['added_words'] = $added;
            $stats['removed_count'] = count($removed);
            $stats['added_count'] = count($added);
            $stats['new_word_total'] = count($new_words);
        }

        return $this->render('log_detail', [
            'model' => $audit,
            'stats' => $stats
        ]);
    }
}
