<?php

namespace backend\controllers;

use Yii;
use common\models\CourseProgramDates;
use backend\models\CourseProgramDatesSearch;
use common\helpers\DataHelper;
use common\models\College;
use common\models\CollegeCourseContent;
use common\models\CollegeProgram;
use yii\base\Model;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * CourseProgramDatesController implements the CRUD actions for CourseProgramDates model.
 */
class CourseProgramDatesController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    public function actionHome()
    {
        $searchModel = new CourseProgramDatesSearch();
        $params = Yii::$app->request->queryParams;
        $dataProvider = $searchModel->home($params);

        return $this->render('home', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
            'entity_type' => $params['entity_type'] == 1 ? 'CI' : 'PI',
            'college' => College::findOne($params['college_id']),
            'params' => $params
        ]);
    }

    /**
     * Lists all CourseProgramDates models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new CourseProgramDatesSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single CourseProgramDates model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new CourseProgramDates model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new CourseProgramDates();

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->id]);
        } else {
            return $this->render('create', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Updates an existing CourseProgramDates model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->id]);
        } else {
            return $this->render('update', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Deletes an existing CourseProgramDates model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the CourseProgramDates model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return CourseProgramDates the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = CourseProgramDates::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

    public function actionAddDates($entityType, $entityId)
    {
        $models = $this->getCourseProgramModels($entityType, $entityId);

        if (Yii::$app->request->isPost) {
            if (Model::loadMultiple($models, Yii::$app->request->post())) {
                foreach ($models as $model) {
                    $existingModel = CourseProgramDates::findOne(['id' => $model->id]);

                    // If both start & end dates are removed, update status to inactive
                    if (empty($model->start) && empty($model->end)) {
                        if ($existingModel) {
                            $existingModel->status = CourseProgramDates::STATUS_INACTIVE;
                            if (!$existingModel->save(false)) {
                                Yii::$app->session->setFlash('error', 'Error updating status: ' . json_encode($existingModel->getErrors()));
                            }
                        }
                        continue; // Skip saving the model as dates are empty
                    }
                    
                    if (!$model->save()) {
                        Yii::$app->session->setFlash('error', 'Error saving: ' . json_encode($model->getErrors()));
                        return $this->render('_tabular-form-dates', [
                            'models' => $models,
                            'entityType' => $entityType,
                            'entityId' => $entityId,
                            'college' => self::getCollegeMapping($entityType, $entityId)
                        ]);
                    }
                }
                Yii::$app->session->setFlash('success', 'Dates added successfully');
                return $this->refresh();
            }
        }

        return $this->render(
            '_tabular-form-dates',
            [
                'models' => $models,
                'entityType' => $entityType,
                'entityId' => $entityId,
                'college' => self::getCollegeMapping($entityType, $entityId)
            ]
        );
    }


    private function getCollegeMapping($entityType, $entityId)
    {
        $mapping = [
            CourseProgramDates::ENTITY_TYPE_CI => CollegeCourseContent::class,
            CourseProgramDates::ENTITY_TYPE_PI => CollegeProgram::class,
        ];

        return isset($mapping[$entityType]) ? $mapping[$entityType]::findOne($entityId) ?? [] : [];
    }


    private function getCourseProgramModels($entityType, $entityId)
    {
        $existingModels = CourseProgramDates::find()->where(['entity_type' => $entityType, 'entity_id' => $entityId])->indexBy('slug')->all();

        $models = $existingModels;

        foreach (DataHelper::examDateList() as $slug => $name) {
            if (!isset($existingModels[$slug])) {
                $models[$slug] = new CourseProgramDates([
                    'entity_type' => $entityType,
                    'entity_id' => $entityId,
                    'name' => $name,
                    'slug' => $slug,
                ]);
            }
        }

        return $models;
    }
}
