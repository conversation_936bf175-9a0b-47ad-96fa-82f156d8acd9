<?php

namespace backend\controllers;

use Yii;
use common\models\City;
use backend\models\CitySearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\web\UploadedFile;
use common\services\S3Service;
use common\helpers\DataHelper;

/**
 * CityController implements the CRUD actions for City model.
 */
class CityController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all City models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new CitySearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single City model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new City model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new City();
        $postRequest = Yii::$app->request->post();

        if ($model->load($postRequest) && $model->save()) {
            if (empty($model->image)) {
                unset($model->image);
            }
            if ($model->save()) {
                $image = UploadedFile::getInstance($model, 'image');
                if (isset($image)) {
                    $imageName = $model->slug . '.' . $image->extension;
                    $saveLogo =  (new S3Service())->uploadFile(DataHelper::s3Path($imageName, 'city'), $image->tempName);
                    if ($saveLogo) {
                        $model->image = $imageName;
                    }
                }

                if ($model->save()) {
                    return $this->redirect(['view', 'id' => $model->id]);
                }
            }
            return $this->redirect(['view', 'id' => $model->id]);
        } else {
            return $this->render('create', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Updates an existing City model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);
        $postRequest = Yii::$app->request->post();

        if ($model->load($postRequest)) {
            if (empty($model->image)) {
                unset($model->image);
            }
            if ($model->save()) {
                $image = UploadedFile::getInstance($model, 'image');
                if (isset($image)) {
                    $imageName = $model->slug . '.' . $image->extension;
                    $saveLogo =  (new S3Service())->uploadFile(DataHelper::s3Path($imageName, 'city'), $image->tempName);
                    if ($saveLogo) {
                        $model->image = $imageName;
                    }
                }

                if ($model->save()) {
                    return $this->redirect(['view', 'id' => $model->id]);
                }
            }
            return $this->redirect(['view', 'id' => $model->id]);
        } else {
            return $this->render('update', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Deletes an existing City model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $model = $this->findModel($id);
        $model->status = City::STATUS_INACTIVE;
        $model->save();

        return $this->redirect(['index']);
    }

    /**
     * Finds the City model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return City the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = City::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }
}
