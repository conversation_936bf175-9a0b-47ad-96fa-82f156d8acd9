<?php

namespace backend\controllers;

use backend\models\NewsSubdomainLiveUpdateSearch;
use Yii;
use common\models\NewsSubdomainLiveUpdate;
use common\models\NewsSubdomain;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * NewsSubdomainLiveUpdateController implements the CRUD actions for NewsSubdomainLiveUpdate model.
 */
class NewsSubdomainLiveUpdateController extends Controller
{
    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all NewsSubdomainLiveUpdate models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new NewsSubdomainLiveUpdateSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        $dataProvider->sort->defaultOrder = ['id' => SORT_DESC];
        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single NewsSubdomainLiveUpdate model.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new NewsSubdomainLiveUpdate model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new NewsSubdomainLiveUpdate();
        $news = NewsSubdomain::findOne(['id' => Yii::$app->request->get('news_id')]);
        $model->news_id = !empty($news) ? $news->id : '';
        $model->published_at = ($model->status == 1) ? null : $model->scheduled_at;
        $model->scheduled_at = ($model->status == 1) ? null : $model->scheduled_at;
        $model->expired_at = ($model->status == 1) ? null : $model->expired_at;

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->id]);
        }

        return $this->render('create', [
            'model' => $model,
            'news' => $news
        ]);
    }

    /**
     * Updates an existing NewsSubdomainLiveUpdate model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post())) {
            $model->scheduled_at = ($model->status == 1) ? null : $model->scheduled_at;
            $model->expired_at = ($model->status == 1) ? null : $model->expired_at;
            if ($model->save()) {
                return $this->redirect(['view', 'id' => $model->id]);
            }
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * Deletes an existing NewsSubdomainLiveUpdate model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    /*public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }
*/
    /**
     * Finds the NewsSubdomainLiveUpdate model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return NewsSubdomainLiveUpdate the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = NewsSubdomainLiveUpdate::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
