<?php

namespace backend\controllers;

use Yii;
use common\models\CollegePiSubpageContent;
use backend\models\CollegePiSubpageContentSearch;
use common\helpers\DataHelper;
use common\models\CollegeProgram;
use yii\base\Model;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * CollegePiSubpageContentController implements the CRUD actions for CollegePiSubpageContent model.
 */
class CollegePiSubpageContentController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all CollegePiSubpageContent models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new CollegePiSubpageContentSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single CollegePiSubpageContent model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new CollegePiSubpageContent model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new CollegePiSubpageContent();

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->id]);
        } else {
            return $this->render('create', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Updates an existing CollegePiSubpageContent model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->id]);
        } else {
            return $this->render('update', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Deletes an existing CollegePiSubpageContent model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the CollegePiSubpageContent model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return CollegePiSubpageContent the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = CollegePiSubpageContent::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

    public function actionAddPiSubpageContent($college_program_id)
    {
        $models = $this->getSubpageItems($college_program_id);
        $request = Yii::$app->request;

        if ($request->isPost) {
            $postData = Yii::$app->request->post('CollegePiSubpageContent', []);

            foreach ($postData as $key => $data) {
                if (empty($data['content'])) {
                    continue;
                }

                // Normalize subpage key (convert 'cut-off-1', 'cut-off-2', etc., to 'cut-off')
                $normalizedKey = preg_replace('/^cut-off-\d+$/', 'cut-off', $key);

                // Find existing model or create a new one
                $query = CollegePiSubpageContent::find()
                    ->where([
                        'college_program_id' => $college_program_id,
                        'subpage' => $normalizedKey
                    ]);

                if (!empty($data['year'])) {
                    $query->andWhere(['year' => $data['year']]);
                }

                $model = $query->one();

                if (!$model) {
                    $model = new CollegePiSubpageContent();
                }

                // Set attributes from form data
                $model->setAttributes([
                    'college_program_id' => $college_program_id,
                    'subpage' => $normalizedKey, // Store only 'cut-off'
                    'content' => $data['content'] ?? null,
                    'year' => $data['year'] ?? null,
                    'h1' => $data['h1'] ?? null,
                    'meta_title' => $data['meta_title'] ?? null,
                    'meta_description' => $data['meta_description'] ?? null,
                    'status' => $data['status'] ?? null,
                ]);

                if (!$model->save()) {
                    Yii::$app->session->setFlash('error', 'Error saving subpage: ' . json_encode($model->getErrors()));
                    return $this->render('add-pi-subpage-content', [
                        'models' => $models,
                        'college_program' => $this->getCollegeProgramContent($college_program_id),
                    ]);
                }
            }

            Yii::$app->session->setFlash('success', 'Subpage content added successfully.');
            return $this->refresh();
        } else {
            return $this->render('add-pi-subpage-content', [
                'models' => $models,
                'college_program' => $this->getCollegeProgramContent($college_program_id),
            ]);
        }
    }

    protected function findSubpageContentModels($college_program_id)
    {
        $models = CollegePiSubpageContent::find()->where(['college_program_id' => $college_program_id])->all();
        return !empty($models) ? $models : [];
    }

    protected function getCollegeProgramContent($college_program_id)
    {
        $model = CollegeProgram::findOne($college_program_id);
        if ($model !== null) {
            return $model;
        }
        throw new NotFoundHttpException('The requested page does not exist.');
    }

    private function getSubpageItems($college_program_id)
    {
            $items = [];
            $defaultSubpages = array_column(DataHelper::$courseCiPiSubpageItems, 'id');
    
            // Fetch all existing models for the given college_course_content_id
            $existingModels = CollegePiSubpageContent::find()
                ->where(['college_program_id' => $college_program_id])
                ->all();
    
        foreach ($defaultSubpages as $subpage) {
            // If the subpage is 'cut-off', handle multiple entries
            if ($subpage === 'cut-off') {
                $cutOffModels = [];
    
                foreach ($existingModels as $model) {
                    if (preg_match('/^cut-off(-\d+)?$/', $model->subpage)) {
                        $cutOffModels[] = $model;
                    }
                }
    
                if (!empty($cutOffModels)) {
                    foreach ($cutOffModels as $index => $model) {
                        $key = $index === 0 ? 'cut-off' : "cut-off-$index"; // No -0, just "cut-off"
                        $items[$key] = $model;
                    }
                } else {
                    // If no cut-off records exist, create a default entry
                    $item = new CollegePiSubpageContent();
                    $item->setAttributes([
                        'college_program_id' => $college_program_id,
                        'subpage' => 'cut-off'
                    ]);
                    $items['cut-off'] = $item;
                }
            } else {
                // Find the existing model for the subpage
                $model = null;
                foreach ($existingModels as $m) {
                    if ($m->subpage === $subpage) {
                        $model = $m;
                        break;
                    }
                }
    
                if ($model) {
                    $items[$subpage] = $model;
                } else {
                    $item = new CollegePiSubpageContent();
                    $item->setAttributes([
                        'college_program_id' => $college_program_id,
                        'subpage' => $subpage
                    ]);
                    $items[$subpage] = $item;
                }
            }
        }
    
            return $items;
    }
}
