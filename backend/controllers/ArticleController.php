<?php

namespace backend\controllers;

use Yii;
use common\models\Article;
use backend\models\ArticleSearch;
use Carbon\Carbon;
use common\helpers\BoardHelper;
use common\services\AzureService;
use common\services\S3Service;
use common\helpers\DataHelper;
use common\models\CollegeContent;
use yii\db\Query;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\web\UploadedFile;
use yii\filters\VerbFilter;
use yii\helpers\Inflector;
use common\models\AuditTrail;
use frontend\services\ArticleService;
use frontend\helpers\Url;
use common\models\ArticleTranslationCld;
use common\models\Board;
use common\models\BoardContent;
use common\models\Faq;
use common\models\Exam;
use common\models\ExamContent;
use Exception;
use GuzzleHttp\Client as GuzzleHttpClient;
use DomDocument;
use yii\helpers\ArrayHelper;
use common\models\BoardPages;

/**
 * ArticleController implements the CRUD actions for Article model.
 */
class ArticleController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all Article models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new ArticleSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single Article model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id)
    {
        $model = $this->findModel($id);
        if (!empty($model->audio)) {
            $model->audio =  DataHelper::s3Path($model->audio, 'article_audio', true);
        }
        return $this->render('view', [
            'model' => $model,
        ]);
    }

    /**
     * Creates a new Article model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new Article();
        $postRequest = Yii::$app->request->post();

        /**** Check for cache and restricted urls in content ****/
        if (!empty($postRequest['Article']['description'])) {
            $restrictedUrl = DataHelper::checkRestrictedUrl($postRequest['Article']['description']);
        }
        if (!empty($restrictedUrl)) {
            return $this->redirect(Yii::$app->request->referrer);
        }
        /*********************************************************/

        if (!empty($postRequest['autoSaveId'])) {
            $model = $this->updateAutoSave($postRequest['autoSaveId']);
            return $this->redirect(['view', 'id' => $model->id]);
        }
        if (isset($postRequest['Article']['slug_title'])) {
            $slug_title = explode(' ', $postRequest['Article']['slug_title']);
            $model->slug = Inflector::slug(implode('-', $slug_title));
        }
        $newsIds = [];
        $collegeIds = [];
        $examIds = [];
        $boardIds = [];
        $articleIds = [];
        $courseIds = [];
        $translationIds = [];

        if (!empty($postRequest['Article']['article'])) {
            $articleIds = $postRequest['Article']['article'];
        }
        if (!empty($postRequest['Article']['news'])) {
            $newsIds = $postRequest['Article']['news'];
        }
        if (!empty($postRequest['Article']['college'])) {
            $collegeIds = $postRequest['Article']['college'];
        }
        if (!empty($postRequest['Article']['exam'])) {
            $examIds = $postRequest['Article']['exam'];
        }
        if (!empty($postRequest['Article']['board'])) {
            $boardIds = $postRequest['Article']['board'];
        }
        if (!empty($postRequest['Article']['course'])) {
            $courseIds = $postRequest['Article']['course'];
        }
        if (!empty($postRequest['Article']['translation'])) {
            $translationIds = $postRequest['Article']['translation'];
        }
        if (!empty($postRequest['Article']['bucket_id'])) {
            $model->bucket_id = $postRequest['Article']['bucket_id'];
        }

        if ($model->load($postRequest)) {
            $model->lang_code = empty($model->lang_code) ? 1 : $model->lang_code;
            $model->updated_by = Yii::$app->user->identity->id;
            /**
             * Below code is used for audio file upload in the s3
             * <AUTHOR> Date: 3-04-2023
             */
            if (isset($model->audio)) {
                $articleAudio = \yii\web\UploadedFile::getInstance($model, 'audio');
                if (isset($articleAudio)) {
                    $newFileName = $model->entity . '-' . md5(uniqid(rand(), true)) . '.' . $articleAudio->getExtension();
                    $model->audio = $newFileName ?? '';
                    $s3 = new S3Service();
                    $saveAudio = $s3->uploadFile(DataHelper::s3Path($newFileName, 'article_audio'), $articleAudio->tempName);
                    if ($saveAudio) {
                        $model->audio = $newFileName ?? '';
                    }
                }
            }
            //end of audio file capture
            if (isset($model->cover_image)) {
                $articleImage = \yii\web\UploadedFile::getInstance($model, 'cover_image');
                if (isset($articleImage)) {
                    $articleImageName = $model->entity . '-' . md5(uniqid(rand(), true)) . '.' . $articleImage->getExtension();
                    $model->cover_image = $articleImageName;
                    if ($model->entity != 'study-abroad') {
                        $saveImage =  (new S3Service())->uploadFile(DataHelper::s3Path($articleImageName, 'article_general'), $articleImage->tempName);
                    } else {
                        $saveImage =  (new S3Service())->uploadFile(DataHelper::s3Path($articleImageName, 'article_study_abroad'), $articleImage->tempName);
                    }
                    if ($saveImage) {
                        $model->cover_image = $articleImageName;
                    }
                }

                if ($model->status == Article::STATUS_ACTIVE) {
                    $model->published_at = Carbon::now();
                }
                if ($model->save()) {
                    $model->saveArticle($articleIds);
                    $model->saveNews($newsIds);
                    $model->saveCollege($collegeIds);
                    $model->saveExam($examIds);
                    $model->saveBoard($boardIds);
                    $model->saveCourse($courseIds);
                    $model->saveTranslation($translationIds);

                    if (!empty($postRequest['Article']['college']) && $postRequest['Article']['status'] == 1) {
                        ArticleService::createCollegeContentArticle($postRequest['Article']['college']);
                    }

                    return $this->redirect(['view', 'id' => $model->id]);
                } else {
                    print_r($model->getErrors());
                }
            }
        } else {
            return $this->render('create', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Updates an existing Article model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);
        $postRequest = Yii::$app->request->post();

        /**** Check for cache and restricted urls in content ****/
        if (!empty($postRequest['Article']['description'])) {
            $restrictedUrl = DataHelper::checkRestrictedUrl($postRequest['Article']['description']);
        }
        if (!empty($restrictedUrl)) {
            return $this->redirect(Yii::$app->request->referrer);
        }
        /*********************************************************/

        $newsIds = [];
        $collegeIds = [];
        $examIds = [];
        $boardIds = [];
        $articleIds = [];
        $courseIds = [];
        $translationIds = [];

        if (!empty($postRequest['Article']['news'])) {
            $newsIds = $postRequest['Article']['news'];
        }
        if (!empty($postRequest['Article']['college'])) {
            $collegeIds = $postRequest['Article']['college'];
        }
        if (!empty($postRequest['Article']['exam'])) {
            $examIds = $postRequest['Article']['exam'];
        }

        if (!empty($postRequest['Article']['board'])) {
            $boardIds = $postRequest['Article']['board'];
        }

        if (!empty($postRequest['Article']['article'])) {
            $articleIds = $postRequest['Article']['article'];
        }

        if (!empty($postRequest['Article']['course'])) {
            $courseIds = $postRequest['Article']['course'];
        }
        if (!empty($postRequest['Article']['translation'])) {
            $translationIds = $postRequest['Article']['translation'];
        }
        if (!empty($postRequest['Article']['bucket_id'])) {
            $model->bucket_id = $postRequest['Article']['bucket_id'];
        }

        $oldArticleImage = $model->cover_image;
        $oldArticleAudio = $model->audio;
        if ($model->load($postRequest)) {
            $model->updated_by = Yii::$app->user->identity->id;
            /**
             * Below code is used for audio file upload in the s3
             * <AUTHOR> Date: 3-04-2023
             */
            if (isset($postRequest['Article']['slug_title'])) {
                $slug_title = explode(' ', $postRequest['Article']['slug_title']);
                $model->slug = Inflector::slug(implode('-', $slug_title));
            }
            if (isset($model->audio)) {
                $articleAudio = \yii\web\UploadedFile::getInstance($model, 'audio');
                if (isset($articleAudio)) {
                    $newFileName = $model->slug . '_' . time() . '.' . $articleAudio->getExtension();
                    $model->audio = $newFileName ?? '';
                    $s3 = new S3Service();
                    $saveAudio = $s3->uploadFile(DataHelper::s3Path($newFileName, 'article_audio'), $articleAudio->tempName);
                    if ($saveAudio) {
                        $model->audio = $newFileName ?? '';
                    }
                }
            }
            //end of audio file capture
            if (isset($model->cover_image)) {
                $articleImage = \yii\web\UploadedFile::getInstance($model, 'cover_image');
                if (isset($articleImage)) {
                    $newFileName = $model->slug . '_' . time() . '.' . $articleImage->getExtension();
                    $model->cover_image = $newFileName;
                    if ($model->entity != 'study-abroad') {
                        $saveImage =  (new S3Service())->uploadFile(DataHelper::s3Path($newFileName, 'article_general'), $articleImage->tempName);
                    } else {
                        $saveImage =  (new S3Service())->uploadFile(DataHelper::s3Path($newFileName, 'article_study_abroad'), $articleImage->tempName);
                    }
                    if ($saveImage) {
                        $model->cover_image = $newFileName;
                        $model->saveArticle($articleIds);
                        if (Yii::$app->user->can('Seo-Tag-Permission')) {
                            $model->saveNews($newsIds);
                            $model->saveCollege($collegeIds);
                            $model->saveExam($examIds);
                            $model->saveBoard($boardIds);
                            $model->saveCourse($courseIds);
                            $model->saveTranslation($translationIds);
                        }
                        if ($model->status == Article::STATUS_ACTIVE && $model->published_at == null) {
                            $model->published_at = Carbon::now();
                        }
                        $model->save();
                        return $this->redirect(['view', 'id' => $model->id]);
                    }
                }
            }
            $model->cover_image = $oldArticleImage;
            if (empty($model->audio)) {
                $model->audio = $oldArticleAudio;
            }
            $model->saveArticle($articleIds);
            if (Yii::$app->user->can('Seo-Tag-Permission')) {
                $model->saveNews($newsIds);
                $model->saveCollege($collegeIds);
                $model->saveExam($examIds);
                $model->saveBoard($boardIds);
                $model->saveCourse($courseIds);
                $model->saveTranslation($translationIds);
            }

            if ($model->status == Article::STATUS_ACTIVE && $model->published_at == null) {
                $model->published_at = Carbon::now();
                ;
            }
            if ($model->save()) {
                return $this->redirect(['view', 'id' => $model->id]);
            } else {
                // Handle validation errors
                $errors = $model->errors;
                if (!empty($errors)) {
                    // Convert errors to a single message
                    $errorMessage = '';
                    foreach ($errors as $attributeErrors) {
                        $errorMessage .= implode(' ', $attributeErrors) . ' ';
                    }
                    Yii::$app->session->setFlash('error', trim($errorMessage));
                } else {
                    Yii::$app->session->setFlash('error', 'Failed to save record due to unknown error.');
                }
                return $this->redirect(['update', 'id' => $model->id]);
            }
        } else {
            if (!empty($model->audio)) {
                $model->audio =  DataHelper::s3Path($model->audio, 'article_audio', true);
            }
            $model->slug_title = $model->slug;
            return $this->render('update', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Deletes an existing Article model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    // public function actionDelete($id)
    // {
    //     $this->findModel($id)->delete();

    //     return $this->redirect(['index']);
    // }

    /**
     * Uploads Image
     */
    public function actionUpload($id)
    {
        $model = $this->findModel($id);
        print_r($model);
        exit();
        if (Yii::$app->request->isPost) {
            $model->cover_image = UploadedFile::getInstance($model, 'cover_image');
            if ($model->upload()) {
                return $this->redirect(['view', 'id' => $model->id]);
            }
        }

        return $this->render('upload', ['model' => $model]);
    }

    /**
     * Finds the Article model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return Article the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = Article::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

    protected function updateAutoSave($id)
    {
        $model = $this->findModel($id);
        $postRequest = Yii::$app->request->post();
        $newsIds = [];
        $collegeIds = [];
        $examIds = [];
        $boardIds = [];
        $articleIds = [];
        $courseIds = [];
        $translationIds = [];

        if (!empty($postRequest['Article']['news'])) {
            $newsIds = $postRequest['Article']['news'];
        }
        if (!empty($postRequest['Article']['college'])) {
            $collegeIds = $postRequest['Article']['college'];
        }
        if (!empty($postRequest['Article']['exam'])) {
            $examIds = $postRequest['Article']['exam'];
        }

        if (!empty($postRequest['Article']['board'])) {
            $boardIds = $postRequest['Article']['board'];
        }

        if (!empty($postRequest['Article']['article'])) {
            $articleIds = $postRequest['Article']['article'];
        }

        if (!empty($postRequest['Article']['course'])) {
            $courseIds = $postRequest['Article']['course'];
        }
        if (!empty($postRequest['Article']['translation'])) {
            $translationIds = $postRequest['Article']['translation'];
        }

        $oldArticleImage = $model->cover_image;
        $oldArticleAudio = $model->audio;
        if ($model->load($postRequest)) {
            /**
             * Below code is used for audio file upload in the s3
             * <AUTHOR> Date: 3-04-2023
             */
            if (isset($postRequest['Article']['slug_title'])) {
                $slug_title = explode(' ', $postRequest['Article']['slug_title']);
                $model->slug = Inflector::slug(implode('-', $slug_title));
            }
            if (isset($model->audio)) {
                $articleAudio = \yii\web\UploadedFile::getInstance($model, 'audio');
                if (isset($articleAudio)) {
                    $newFileName = $model->slug . '.' . $articleAudio->getExtension();
                    $model->audio = $newFileName ?? '';
                    $s3 = new S3Service();
                    $saveAudio = $s3->uploadFile(DataHelper::s3Path($newFileName, 'article_audio'), $articleAudio->tempName);
                    if ($saveAudio) {
                        $model->audio = $newFileName ?? '';
                    }
                }
            }
            //end of audio file capture
            if (isset($model->cover_image)) {
                $articleImage = \yii\web\UploadedFile::getInstance($model, 'cover_image');
                if (isset($articleImage)) {
                    $newFileName = $model->slug . '.' . $articleImage->getExtension();
                    $model->cover_image = $newFileName;
                    if ($model->entity != 'study-abroad') {
                        $saveImage =  (new S3Service())->uploadFile(DataHelper::s3Path($newFileName, 'article_general'), $articleImage->tempName);
                    } else {
                        $saveImage =  (new S3Service())->uploadFile(DataHelper::s3Path($newFileName, 'article_study_abroad'), $articleImage->tempName);
                    }
                    if ($saveImage) {
                        $model->cover_image = $newFileName;
                        $model->saveArticle($articleIds);
                        if (Yii::$app->user->can('Seo-Tag-Permission')) {
                            $model->saveNews($newsIds);
                            $model->saveCollege($collegeIds);
                            $model->saveExam($examIds);
                            $model->saveBoard($boardIds);
                            $model->saveCourse($courseIds);
                            $model->saveTranslation($translationIds);
                        }
                        $model->save();
                        return $model;
                    }
                }
            }
            $model->cover_image = $oldArticleImage;
            if (empty($model->audio)) {
                $model->audio = $oldArticleAudio;
            }
            $model->saveArticle($articleIds);
            if (Yii::$app->user->can('Seo-Tag-Permission')) {
                $model->saveNews($newsIds);
                $model->saveCollege($collegeIds);
                $model->saveExam($examIds);
                $model->saveBoard($boardIds);
                $model->saveCourse($courseIds);
                $model->saveTranslation($translationIds);
            }

            if ($model->save()) {
                return $this->redirect(['view', 'id' => $model->id]);
            } else {
                // Handle validation errors
                $errors = $model->errors;
                if (!empty($errors)) {
                    // Convert errors to a single message
                    $errorMessage = '';
                    foreach ($errors as $attributeErrors) {
                        $errorMessage .= implode(' ', $attributeErrors) . ' ';
                    }
                    Yii::$app->session->setFlash('error', trim($errorMessage));
                } else {
                    Yii::$app->session->setFlash('error', 'Failed to save record due to unknown error.');
                }
                return $this->redirect(['update', 'id' => $model->id]);
            }
        } else {
            if (!empty($model->audio)) {
                $model->audio =  DataHelper::s3Path($model->audio, 'article_audio', true);
            }
            $model->slug_title = $model->slug;

            return $model;
        }
    }

    /**
     * Displays a single ArticleContent value for preview.
     * @param integer $id
     * @return mixed
     */
    public function actionPreview($id)
    {
        return $this->render('preview', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Displays a single ArticleLogs value.
     * @param integer $id
     * @return mixed
     */
    public function actionLogs($id)
    {
        $tableNameArr = $this->getDbDetails();
        return $this->render('logs', [
            'model' => $this->findModel($id),
            'data' => $this->getArticleLogData($id, $tableNameArr)
        ]);
    }

    /**
     * Get Audit Trail and GMU DB details.
     * @return array
     */

    protected function getDbDetails(): array
    {

        $auditTrailDbObj        = AuditTrail::getDb();
        $gmuDbObj               = Yii::$app->get('db');

        $auditTrailDbArr        = explode(';', $auditTrailDbObj->dsn);
        $gmuDbArr               = explode(';', $gmuDbObj->dsn);

        $auditTrailDbNameArr    = explode('=', $auditTrailDbArr[1]);
        $gmuDbNameArr           = explode('=', $gmuDbArr[1]);


        return [
            $auditTrailDbNameArr[1] . '.audit_trail',
            $gmuDbNameArr[1] . '.user'
        ];
    }

    /**
     * Get Article Log Data with Username.
     * @param integer $id
     * @param array $tableNameArr
     * @return array
     */
    protected function getArticleLogData(int $id, array $tableNameArr, string $model = 'Article'): array
    {
        $query = new Query();
        $query->select(['username', 'created', 'field', 'new_value'])->from($tableNameArr[0])
            ->where(['like', 'model', $model])->andWhere([
                'model_id' => $id,
                'action' => 'UPDATE'
            ]);
        $query->innerJoin($tableNameArr[1], 'user.id = audit_trail.user_id');
        return $query->all();
    }

    public function actionExport()
    {
        // Create a temporary file
        $tempFile = tempnam(sys_get_temp_dir(), 'csv');

        $query = Article::find();
        $query->joinWith(['backendauthor']);


        // Set headers for CSV download
        Yii::$app->response->format = \yii\web\Response::FORMAT_RAW;
        Yii::$app->response->headers->set('Content-Type', 'text/csv');
        Yii::$app->response->headers->set('Content-Disposition', 'attachment;filename="export.csv"');
        Yii::$app->response->headers->set('Cache-Control', 'no-store, no-cache, must-revalidate, max-age=0');
        Yii::$app->response->headers->set('Cache-Control', 'post-check=0, pre-check=0');
        Yii::$app->response->headers->set('Pragma', 'public');
        // Open the temporary file for writing
        $output = fopen($tempFile, 'w');


        // Define column names dynamically (adjust if needed)
        $columns = ['id', 'h1', 'title', 'slug', 'author', 'category_id', 'status', 'updated_at'];

        // Set the selected columns
        $query->select(['article.id', 'h1', 'title', 'article.slug', 'author_id', 'category_id', 'article.status', 'article.updated_at']);

        if (!empty(Yii::$app->request->queryParams['ArticleSearch'])) {
            $filterParam = Yii::$app->request->queryParams['ArticleSearch'];

            $query->andFilterWhere([
                'category_id' => $filterParam['category_id'],
                'is_popular' => $filterParam['is_popular'],
                'article.status' => $filterParam['status'],
            ]);

            $query->andFilterWhere(['like', 'title', $filterParam['title']])
                ->andFilterWhere(['like', 'article.slug', $filterParam['slug']])
                ->andFilterWhere(['like', 'h1', $filterParam['h1']])
                ->andFilterWhere(['like', 'lang_code', $filterParam['lang_code']])
                ->andFilterWhere(['like', 'user.name', $filterParam['article_name']]);

            $query->andFilterWhere(['DATE(article.published_at)' => $filterParam['published_at']])
                ->andFilterWhere(['DATE(article.updated_at)' => $filterParam['updated_at']]);
        } else {
            Yii::$app->session->setFlash('error', 'Please select any filter to export file.');
            return Yii::$app->response->redirect(['article/index']);
        }
        $query->orderBy(['article.updated_at' => SORT_DESC]);
        // Write column headers to CSV
        fputcsv($output, $columns);

        // Fetch data in chunks
        $pageSize = 1000; // Number of records per chunk
        $offset = 0;

        do {
            $models = $query->offset($offset)->limit($pageSize)->all();

            // Stop if no more data
            if (empty($models)) {
                break;
            }

            // Write each record to the CSV
            foreach ($models as $model) {
                $row = [];
                foreach ($columns as $column) {
                    if ($column == 'author') {
                        $row[] = $model->backendauthor->username ?? null;
                    } else {
                        $row[] = $model->$column;
                    }
                }
                fputcsv($output, $row);
            }

            // Increment offset for the next chunk
            $offset += $pageSize;
        } while (count($models) > 0);

        // Close the memory stream
        fclose($output);

        // Send the file to the browser
        return Yii::$app->response->sendFile($tempFile, 'data.csv', [
            'mimeType' => 'text/csv',
            'inline' => false, // Set to true if you want the file to be displayed inline
        ])->on(\yii\web\Response::EVENT_AFTER_SEND, function () use ($tempFile) {
            // Delete the temporary file after sending
            @unlink($tempFile);
            Yii::$app->response->redirect(['article/index']);
        });
    }
    public function actionTranslate()
    {

        $data = Yii::$app->request->get();
         $result = [];
        if (Yii::$app->request->isAjax) {
            \Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
            $postData =  Yii::$app->request->post();
            $article = $this->findModel($postData['article_id']);
            if (!empty($article)) {
                $lang_code = array_search($article->lang_code, DataHelper::$languageCode);
                $checkTranslation = ArticleTranslationCld::find()->where(['article_id'=>$article->id])
                ->andWhere(['translation_status'=>0])
                ->andWhere(['lang_code'=> 'hi'])
                ->one();
                if (!empty($checkTranslation)) {
                        $result['status'] = 'success';
                        $result['message'] = 'Request send successfully. Please wait for some time';
                        $result['transaction_id'] =  $checkTranslation->transaction_id;
                        return $result;
                } else {
                       $returnData =  $this->getArticleTranslationCLD($article);
                       return $returnData;
                }
            } else {
                return $this->render('translate', [
                    'model' => $this->findModel($data['id']),
                ]);
            }
        }
        return $this->render('translate', [
            'model' => $this->findModel($data['id']),
        ]);
    }

    public function getArticleTranslationCLD($article)
    {
        $translationLang = ['hi'];
        $translation_array['payload']['anchor_tag_hrefs'] = [];
        $lang_code = array_search($article->lang_code, DataHelper::$languageCode);
        $articleFaq = Faq::find()->where(['entity_id'=>$article->id])->andWhere(['entity'=>'articles'])->one();
        if (!empty($articleFaq->qnas)) {
            $i=5;
            foreach ($articleFaq->qnas as $qnas) {
                $j=0;
               // echo "<pre>"; print_r($articleFaq->qnas); die;
                foreach ($qnas as $faq) {
                    if ($j==0) {
                        $faqTranslate[$i] =
                            ['label' => 'qns_' . $i, 'input'=> $qnas['question']];
                    } else {
                        $faqTranslate[$i] = ['label' => 'ans_' . $i, 'input'=> $qnas['answer']];
                    }
                    $j++;
                    $i++;
                }
            }
        }
        
        foreach ($translationLang as $langCode) {
            $translation_array = [];
            $data_array = [
                ['label' => 'h1', 'input' => $article->h1],
                ['label' => 'title', 'input' => $article->title],
                ['label' => 'meta_description', 'input' => $article->meta_description],
                ['label' => 'description', 'input' => $article->description],
                ['label' => 'meta_title', 'input' => $article->meta_title]
            ];
            $dom = new DomDocument();
            $dom->loadHTML($article->description);
            $outputExam = [];
            $outputArticles = [];
            $outputBoards = [];
            foreach ($dom->getElementsByTagName('a') as $item) {
                if (strpos($item->getAttribute('href'), 'https://www.getmyuni.com/articles') !==  false) {
                    $outputArticles[] =  [
                        'str' => $dom->saveHTML($item),
                        'href' => $item->getAttribute('href'),
                        'anchorText' => $item->nodeValue
                    ];
                }
                
                if (strpos($item->getAttribute('href'), 'https://www.getmyuni.com/exams')!==false) {
                    $outputExam[] =  [
                        'str' => $dom->saveHTML($item),
                        'href' => $item->getAttribute('href'),
                        'anchorText' => $item->nodeValue
                    ];
                }

                if (strpos($item->getAttribute('href'), 'https://www.getmyuni.com/boards')!==false) {
                    $outputBoards[] =  [
                        'str' => $dom->saveHTML($item),
                        'href' => $item->getAttribute('href'),
                        'anchorText' => $item->nodeValue
                    ];
                }
                if (strpos($item->getAttribute('href'), 'https://www.getmyuni.com')!==false) {
                    $endString = substr($item->getAttribute('href'), strrpos($item->getAttribute('href'), '/')+1);
                    if ($endString=='b') {
                        $outputBoards[] =  [
                            'str' => $dom->saveHTML($item),
                            'href' => $item->getAttribute('href'),
                            'anchorText' => $item->nodeValue
                        ];
                    }
                }
            }

            if (!empty($outputExam)) {
                foreach ($outputExam as $examLink) {
                    $examSubPages = DataHelper::$examSeoSubPages;
                    $examPages  =  DataHelper::getExamDropDownSubPages();
                    $isPage=0;
                    $examSlug = substr($examLink['href'], strrpos($examLink['href'], '/') + 1);
                    $examPageSlug ='';
                    $examSubpageContentSlug ='';
                    $urlAttach ='';
                    foreach ($examPages as $key => $page) {
                        if (strpos($examSlug, $key) !== false) {
                            $examPageSlug =  str_replace('-' . $key, '', $examSlug);
                            $examSubpageContentSlug = $key;
                            $isPage=1;
                        }
                    }
                    if ($isPage==0) {
                        foreach ($examSubPages as $key => $page) {
                            if (strpos($examSlug, $page) !== false) {
                                $examPageSlug =  str_replace('-' . $page, '', $examSlug);
                                $examSubpageContentSlug  = $page;
                            }
                        }
                    }
                     $urlAttach = $examSlug;
                    $checkExamTranslate = Exam::find()->where(['slug'=>$examPageSlug])
                                          ->andWhere(['status'=>Exam::STATUS_ACTIVE])
                                          ->one();
                    if (!empty($checkExamTranslate)) {
                        $checkExamContent = ExamContent::find()->where(['exam_id'=>$checkExamTranslate->id])
                                             ->andWhere(['slug'=>$examSubpageContentSlug])
                                             ->andWhere(['lang_code'=>DataHelper::$languageCode['hi']])
                                             ->andWhere(['status'=>ExamContent::STATUS_ACTIVE])
                                             ->one();
                        if ($checkExamContent) {
                            $translation_array['payload']['anchor_tag_hrefs'][$examLink['href']] =  'https://www.getmyuni.com/hi/exams/' . $urlAttach;
                        }
                    }
                }
            }
            

            if (!empty($outputBoards)) {
                foreach ($outputBoards as $boardLink) {
                    $allBoardSubpages =  ArrayHelper::map(BoardPages::find()->where(['status'=>BoardPages::STATUS_ACTIVE])->andWhere(['is', 'parent_id', new \yii\db\Expression('null')])->all(), 'slug', 'name');
                    $boardSubPages = BoardHelper::$boardDefaultSeoInfo;
                    $boardPages  =  $allBoardSubpages;
                    $isPage=0;
                    $urlAttach ='';
                    $boardSlug = substr($boardLink['href'], strrpos($boardLink['href'], '/') + 1);
                    if ($boardSlug=='b') {
                        $boardUrlArray = explode('/', $boardLink['href']);
                        $boardSlug = $boardUrlArray[count($boardUrlArray)-2];
                    }
                    $boardPageSlug ='';
                    $boardubpageContentSlug ='';
                    foreach ($boardPages as $key => $page) {
                        if (strpos($boardSlug, $key) !== false) {
                            $boardPageSlug =  str_replace('-' . $key, '', $boardSlug);
                            $boardubpageContentSlug = $key;
                             $isPage=1;
                        }
                    }
                    if ($isPage==0) {
                        foreach ($boardSubPages as $key => $page) {
                            if (strpos($examSlug, $key) !== false) {
                                 $boardPageSlug =  str_replace('-' . $page, '', $examSlug);
                                 $boardubpageContentSlug  = $key;
                            }
                        }
                    }
                       $urlAttach = $boardSlug;
                      $checkBoardTranslate = Board::find()->where(['slug'=>$boardPageSlug])
                                            ->andWhere(['status'=>Board::STATUS_ACTIVE])
                                            ->one();
                    if (!empty($checkBoardTranslate)) {
                        $checkBoardContent = BoardContent::find()->where(['board_id'=>$checkBoardTranslate->id])
                                             ->andWhere(['page_slug'=>$boardubpageContentSlug])
                                             ->andWhere(['lang_code'=>DataHelper::$languageCode['hi']])
                                             ->andWhere(['status'=>ExamContent::STATUS_ACTIVE])
                                             ->one();
                        if ($checkBoardContent) {
                            $translation_array['payload']['anchor_tag_hrefs'][$boardLink['href']] =  'https://www.getmyuni.com/hi/boards/' . $urlAttach;
                        }
                    }
                }
            }

            if (!empty($outputArticles)) {
                foreach ($outputArticles as $articleLink) {
                    $articleSlug = substr($articleLink['href'], strrpos($articleLink['href'], '/') + 1);
                    $checkArticleTranslate = Article::find()->where(['slug'=>$articleSlug])
                                          ->andWhere(['lang_code'=>Article::LANG_HINDI])
                                          ->andWhere(['status'=>Article::STATUS_ACTIVE])
                                          ->one();
                    if (!empty($checkArticleTranslate)) {
                        $translation_array['payload']['anchor_tag_hrefs'][$articleLink['href']] =  'https://www.getmyuni.com/hi/articles/' . $articleSlug;
                    }
                }
            }
            
           
            //foreach()
            if (!empty($faqTranslate)) {
                $data_array = array_merge($data_array, $faqTranslate);
               // echo "<pre>"; print_r( $data_array); die;
            }
            $translation_array['payload']['target_lang'] = $langCode;
            $translation_array['payload']['data'] = $data_array;
            $translation_array['callback_url'] =  Article::CALLBACK_URL;
            //$translation_array['callback_auth_token'] =  'Bearer -IShkyGtWUmsLNbYriJy9q_G68XmCM8E';
            $translation_array['callback_headers'] = ['Content-Type' => 'application/json', 'Authorization' => 'Bearer -IShkyGtWUmsLNbYriJy9q_G68XmCM8E'];
            $request = new GuzzleHttpClient();
            $response = $request->post(Article::TRANSLATION_URL, [
                'headers' => [
                    'Authorization' => Article::AUTHORIZATION_KEY,
                    'accept' => 'application/json',
                    'content-type' => 'application/json',
                ],
                'body' =>
                json_encode(
                    $translation_array
                )
            ]);

            try {
                $response = json_decode($response->getBody());
                if (isset($response->transaction_id) && !empty($response->transaction_id)) {
                    $articleCld = ArticleTranslationCld::find()
                        ->where(['article_id' => $article->id])
                        ->andWhere(['lang_code' => $langCode])
                        ->one();
                    $checkArticleExits = Article::find()->where(['slug'=>$article->slug])->andWhere(['lang_code'=>Article::LANG_HINDI])->one();
                    $userID = \Yii::$app->user->getId();

                    if (!empty($articleCld)) {
                        if (empty($articleCld->translation_article_id) && !empty($checkArticleExits)) {
                            $articleCld->translation_article_id = $checkArticleExits->id;
                        }
                        $articleCld->transaction_id = $response->transaction_id;
                        $articleCld->translate_author_id  = $userID;
                        $articleCld->translation_status  = 0;
                        $articleCld->update();
                    } else {
                        $articleCld = new ArticleTranslationCld();
                        $articleCld->transaction_id = $response->transaction_id;
                        $articleCld->article_id = $article->id;
                        $articleCld->translate_author_id  = $userID;
                        $articleCld->lang_code = $langCode;
                        $articleCld->created_at = $article->created_at;
                        $articleCld->updated_at = $article->updated_at;
                        $articleCld->translation_status  = 0;
                        $articleCld->save();
                    }
                    $result['status'] = 'success';
                    $result['message'] = 'Request send successfully. Please wait for some time';
                    $result['transaction_id'] =  $response->transaction_id;
                } else {
                    $result['status'] = 'fail';
                    $result['message'] = 'Request failed. Please wait for some time';
                    $result['transaction_id'] =  '';
                }
                return $result;
            } catch (Exception $e) {
                print_r($e->getMessage());
                return false;
            }
            return true;
        }
    }
   
    public function actionCheckTranslate()
    {
        $data = Yii::$app->request->post();
        if (Yii::$app->request->isAjax) {
            \Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
            if (!empty($data['transaction_id'])) {
                 $checkTranslation = ArticleTranslationCld::find()->where(['transaction_id'=>$data['transaction_id']])
                                 ->andWhere(['translation_status'=>1])
                                 ->one();
                if (!empty($checkTranslation)) {
                    return ['checkStatus'=>1,'status'=>true,'message'=>'Article translate successfully <a  target="_blank "href="/article/preview?id=' . $checkTranslation->translation_article_id . '">preview</a>'];
                } else {
                    return ['status'=>false,'message'=>'','checkStatus'=>2];
                }
            } else {
                return ['status'=>false,'message'=>'something went wrong , please try again','checkStatus'=>3];
            }
        }
    }
}
