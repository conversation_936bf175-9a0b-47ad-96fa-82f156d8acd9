<?php

namespace backend\controllers;

use Yii;
use common\models\BoardPages;
use common\models\BoardPagesSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * BoardPagesController implements the CRUD actions for BoardPages model.
 */
class BoardPagesController extends Controller
{
    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all BoardPages models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new BoardPagesSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single BoardPages model.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView($id)
    {
       
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new BoardPages model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new BoardPages();
        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->id]);
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }

    /**
     * Updates an existing BoardPages model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);
        $data = BoardPages::find()->select(['id', 'name'])
        ->where(['status' => BoardPages::STATUS_ACTIVE])
        ->andWhere(['is', 'parent_id', new \yii\db\Expression('null')])
        ->all();
        $data = array_map(function ($list) {
            return [
                'id' => $list['id'],
                'text' => $list['name'] ?? $list['title']
            ];
        }, $data);
        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->id]);
        }
        
        return $this->render('update', [
            'model' => $model,
            'data'=>$data
        ]);
    }

    /**
     * Deletes an existing BoardPages model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the BoardPages model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return BoardPages the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = BoardPages::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }

    public function actionGetPageList()
    {
        $requestParamQuery = Yii::$app->request->get('q');
        $column = 'name';
                $data = BoardPages::find()->select(['id', 'name'])
                    ->where(['like', $column, '%' . $requestParamQuery . '%', false])
                    ->andWhere(['status' => 1])
                    ->andWhere(['is', 'parent_id', new \yii\db\Expression('null')])
                    ->all();
                
                $data = array_map(function ($list) {
                    return [
                        'id' => $list['id'],
                        'text' => $list['name'] ?? $list['title']
                    ];
                }, $data);
            
        if (!empty($data)) {
            \Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
            $out['results'] = array_values($data);
            return $out;
        } else {
            return false;
        }
    }
}
