<?php

namespace backend\controllers;

use Yii;
use common\models\SaCountryDetail;
use backend\models\SaCountryDetailSearch;
use common\helpers\DataHelper;
use common\services\AzureService;
use common\services\S3Service;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\web\UploadedFile;

/**
 * SaCountryDetailController implements the CRUD actions for SaCountryDetail model.
 */
class SaCountryDetailController extends Controller
{
    protected $azureService;

    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    public function __construct($id, $module, AzureService $azureService, $config = [])
    {
        $this->azureService = $azureService;
        parent::__construct($id, $module, $config);
    }

    /**
     * Lists all SaCountryDetail models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new SaCountryDetailSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single SaCountryDetail model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new SaCountryDetail model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new SaCountryDetail();
        $postRequest = Yii::$app->request->post();

        if ($model->load($postRequest)) {
            if (isset($model->cover_image)) {
                $coverImage = UploadedFile::getInstance($model, 'cover_image');
                if (isset($coverImage)) {
                    $newImageName = $model->slug;
                    $saveImage =  (new S3Service())->uploadFile(DataHelper::s3Path($newImageName . '.' . $coverImage->extension, 'sa_college_genral'), $coverImage->tempName);
                    if ($saveImage) {
                        $model->cover_image = $newImageName . '.' . $coverImage->extension;
                    }
                }
            }
            $model->created_by = $model->updated_by = Yii::$app->user->identity->id;
            $model->sa_country_id = isset($_GET['country_id']) ? $_GET['country_id'] : $model->sa_country_id;

            if ($model->save()) {
                return $this->redirect(['view', 'id' => $model->id]);
            }
            return $this->redirect(['view', 'id' => $model->id]);
        } else {
            return $this->render('create', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Updates an existing SaCountryDetail model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);
        $postRequest = Yii::$app->request->post();

        if ($model->load($postRequest)) {
            if (empty($model->cover_image)) {
                unset($model->cover_image);
            }

            if (isset($model->cover_image)) {
                $coverImage = UploadedFile::getInstance($model, 'cover_image');
                if (isset($coverImage)) {
                    $newImageName = $model->slug;
                    $saveImage =  (new S3Service())->uploadFile(DataHelper::s3Path($newImageName . '.' . $coverImage->extension, 'sa_college_genral'), $coverImage->tempName);
                    if ($saveImage) {
                        $model->cover_image = $newImageName . '.' . $coverImage->extension;
                    }
                }
            }
            
            $model->updated_by = Yii::$app->user->identity->id;
            
            if ($model->save()) {
                return $this->redirect(['view', 'id' => $model->id]);
            }

            return $this->redirect(['view', 'id' => $model->id]);
        } else {
            return $this->render('update', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Deletes an existing SaCountryDetail model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the SaCountryDetail model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return SaCountryDetail the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = SaCountryDetail::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }
}
