var currentUrlPathName = window.location.pathname;

$("#article-author_id, #article-category_id,#article-title,#article-meta_description, #article-stream_id, #article-highest_qualification").change(function () {
    var data = {};
    data['author_id'] = $("#article-author_id").val();
    data['category_id'] = $("#article-category_id").val();
    data['title'] = $("#article-title").val();
    data['meta_description'] = $("#article-meta_description").val();
    data['article_stream_id'] = $("#article-stream_id").val();
    data['article_highest_qualification'] = $("#article-highest_qualification").val();
    if (data['author_id'] && data['category_id'] && data['title'] && data['meta_description'] && data['article_stream_id'] && data['article_highest_qualification']) {
        tinyMCE.get('article-description').setMode('design');
    } else {
        tinyMCE.get('article-description').setMode('readonly');
    }
});

$(document).ready(function () {
    if (currentUrlPathName == "/article/create" || currentUrlPathName.indexOf('/article/update') > -1) {
        setTimeout(function () {
            setInterval(autoSaveArticle, 50000);
        }, 10000);
    }
    
    // var statusChage = $('#article-status').val();
    // if(statusChage !== '1') {
    //     $('#article-title').on("keyup", function() {
    //         var Text = $(this).val();
    //         var slugChange = convertToSlug(Text);
    //         $('#article-slug').val(slugChange);
    //     });
    // }
});

function autoSaveArticle() {
    var data = {};
    var update_page = '';   
    data['author_id'] = $("#article-author_id").val();
    data['category_id'] = $("#article-category_id").val();
    data['title'] = $("#article-title").val();
    data['description'] = tinyMCE.get('article-description').getContent();
    data['meta_description'] = $("#article-meta_description").val();
    data['article_stream_id'] = $("#article-stream_id").val();
    data['article_highest_qualification'] = $("#article-highest_qualification").val();

    $('#article-description').val(tinyMCE.get('article-description').getContent());
    $('#article-editor_remark').val(tinyMCE.get('article-editor_remark').getContent());

    if (currentUrlPathName == "/article/update") {
        update_page = true;
    } else {
        update_page = false;
    }

    if (data['author_id'] && data['category_id'] && data['title'] && data['meta_description'] && data['article_stream_id'] && data['article_highest_qualification'] && data['description'] && $("#article-status").val() == 2) {
        // ajax to POST article create auto save
        $.ajax({
            type: "POST",
            url: "/auto-save/auto-save-article",
            data: {
                dataArr: $(".article-form form").serialize(),
                auto_save_id: $("#article-auto-save-id").val(),
                update_page: update_page
            },
            success: function (response) {
                $("#article-auto-save-id").val(response.id);
            }
        });
    }
}

function convertToSlug( str ) {
    
    //replace all special characters | symbols with a space
    str = str.replace(/[`~!@#$%^&*()_\-+=\[\]{};:'"\\|\/,.<>?\s]/g, ' ')
             .toLowerCase();
      
    // trim spaces at start and end of string
    str = str.replace(/^\s+|\s+$/gm,'');
      
    // replace space with dash/hyphen
    str = str.replace(/\s+/g, '-');   
    // document.getElementById("slug-text").innerHTML = str;
    return str;
}