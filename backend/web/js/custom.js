$(document).ready(function () {
    $("#featureGroup").on('change', function (e) {
        e.preventDefault();
        var featureGroupId = $(this).val();
        var collegeId = $("#collegeId").val();

        // ajax to get the form fields
        $.ajax({
            type: "GET",
            url: "/feature-value/get-feature-values?feature-group=" + featureGroupId + "&college-id=" + collegeId,
            // dataType: "dataType",
            success: function (response) {
                $("#featureValues").html("").append(response.html);
            }
        });
    });

    $("body").on('change', '#entity', function (e) {
        $('#faq-entity_id').prop('disabled', true);
        $('#faq-entity_id').val("x").trigger("change");
        if ($(this).val() != '') {
            $('#faq-entity_id').prop('disabled', false);
        }
    });
    $("#SponsorCollege-college_id").on('change', function (e) {
        var collegeId = $(this).val();
        $.ajax({
            type: "GET",
            url: "/sponsor-college/get-tags?&college-id=" + collegeId,
            success: function (response) {
                $("#sponsorTags").html("").append(response.html);
            }
        });
    });

    $("body").on('submit', '#f-value-tabular-form', function (e) {
        e.preventDefault();
        $.ajax({
            type: "post",
            url: $(this).attr('action'),
            data: $(this).serialize(),
            dataType: "json",
            success: function (response) {
                console.log(response);
                if (response.response == 'success') {
                    $("#alertMsg").html("").append(alertMsg(response.response));
                    setTimeout(() => {
                        $("#f-value-tabular-form").html(response.html);
                    }, 2000);
                } else {
                    $("#f-value-tabular-form").html(response.html);
                }
            }
        });
    });
});

function alertMsg(response) {
    if (response == 'success') {
        $html = '<div class="alert alert-success">';
        $html += '<strong> Success </strong>';
        $html += '</div>';
    }
    /*else {
        $html = '<div class="alert alert-danger">';
        $html += '<strong> Error </strong>';
        $html += '</div>';
    }*/

    return $html;
}

$('#newscontent-meta_keywords').tagsinput();
$(".bootstrap-tagsinput  input").addClass('form-control');
$(document).ready(function () {
    $(".bootstrap-tagsinput  input").removeAttr('style');
});
$(document).on("change", ".criteria-select", function () {
    var id = $(this).attr('id');
    var result = id.split('-');
    var emptyId = result[0] + '-' + result[1] + '-criteria_id';
    $("#" + emptyId).empty();
});
$("#college-rank-tabular-form").submit(function (e) {
    e.preventDefault();
    var dataIndexvalues = [];
    $('.multiple-input-list__item').each(function (index) {
        dataIndexvalues.push($(this).attr('data-index'));
    });
    for (let i = 0; i < dataIndexvalues.length; i++) {
        var editVal = $('#collegerankings-' + dataIndexvalues[i] + '-id').val();
        var dataIndex = $('#collegerankings-' + dataIndexvalues[i] + '-id').parent().parent().attr('data-index');
        if (editVal == '') {
            var selectCriteriaVal = $('#collegerankings-' + dataIndexvalues[i] + '-criteria_id').val();
            var rankVal = $('#collegerankings-' + dataIndexvalues[i] + '-rank').val();
            var yearVal = $('#collegerankings-' + dataIndexvalues[i] + '-year').val();
            if (selectCriteriaVal == '') {
                alert("Please select criteria value for row " + (i + 1));
                return false;
            }
            if (rankVal == '') {
                alert("Please  enter rank for row " + (i + 1));
                return false;
            }
            if (yearVal == '') {
                alert("Please  enter year for row " + (i + 1));
                return false;
            }
        }
    }
    e.currentTarget.submit();
});
$(document).ready(function () {
    $('#sortable').sortable({
        axis: 'y',
        update: function (event, ui) {
            var data = $("#sortable").sortable('toArray');
            $('.pageLoader').show();
            $.ajax({
                data: { 'data': data, 'entity': $("#sortable").attr('entity') },
                type: 'POST',
                url: '/ajax/get-save-menu-order',
                success: function (response) {
                    $('.pageLoader').hide();
                }
            });


        }
    });

    $('#sortableBoard').sortable({
        axis: 'y',
        update: function (event, ui) {
            var data = $("#sortableBoard").sortable('toArray');
            $('.pageLoader').show();
            $.ajax({
                data: { 'data': data, 'entity': $("#sortableBoard").attr('entity') },
                type: 'POST',
                url: '/ajax/get-save-board-menu-order',
                success: function (response) {
                    $('.pageLoader').hide();
                }
            });


        }
    });
    var myInterval;
    $("#submitFormTranslate").submit(function (e) {
        e.preventDefault();
        e.stopImmediatePropagation();

        $.ajax({
            type: "POST",
            url: $(this).attr('action'),
            data: $(this).serialize(),
            dataType: "json",
            success: function (response) {
                alert(response.message);
                if (response.status == 'success') {
                    $('.submitFormTranslate').attr('disabled', true);
                    myInterval = setInterval(fetchdata, 240000, response.transaction_id);
                }
            }
        });
    });
    function fetchdata(transaction_id) {
        $.ajax({
            url: '/article/check-translate',
            type: 'post',
            data: { 'transaction_id': transaction_id },
            success: function (response) {
                if (response.checkStatus == 1) {
                    $('.translate-message').html(response.message);
                    $('#article-translation').show();
                    clearTimeout(myInterval);
                    $('.submitFormTranslate').attr('disabled', true);
                } else if (response.checkStatus == 3) {
                    $('.translate-message').html(response.message);
                    $('#article-translation').show();
                } else {
                    $('#article-translation').show();
                }
            }
        });
    }

    $('body').on('click', '.close', function () {
        $("#article-translation").hide();
    });

    $("#boardcontent-page").change(function(){
        $.ajax({
            url: 'board-subpage-combination',
            method: 'POST',
            data: { page: $(this).val(), board_id: $("#boardcontent-board_id").val() },
            success: function(response) {
                if(response.status){
                var data = response.results;
                var page = response.page;
                if(page!='supplementary'){
                var formoption = "<option value=''>Select Subjects</option>";
                }else if(page=='supplementary'){
                    var formoption = "<option value=''>Select Supplementary</option>"; 
                }
                $.each(data, function(v) {
                    formoption += "<option value='" + data[v]['text'] + "'>" + data[v]['text'] + "</option>";
                });
                if(page!='supplementary'){
                    $('#syllabus-sub-page-dropdown').html(formoption);
                    
                }else if(page=='supplementary'){
                    $('#supplementary-sub-page-dropdown').html(formoption);
                   
                }
              }
            },
            error: function(xhr, status, error) {
              console.error(error);
            }
          
          });
    })
  
});
