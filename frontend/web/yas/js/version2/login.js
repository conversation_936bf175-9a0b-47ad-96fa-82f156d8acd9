$(document).ready(function () {
    inputErrorClearonFocusLeadForm();
    $.get('/site/login-popup', {}, function (data) {
        $("#login-form-js").html(data);
        initializeLogin();

        //name validation
        $(".txtOnly").keypress(function (e) {
            var key = e.keyCode;
            var regex = /^[A-Za-z ]+$/;
            var isValid = regex.test(String.fromCharCode(key));
            if (!isValid) {
                e.preventDefault();
            }
        });

        //otp validation
        $("#otp-phone, #signup-phone").keypress(function (e) {
            var mobNum = $(this).val();
            var key = e.keyCode;
            var regex = /^[0-9]+$/;
            var isValid = regex.test(String.fromCharCode(key));
            if (!isValid || (
                jQuery.inArray(String.fromCharCode(key), ['9', '8', '7', '6']) == -1 &&
                mobNum.length == 0)) {
                e.preventDefault();
            } else {
                if (mobNum.length >= 10) {
                    e.preventDefault();
                }
            }
        });

        //validtion for mobile
        if (document.querySelector("#signup-phone") !== null) {
            document.querySelector("#signup-phone").addEventListener('input', (e) => {
                e.target.value = e.target.value.replace(/^[^6-9]|\D/g, '');
                if (e.target.value.length < 10) {
                    $(".errorMsgMobile").html("phone number should contain 10 digits");
                } else {
                    $(".errorMsgMobile").html("");
                }
            });
        }

        $(".streamCategoryLogin select").select2({
            placeholder: "Select Stream (E.g. B.com)",
            ajax: {
                url: "/ajax/lead-stream",
                dataType: "json",
                type: "GET",
                data: function (params) {
                    var queryParameters = {
                        term: params.term,
                        entity: 'login'
                    }
                    return queryParameters;
                },
                processResults: function (data) {
                    return {
                        results: $.map(data, function (item) {
                            return {
                                text: item.text,
                                id: item.id,
                            };
                        }),
                    };
                },
            },
        });

        // Function to enable/disable the select dropdown
        function toggleLevelSelect(enabled) {
            var $levelSelect = $('#interested_level_lead_login'); // select dropdown
            if (enabled) {
                $levelSelect.prop('disabled', false);
            } else {
                $levelSelect.prop('disabled', true);
            }
        }

        $('.streamCategoryLogin select').on('change', function (e) {
            toggleLevelSelect(true);
        });

        $('.levelCategoryLogin select').select2({
            placeholder: "Level Interested",
            name: 'inputLevel',
            ajax: {
                url: "/ajax/lead-level",
                dataType: "json",
                type: "GET",
                data: function (params) {
                    var queryParameters = {
                        term: params.term,
                        entity: 'login',
                        stream_id: $("#interested_stream_lead_login").val(), // selected stream ID
                    }
                    return queryParameters;
                },
                processResults: function (data) {
                    return {
                        results: $.map(data, function (item) {
                            return {
                                text: item.text,
                                id: item.id,
                            };
                        }),
                    };
                },
            },
        });

        $("#current_city").select2({
            placeholder: "Select City",
            ajax: {
                url: "/ajax/lead-cities",
                dataType: "json",
                type: "GET",
                data: function (params) {
                    var queryParameters = {
                        term: params.term
                    }
                    return queryParameters;
                },
                processResults: function (data) {
                    var states = data.states || [];
                    var results = states.map(function (state) {
                        return {
                            text: state.state_name,
                            children: state.cities.map(function (city) {
                                return {
                                    id: city.id,
                                    text: city.text
                                };
                            })
                        };
                    });

                    return { results: results };
                },
            },
        });
    });
})

function initializeLogin() {
    //get field info
    const nameInputLead = document.querySelector('#formNameLogin');
    const emailInputLead = document.querySelector('#formEmailLogin');
    const mobileInputLead = document.querySelector('#signup-phone');
    const streamSelectLead = document.querySelector('#interested_stream_lead_login');
    const levelSelectLead = document.querySelector('#interested_level_lead_login');
    const citySelectLead = document.querySelector('#current_city');
    const cityInputIp = document.querySelector('#current_city_ip_login');
    const submitButtonLead = document.querySelector('#submitLoginForm');


    //email validation
    if (document.querySelector("#formEmailLogin") !== null) {
        document.querySelector("#formEmailLogin").addEventListener('input', (e) => {
            e.target.value = e.target.value.replace(/^[^a-zA-Z]|[^a-zA-Z0-9@_\-\.]|[^\w\d_\-@\.]$/g, '');
            if ((e.target.value.match(/\./g) || []).length > 1) {
                e.target.value = e.target.value.substring(0, e.target.value.length - 1);
            }
            if ((e.target.value.match(/@/g) || []).length > 1) {
                e.target.value = e.target.value.substring(0, e.target.value.length - 1);
            }

            var email = $('#formEmailLogin').val();
            let strLst = email.slice(email.indexOf("@") + 1, email.length)
            if (email.indexOf('@') === -1 || email.indexOf('@') === email.length - 1 || !(/^[a-zA-Z]+\.[a-zA-Z]+$/).test(strLst)) {
                // String contains "@" after the first character and "@" is not the last character
                $(".errorMsgEmail").html("Email is not a valid email address.");

            } else {
                if (nameInputLead.value.length > 0 && streamSelectLead.value.length > 0 && levelSelectLead.value.length > 0 && (citySelectLead.value.length !== 0 || cityInputIp.value.length !== 0)) {
                    console.log("hi");
                    submitButtonLead.removeAttribute('disabled');
                }
                $('.errorMsgEmail').html('');
                // $('.validationError').html('');
            }
        })
        // document.querySelector("#formEmailLogin").addEventListener('blur', (e) => {
        //     var email = $('#formEmailLogin').val();
        //     let strLst = email.slice(email.indexOf("@") + 1, email.length)
        //     if (email.indexOf('@') === -1 || email.indexOf('@') === email.length - 1 || !(/^[a-zA-Z]+\.[a-zA-Z]+$/).test(strLst)) {
        //         // String contains "@" after the first character and "@" is not the last character
        //         $(".errorMsgEmail").html("Email is not a valid email address.");

        //     } else {
        //         if (nameInputLead.value.length > 0 && streamSelectLead.value.length > 0 && levelSelectLead.value.length > 0 && (citySelectLead.value.length !== 0 || cityInputIp.value.length !== 0)) {
        //             console.log("hi");
        //             submitButtonLead.removeAttribute('disabled');
        //         }
        //         $('.errorMsgEmail').html('');
        //         // $('.validationError').html('');
        //     }
        // });
    }

    $("#formNameLogin, #formEmailLogin, #signup-phone, #interested_stream_lead_login, #interested_level_lead_login").bind("change keyup", function (event) {
        if (nameInputLead.value.length !== 0 && (emailInputLead.value.length !== 0 && $(".inputEmailContainerLogin .errorMsg").html() == '') && mobileInputLead.value.length == 10 && (citySelectLead.value.length !== 0 || cityInputIp.value.length !== 0) && streamSelectLead.value.length !== 0 && levelSelectLead.value.length !== 0) {
            console.log("hi1");
            submitButtonLead.removeAttribute('disabled');
        } else {
            submitButtonLead.setAttribute('disabled', true);
        }
    });

    $('.logInPage .select2').select2({
        placeholder: function () {
            $(this).data('placeholder');
        }
    })

    let inputs = document.querySelectorAll(".otp-box");
    let values = Array(4);
    let clipData;
    inputs.length !== 0 ? inputs[0].focus() : '';

    inputs.forEach((tag, index) => {
        tag.addEventListener('keyup', (event) => {
            if (event.code === "Backspace" && hasNoValue(index)) {
                if (index > 0) inputs[index - 1].focus();
            }

            //else if any input move focus to next or out
            else if (tag.value !== "") {
                (index < inputs.length - 1) ? inputs[index + 1].focus() : tag.blur();
            }

            //add val to array to track prev vals
            values[index] = event.target.value;
        });

        tag.addEventListener('input', () => {
            //replace digit if already exists
            if (tag.value > 10) {
                tag.value = tag.value % 10;
            }
        });

        tag.addEventListener('paste', (event) => {
            event.preventDefault();
            clipData = event.clipboardData.getData("text/plain").split('');
            filldata(index);
        })
    })

    function filldata(index) {
        for (let i = index; i < inputs.length; i++) {
            inputs[i].value = clipData.shift();
        }
    }

    function hasNoValue(index) {
        if (values[index] || values[index] === 0)
            return false;

        return true;
    }

    $("#otp-form").submit(function (e) {
        e.preventDefault()
        displayErrors()
        var form = $(this)
        $.ajax({
            url: '/site/send-otp',
            data: form.serialize(),
            dataType: 'json',
            method: 'POST',
            beforeSend: function () {
                $('.primaryBtn').prop('disabled', true);
            },
            error: function (xhr, err) {
                $('.primaryBtn').prop('disabled', false)
                displayErrors('Something went wrong, please try again!')
            },
            complete: function () {
                $('.primaryBtn').prop('disabled', false);
            },
            success: function (data) {
                if (data.success == true) {
                    $('#mobileNum').html($('#otp-phone').val())
                    $('#otp-form').hide()
                    $('#login-form').show()
                    otpTimer(data.data.expiresIn)
                }
                displayErrors(data.message)

            }
        });
    });

    $(document).ready(function () {
        $("#signup-form").submit(function (e) {
            e.preventDefault()
            displayErrors()
            var form = $(this)
            $.ajax({
                url: '/site/signup',
                data: form.serialize(),
                dataType: 'json',
                method: 'POST',
                beforeSend: function () {
                    $('.primaryBtn').prop('disabled', true);
                },
                error: function (xhr, err) {
                    $('.primaryBtn').prop('disabled', false)
                    displayErrors('Something went wrong, please try again!')
                },
                complete: function () {
                    $('.primaryBtn').prop('disabled', false);
                },
                success: function (data) {
                    if (data.success == true) {
                        $('#otp-phone').val($('#signup-phone').val())
                        $("#otp-form").submit()
                        $('#signup-form').hide()
                    } else {
                        displayErrors(data.message)
                    }

                }
            });

        });
    });

    $("#login-form").submit(function (e) {
        e.preventDefault()
        displayErrors()
        webengage.track("login", {
            'login':'Yes'
        });
        var otp = $("input[name='digit[]']").map(function () { return $(this).val(); }).get().join('')
        $('input[name="otp"]').val(otp)
        $('#login-phone').val($('#otp-phone').val())
        var form = $(this)
        $.ajax({
            url: '/site/login',
            data: form.serialize(),
            dataType: 'json',
            method: 'POST',
            beforeSend: function () {
                $('.primaryBtn').prop('disabled', true);
            },
            error: function (xhr, err) {
                // $('.primaryBtn').prop('disabled', false)
                // displayErrors('Something went wrong, please try again!33333')
            },
            complete: function () {
                $('.primaryBtn').prop('disabled', false);
            },
            success: function (data) {
                form.trigger("reset")
                if (data.success == true) {
                    displayErrors(data.message)

                    //add to local storage if logged in though readMore
                    if (localStorage.getItem('reviewReadMoreId')) {
                        localStorage.setItem('loggedIn', "Yes");
                    }
                    // window.location = document.referrer;
                    // window.location.reload();
                    window.location.href = "https://getmyuni.com";
                } else {
                    displayErrors(data.message)
                }

            }
        });
    });

    $(".closeLoginpopup").click(function () {
        $('#otp-form').trigger("reset");
        $('.errorMsg').html('');
        $("#login-form-js").fadeOut();
        $("body").removeClass("no-scroll");
        $("body").css("overflowY", "unset");
        $(".pageMask").css("display", "none");
    });
}

function displayErrors(errors = undefined) {
    $('.validationError').remove();
    $('.errorMsg').html('');
    $('select, input').removeClass('errorInputField');

    if (typeof errors === 'object') {
        for (const [key, value] of Object.entries(errors)) {
            $('select[name="' + key + '"], input[name="' + key + '"]').parent().append('<p class="error validationError">' + value + '</p>');
            $('select[name="' + key + '"], input[name="' + key + '"]').addClass('errorInputField');
        }
    }

    if (typeof errors === 'string') {
        $('.errorMsg').html(errors);
    }
}

function resendOtp() {
    $("#otp-form").submit()
    $("#otp-form-news").submit()
}

function otpTimer(counter) {
    $('#otpResend').html('You can resend otp in <a style="cursor: pointer;">' + counter + '</a> seconds.')
    var interval = setInterval(function () {
        counter--;
        if (counter <= 0) {
            clearInterval(interval);
            $('#otpResend').html('Didn\'t received the OTP? <a onclick="resendOtp()" style="cursor: pointer;"> Resend OTP.</a>');
            return;
        } else {
            $('#otpResend').html('You can resend otp in <a style="cursor: pointer;">' + counter + '</a> seconds.')
        }
    }, 1000);

}

function showHide(show = 'signup') {
    displayErrors();
    $('#login-form').hide();
    if (show == 'signup') {
        $('#otp-form').hide();
        $('#signup-form').show();
    }
    if (show == 'login') {
        $('#otp-form').show();
        $('#signup-form').hide();
    }
}

function showLoginPopup(bool = true) {
    if (bool) {
        document.querySelector('#lead-form-js-new').style.display = "none";
        $('#login-form-js').css('display', 'block');
    } else {
        $('#login-form-js').fadeOut();
        document.querySelector('#lead-form-js-new').style.display = "block";
    }
}
function inputErrorClearonFocusLeadForm() {
    $(".form-group").focusin(function () {
        $(this).find('.help-block').html('')
        $(this).find('.validationError').html('')
    });
    disableScrolling();
}

function disableScrolling() {
    var x = window.scrollX;
    var y = window.scrollY;
    window.onscroll = function () { window.scrollTo(x, y); };
    setTimeout(
        function () {
            enableScrolling();
        }, 50);
}

function enableScrolling() {
    window.onscroll = function () { };
}

function inputErrorClearonFocusLeadForm() {
    $(".form-group").focusin(function () {
        $(this).find('.help-block').html('')
        $(this).find('.validationError').html('')
    });
}

function inputErrorClearonFocusLeadForm() {
    $(".form-group").focusin(function () {
        $(this).find('.help-block').html('')
        $(this).find('.validationError').html('')
    });
}

var blog_arr = [
    "Paramedical",
    "Pharmacy",
    "Architecture",
    "Fashion",
    "Design",
    "HotelManagement",
    "Law",
    "Veterinary",
    "VocationalCourses",
    "Arts",
    "Computer",
    "Dental",
    "Education",
];
var catId = "test";
var arrFeaturedColl = {};
window.sessionStorage.setItem("items", JSON.stringify(arrFeaturedColl));
$(document.body).on("mouseenter", ".moremenu", function () {
    $.each(blog_arr, function (i, val) {
        $("#secondaryHeader-web #" + val + "-blog").addClass("display_none");
        $("#secondaryHeader-web #" + val + "-blog").removeAttr("style");
        $("#secondaryHeader-web #" + val + " a").removeClass("fontBold");
        $("#secondaryHeader-web #" + val).removeClass("hoverbg");
    });
    var categoryId = $(this).attr("id");
    $("#secondaryHeader-web #" + categoryId + "-blog").removeClass(
        "display_none"
    );
    $("#secondaryHeader-web #" + categoryId + " a").addClass("fontBold");
    $("#secondaryHeader-web #" + categoryId).addClass("hoverbg");
    secondaryNavigationAjax(categoryId);

});

$(document.body).on("mouseenter", ".navigation_header_dropdown", function () {
    var menuId = $(this).attr("id");
    if (menuId == "More") {
        menuId = $(".hoverbg").attr("id");
    }
    secondaryNavigationAjax(menuId);
});

function secondaryNavigationAjax(categoryId) {
    boolNav = true;
    var storedArray = JSON.parse(sessionStorage.getItem("items"));//no brackets
    var navKeys = Object.keys(storedArray);
    if (navKeys != '') {
        if (navKeys.includes(categoryId)) {
            response = storedArray[categoryId];
            featuredCollegeDisplay(response, categoryId);
            boolNav = false;
        }
    }
    if (boolNav) {
        $.ajax({
            type: "get",
            url: '/site/secondary-navigations',
            data: { streamName: categoryId },
            success: function (response) {
                if (response != '') {
                    featuredCollegeDisplay(response, categoryId);
                    arrFeaturedColl[categoryId] = response;
                    window.sessionStorage.setItem("items", JSON.stringify(arrFeaturedColl));
                }
            }
        })
    }
}

function featuredCollegeDisplay(response, categoryId) {
    var parent = document.querySelector('.outer-nav');
    var child = document.querySelector('.featuredScrollLeft');
    if ((parent.contains(child) == false) || (catId != categoryId)) {
        if (parent.contains(child) == true) {
            $('.outer-nav').children().remove();
        }
        catId = categoryId;
        $('.outer-nav').append(response);
        var resChild = document.querySelector('.featuredScrollLeft');
        if (parent.contains(resChild)) {
            $('.outer-nav').show();
        } else {
            $('.outer-nav').hide();
        }
        $('.customSliderCards.homeFeaturedCollege').not('.slick-initialized').slick({
            speed: 2000,
            autoplay: true,
            autoplaySpeed: 1500,
            centerMode: true,
            cssEase: 'linear',
            slidesToShow: 3,
            slidesToScroll: 1,
            variableWidth: true,
            infinite: true,
            initialSlide: 1,
            arrows: false,
            buttons: false
        });
    }
}

//header mega menu ends
