<?php

use common\helpers\DataHelper;
use frontend\helpers\Url;
use frontend\models\LeadForm;
use yii\helpers\ArrayHelper;

$student = \Yii::$app->user->identity;

$userCity = DataHelper::getUserLocation();
$styleCityField = $userCity['cityId'] == '' ? 'block' : 'none';

?>
<form class="userForm" id="otp-form" style="display:none">
    <input type="hidden" name="_csrf-frontend" value="<?= Yii::$app->request->csrfToken; ?>">
    <div class="signInDiv">
        <div>
            <div class="forPage">
                <h2 class="m-0">Welcome Back!</h2>
                <p>Log in with your data that you entered during your registration</p>
            </div>
            <div class="formHeadingDiv row forPopup">
                <!-- <div class="formImg">
                    <img src="<?= Url::toDomain() ?>yas/images/default-lead-form-icon.png" alt="" width="62" height="62">
                </div> -->
                <div class="formHeading">
                    <p class="loginHeadingText">Welcome back!</p>
                    <p class="loginSubHeadingText">Log in with the data you entered during your registration</p>
                </div>
            </div>
            <img class="lazyload mobileOnly loginCreative" loading="lazy" src="/yas/images/login-image.webp" alt="img" width="273" height="400">

            <div class="formField">
                <div class="row m-0">
                    <div class="dialCodeDiv">
                        <i class="spriteIcon flagIcon"></i>
                        <span class="dialCode">+91</span>
                        <img src="<?= Url::toDomain() ?>yas/images/select-angle.png" width="14" height="9" alt="">
                    </div>
                    <div class="numberInput">
                        <div class="form-group field-leadform-mobile required">
                            <input type="number" id="otp-phone" name="phone" min="0" oninput="validity.valid||(value='');" maxlength="10" placeholder="Mobile Number">
                        </div>
                    </div>
                    <p class="error errorMsg"></p>
                </div>
            </div>
            <p class="disclaimer forPage">A 4 digit OTP will be sent via SMS to verify your mobile number!</p>
            <button class="primaryBtn" type="submit">Request OTP</button>
            <p class="logInOption pb-0 forPage">Dont have an account yet? <a onclick="showHide('signup')" style="cursor: pointer;">Sign Up</a>
            </p>
            <p class="forPopup signupOption">Dont have an account yet? <a onclick="showLoginPopup(false)">Sign Up</a>
            </p>
        </div>
    </div>
</form>
<form class="userForm" id="login-form" style="display:none;">
    <input type="hidden" name="_csrf-frontend" value="<?= Yii::$app->request->csrfToken; ?>">
    <div class="otpSection">
        <h2>Verify Mobile Number</h2>
        <p class="otpMessage">OTP has been sent to your mobile
            number <br /> +91 <span id="mobileNum"></span> <a onclick="showHide('login')" style="cursor: pointer;">Change</a></p>
        <input type="hidden" id="login-phone" name="phone">
        <input type="hidden" name="otp">
        <div class="otpInputs">
            <div>
                <input onkeydown="return event.keyCode !== 69" id="otp-first" pattern="[0-9]*" class="otp-box" name="digit[]" type="number" min="0" max="9" step="1" aria-label="first digit" />
                <input onkeydown="return event.keyCode !== 69" id="otp-second" pattern="[0-9]*" class="otp-box" name="digit[]" type="number" min="0" max="9" step="1" aria-label="second digit" />
                <input onkeydown="return event.keyCode !== 69" id="otp-third" pattern="[0-9]*" class="otp-box" name="digit[]" type="number" min="0" max="9" step="1" aria-label="third digit" />
                <input onkeydown="return event.keyCode !== 69" id="otp-fourth" pattern="[0-9]*" class="otp-box" name="digit[]" type="number" min="0" max="9" step="1" aria-label="fourth digit" />
            </div>
            <p class="error errorMsg"></p>
        </div>

        <img class="mobileOnly lazyload" loading="lazy" src="/yas/images/login-image.webp" alt="img" width="273" height="400">

        <button class="primaryBtn" type="submit">Verify OTP</button>
        <p id="otpResend"></p>

    </div>

</form>
<form class="userForm" id="signup-form" style="display: block;">
    <input type="hidden" name="_csrf-frontend" value="<?= Yii::$app->request->csrfToken; ?>">
    <div class="registerSection">
        <h2>Register</h2>
        <p>We at GetMyUni aim to bridge the gap between
            Universities and Students</p>
        <p class="error errorMsg"></p>
        <div class="formField">
            <i class="spriteIcon userIcon"></i>
            <div class="form-group">
                <input type="text" name="name" id="formNameLogin" placeholder="Enter Name" class="txtOnly">
            </div>
        </div>
        <div class="formField" style="margin-bottom: 10px;">
            <div class="row m-0 mobileLoginField">
                <div class="dialCodeDiv">
                    <i class="spriteIcon flagIcon"></i>
                    <span class="dialCode">+91</span>
                    <img src="<?= Url::toDomain() ?>yas/images/select-angle.png" width="14" height="9" alt="">
                </div>
                <div class="numberInput" style="margin-bottom: -10px;">
                    <div class="form-group field-leadform-mobile">
                        <input type="number" name="phone" id="signup-phone" min="0" oninput="validity.valid||(value='');" maxlength="10" placeholder="Mobile Number">
                    </div>
                </div>
                <p class="error errorMsg errorMsgMobile"></p>
            </div>
        </div>
        <div class="formField inputEmailContainerLogin">
            <i class="spriteIcon mailIcon"></i>
            <div class="form-group">
                <input type="email" id="formEmailLogin" name="email" placeholder="Email Address">
                <p class="error errorMsg errorMsgEmail"></p>
                <!-- <span class="domainExtention">@gmail.com</span> -->
            </div>
        </div>
        <div class="formField" style="display: <?= $styleCityField ?>;">
            <i class="spriteIcon locationIcon"></i>
            <div class="form-group">
                <select class="select2" name="current_city" id="current_city" data-placeholder="Select Your Current City">
                </select>
            </div>
        </div>
        <div class="formField">
            <i class="spriteIcon bookIcon"></i>
            <div class="form-group streamCategoryLogin autoFtechQualification">
                <select class="select2" id="interested_stream_lead_login" name="stream" data-placeholder="Select Stream">
                </select>
            </div>
        </div>
        <div class="formField">
            <i class="spriteIcon bookIcon"></i>
            <div class="form-group levelCategoryLogin autoFtechQualification">
                <select class="select2" id="interested_level_lead_login" name="level" data-placeholder="Select Level">
                </select>
            </div>
        </div>
        <p class="disclaimer">By clicking submit you agree to the <a target="_blank" href="<?= Url::toDomain() ?>terms-and-conditions"> Terms and Conditions.</a></p>
        <button class="primaryBtn" type="submit" id="submitLoginForm" disabled>Submit</button>
        <p class="logInOption pb-0">Already have an account <a onclick="showHide('login')" style="cursor: pointer;">Login here!</a></p>
    </div>
    <input type="hidden" name="current_city_ip" id="current_city_ip_login" value="<?= $userCity['cityId'] ?>">
    <input type="hidden" name="current_state_ip" id="current_state_ip_login" value="<?= $userCity['stateId'] ?>">
    <input type="hidden" name="source" value="<?= empty($_GET['source']) ? '' : (isset(DataHelper::$leadSource[$_GET['source']]) ? DataHelper::$leadSource[$_GET['source']] : '') ?>">
    <input type="hidden" name="utm_source" value="<?= empty($_GET['utm_source']) ? '' : $_GET['utm_source'] ?>">
    <input type="hidden" name="utm_medium" value="<?= empty($_GET['utm_medium']) ? '' : $_GET['utm_medium'] ?>">
    <input type="hidden" name="utm_campaign" value="<?= empty($_GET['utm_campaign']) ? '' : $_GET['utm_campaign'] ?>">
</form>
<script id="_webengage_script_tag" type="text/javascript">
        var webengage;
        ! function(w, e, b, n, g) {
            function o(e, t) {
                e[t[t.length - 1]] = function() {
                    r.__queue.push([t.join("."), arguments])
                }
            }
            var i, s, r = w[b],
                z = " ",
                l = "init options track screen onReady".split(z),
                a = "feedback survey notification".split(z),
                c = "options render clear abort".split(z),
                p = "Open Close Submit Complete View Click".split(z),
                u = "identify login logout setAttribute".split(z);
            if (!r || !r.__v) {
                for (w[b] = r = {
                        __queue: [],
                        __v: "6.0",
                        user: {}
                    }, i = 0; i < l.length; i++) o(r, [l[i]]);
                for (i = 0; i < a.length; i++) {
                    for (r[a[i]] = {}, s = 0; s < c.length; s++) o(r[a[i]], [a[i], c[s]]);
                    for (s = 0; s < p.length; s++) o(r[a[i]], [a[i], "on" + p[s]])
                }
                for (i = 0; i < u.length; i++) o(r.user, ["user", u[i]]);
                setTimeout(function() {
                    var f = e.createElement("script"),
                        d = e.getElementById("_webengage_script_tag");
                    f.type = "text/javascript",
                        f.async = !0,
                        f.src = ("https:" == e.location.protocol ? "https://widgets.in.webengage.com" : "http://widgets.in.webengage.com") + "/js/webengage-min-v-6.0.js",
                        d.parentNode.insertBefore(f, d);
                }, 10000)
            }
        }(window, document, "webengage");
        webengage.init('in~d3a49c51');
        webengage.onReady(function() {
            webengage.notification.onOpen(function(data) {
                var a = document.getElementById('webklipper-publisher-widget-container-notification-frame');
                var layoutId = a.dataset.notificationLayoutId;
                if (layoutId == '~483819h') {
                    a.style.left = 'auto';
                    a.style.top = 'auto';
                    a.style.transform = 'none';
                    a.style.zIndex = 1;
                    var _stickyBtn = document.getElementsByClassName("getSupport").length ? document.getElementsByClassName("getSupport")[0] : null;
                    a.style.bottom = '0px';
                    if (_stickyBtn) {
                        a.style.bottom = _stickyBtn.getBoundingClientRect().height + 'px';
                    }
                    var throttle = function(callback, delay) {
                        var throttleTimeout = null;
                        var storedEvent = null;

                        var throttledEventHandler = function(event) {
                            storedEvent = event;

                            var shouldHandleEvent = !throttleTimeout;

                            if (shouldHandleEvent) {
                                callback(storedEvent);

                                storedEvent = null;

                                throttleTimeout = setTimeout(function() {
                                    throttleTimeout = null;

                                    if (storedEvent) {
                                        throttledEventHandler(storedEvent);
                                    }
                                }, delay);
                            }
                        };

                        return throttledEventHandler;
                    };
                    var scrollThrottle = throttle(function() {
                        if (_stickyBtn) {
                            a.style.bottom = _stickyBtn.getBoundingClientRect().height + 'px';
                        }
                    }, 1000);
                    document.addEventListener("scroll", scrollThrottle);
                }
            });
        });
    </script>