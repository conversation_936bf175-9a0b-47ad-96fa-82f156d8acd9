<?php

namespace common\event;

use Yii;
use common\helpers\CollegeHelper;
use common\helpers\ContentHelper;
use common\models\College;
use common\models\CollegeCourse;
use common\models\CollegeProgram;
use common\models\CollegeProgramFees;
use common\models\Course;
use common\models\documents\College as DocumentsCollege;
use common\models\documents\CollegeProgram as DocumentsCollegeProgram;
use common\models\Exam;
use common\models\Filter;
use common\models\FilterGroup;
use common\models\Program;
use common\models\ProgramCourseMapping;
use common\services\CollegeService;
use common\services\ReviewService;
use common\models\Specialization;
use common\models\SpecializationNew;
use yii\db\Query;
use yii\helpers\ArrayHelper;
use common\models\CollegeElastic;
use yii\elasticsearch\Query as ElasticQuery;
use common\models\CollegeRankings;
use common\models\OtherRankings;
use common\models\CollegeRankingPublisher;

class CollegeFilterEvent
{
    public static $feesRange = [
        '0-100000',
        '100001-200000',
        '200001-300000',
        '300001-500000',
        '500001',
    ];

    public static $_avgCourseFees = [];

    public static $minAvgFees = 0;
    public static $maxAvgFees = 0;

    /**
     * Update CollegeFilter Collection in Mongodb.
     * Call this function when college details updated.
     *
     * @param College $college \common\models\College
     * @return Object | Null
     */
    public function updateCollegeFilter(College $college)
    {

        $collegeService = new CollegeService();
        $indexedFeatures = [];
        $instituteTypeName = '';

        $model = DocumentsCollege::find()->where(['college_id' => $college->id])->one();
       
        if (!$model) {
            $model = new DocumentsCollege();
        }

        $review = (new ReviewService)->getCollegeReviewsFiltePage($college->id);
        $revCategoryRating = (new ReviewService)->getCollegeBasedCategoryRating($college->id);
        $reviewCount = (new ReviewService)->getReviewRatingCount($college->id);

        $subPages = $this->getSubpages($collegeService->getMenu($college));
        $allFeatures = $collegeService->getFeatures($college);
        if (!empty($allFeatures)) {
            $indexedFeatures = ArrayHelper::index($collegeService->getFeatures($college), null, 'featureGroupName');
            foreach ($indexedFeatures as $key => $value) {
                $indexedFeatures[$key] = ArrayHelper::getColumn($indexedFeatures[$key], 'featureSlug', false);
                if (isset($value[0]['featureSlug']) && $value[0]['featureSlug'] == 'institution-type') {
                    $instituteTypeName = $value[0]['value'];
                }
            }
        }
        

        $model->college_id = $college->id;
        $model->college_old_id = $college->old_id;
        $model->name = $college->name;
        $model->display_name = $college->display_name;
        $model->slug = $college->slug;
        $model->logo = $college->logo_image ? $college->logo_image : $college->getOldAttribute('logo_image');
        $model->banner_image = $college->cover_image ? $college->cover_image : $college->getOldAttribute('cover_image');
        $model->city_id = $college->city->id;
        $model->city_name = $college->city->name;
        $model->city_slug = trim($college->city->slug);
        $model->state_id = $college->city->state->id;
        $model->state_name = $college->city->state->name;
        $model->state_slug = $college->city->state->slug;
        $model->course =  $this->getCourseLists($college);
        $model->avgFees = $this->calculateAverage($model->course);
        $model->review = !empty($review) ? ContentHelper::htmlDecode($review['content']) : '';
        $model->rev_category_rating = $revCategoryRating ?? [];
        $model->review_count = !empty($reviewCount) ? $reviewCount['reviewCount'] : '';
        $model->rating = !empty($revCategoryRating) ? CollegeHelper::getTotalRating($revCategoryRating) : '';
        $model->sub_pages = $subPages ?? [];
        $model->exams = $this->getCollegeCourseExamByCollegeId($college->id) ?? [];
        $model->type = $instituteTypeName ?? '';
        $model->is_popular = $college->is_popular ?? '';
        $model->rank = $college->rank ?? 999999;
        $model->is_sponsored = $college->is_sponsored ?? '';
        $model->status = $college->status;
        if (isset($indexedFeatures['Approvals'])) {
            $model->approvals = $indexedFeatures['Approvals'];
        }
        if (!empty($college->position)) {
            $model->position = $college->position;
        }
        if ($college->type == (College::TYPE_COLLEGE || College::TYPE_INSTITUTES) && !empty($college->parent)) {
            $model->affiliated_by = $college->parent->slug ?? '';
        }

        if ($model->save()) {
            // echo "{$model->college_id} \t {$model->name} \n";
        } else {
            print_r($model->getErrors());
        }
    }

    public static function getCollegeAvgFees($collegeId, $courseId = null)
    {
        // $avgFees = CollegeCourse::find()->where(['college_id' => $collegeId])
        //     ->andWhere(['is not', 'fee', null])
        //     ->andWhere(['status' => CollegeCourse::STATUS_ACTIVE])
        //     ->andWhere([
        //         'and',
        //         ['not like', 'name', '%' . 'Integrated' . '%', false],
        //         // ['not like', 'name', '%' . 'Hons' . '%', false],
        //         ['not like', 'name', '%' . 'Distance' . '%', false],
        //         ['not like', 'name', '%' . '+' . '%', false],
        //         ['not like', 'name', '%' . 'Dual Degree' . '%', false],
        //         ['not like', 'name', '%' . 'Lateral' . '%', false]
        //     ]);

        // if ($courseId) {
        //     $avgFees->andWhere(['course_id' => $courseId]);
        // }

        // return $avgFees->average('fee');
        
        $query = new Query();
        $query->select(['cf.fees', 'cf.id', 'cp.id as collegeprogramId'])
            ->from(CollegeProgramFees::tableName() . ' as cf')
            ->innerJoin(CollegeProgram::tableName() . ' as cp', 'cp.id = cf.college_program_id')
            ->innerJoin(Program::tableName() . ' as pro', 'pro.id = cp.program_id')
            ->where(['cp.college_id' => $collegeId])
            ->andWhere(['cf.status' => CollegeCourse::STATUS_ACTIVE])
            ->andWhere(['cp.status' => CollegeCourse::STATUS_ACTIVE])
            ->andWhere(['pro.type' => 'full_time'])
            ->andWhere(['cf.type' => 'tuition_fees'])
            ->andWhere(['is not', 'cf.fees', null])
            ->andWhere([
                'and',
                ['not like', 'pro.name', '%' . 'Integrated' . '%', false],
                ['not like', 'pro.name', '%' . 'Hons' . '%', false],
                ['not like', 'pro.name', '%' . 'Diploma' . '%', false],
                ['not like', 'pro.name', '%' . 'Distance' . '%', false],
                ['not like', 'pro.name', '%' . '+' . '%', false],
                ['not like', 'pro.name', '%' . 'Dual Degree' . '%', false],
                ['not like', 'pro.name', '%' . 'Lateral' . '%', false],
                ['not like', 'pro.name', '%' . '{' . '%', false],
                ['not like', 'pro.name', '%' . 'tie up' . '%', false],
                ['not like', 'pro.name', '%' . 'certificate' . '%', false],
                ['not like', 'pro.name', '%' . 'Fellowship' . '%', false]
            ])
            ->andWhere(['or', ['not like', 'pro.name', '%' . '{' . '%', false], ['not like', 'pro.name', '%' . '(' . '%', false]])->all();
        if ($courseId) {
            $query->andWhere(['cp.course_id' => $courseId]);
        }

        $data = $query->all();
        if (empty($data)) {
            return 0;
        }
           
        $sum = array_sum(array_column($data, 'fees'));
        $count = count(array_unique(array_column($data, 'collegeprogramId')));

        $avgFees = ($sum > 0) ?  $sum / $count : 0;

        return round($avgFees);
    }

    public static function getCourseLists(College $college)
    {
        $minavgFees = 0;
        $maxavgFees = 0;
        $courses = $college->courses;
        if (empty($courses)) {
            return [];
        }

        $items = [];
        $feesRangeData =[];
        foreach ($courses as $course) {
            if (empty(CollegeService::getCourse($course['slug']))) {
                $collection = Yii::$app->mongodb->getCollection(DocumentsCollegeProgram::COLLECTION_NAME);
                $collection->remove(['course_slug' => $course['slug']]);
                continue;
            }
            // if ($course->slug == 'doctor-of-philosophy-phd') {
            //     continue;
            // }

            $isCollegeCourseActive = CollegeProgram::find()
                ->where(['course_id' => $course->id])
                ->andWhere(['college_id' => $college->id])
                ->andWhere(['status' => CollegeProgram::STATUS_ACTIVE])
                ->count();
            if ($course->slug == 'other' || !$isCollegeCourseActive) {
                continue;
            }

            // $specializationList = CollegeCourse::find()
            //     ->select(['s.slug', 'college_course.is_hons'])
            //     ->where(['course_id' => $course->id])
            //     ->andWhere(['college_id' => $college->id])
            //     ->andWhere(['=', 's.status', Specialization::STATUS_ACTIVE])
            //     ->andWhere(['is not', 'specialization_id', null])
            //     ->leftJoin('specialization_new s', 's.id = specialization_id')
            //     ->asArray()
            //     ->all();
            $specializationList = self::getSpecialization($course->id, $college->id);
            
            // $collegeProgramCourse = CollegeProgram::find()
            //     ->select('course_id')
            //     ->where(['course_id' => $course->id])
            //     ->andWhere(['college_id' => $college->id])
            //     ->one();

            // $courseDegree = CourseService::getCourseFeature($collegeProgramCourse->id);
            // $degrees[] =  $course->degree ?? '';
            
            $type = self::getProgramType($course->id, $college->id);
            $mode = self::getProgramMode($course->id, $college->id);

            // $degrees = CollegeCourse::find()
            //     ->select('degree')->distinct('degree')
            //     ->where(['course_id' => $course->id])
            //     ->andWhere(['college_id' => $college->id])
            //     ->asArray()
            //     ->all();

            // $type = CollegeCourse::find()
            //     ->select('type')
            //     ->distinct('type')
            //     ->where(['course_id' => $course->id])
            //     ->andWhere(['college_id' => $college->id])
            //     ->asArray()
            //     ->all();


            // $mode = CollegeCourse::find()
            //     ->select('mode')
            //     ->distinct('mode')
            //     ->where(['course_id' => $course->id])
            //     ->andWhere(['college_id' => $college->id])
            //     ->asArray()
            //     ->all();

            $avgFees = self::getCollegeAvgFees($college->id, $course->id);

            $feesRange = '';
           
            if ($avgFees >= 500000) {
                $feesRange = '500001';
            } else {
                foreach (self::$feesRange as $fees) {
                    $feesArray = explode('-', $fees);
                    if ($avgFees > min($feesArray) && $avgFees < max($feesArray)) {
                        $feesRange = $fees;
                    }
                }
            }

            if ($avgFees) {
                $avgFees = (int) $avgFees;
                 $feesRangeData[] = $avgFees;
                $feesRange = $feesRange;
            } else {
                $avgFees = 0;
                $feesRange = 0;
            }

            $specializationArr = [];
            $coursesArr = [];
            if (!empty($specializationList)) {
                foreach ($specializationList as $spec) {
                    if (!empty($spec['slug'])) {
                        if ($spec['is_hons'] == 1) {
                            if (str_contains($course->slug, '-hons')) {
                                $arr = explode('-', $course->slug);
                                $specializationArr['isHons'][$arr[0] . '-' . $spec['slug'] . '-hons']['slug'] = $arr[0] . '-' . $spec['slug'] . '-hons';
                                $coursesArr['isHons'][$course->slug] = $course->slug;
                            } else {
                                $specializationArr['isHons'][$course->slug . '-' . $spec['slug'] . '-hons']['slug'] = $course->slug . '-' . $spec['slug'] . '-hons';
                                $coursesArr['isHons'][$course->slug . '-hons'] = $course->slug . '-hons';
                            }
                        } else {
                            $specializationArr['notHons'][$course->slug . '-' . $spec['slug']]['slug'] = $course->slug . '-' . $spec['slug'];
                            $coursesArr['notHons'][$course->slug] = $course->slug;
                        }
                    }
                }
            } else {
                $coursesArr['notHons'][$course->slug] = $course->slug;
            }

            if (!empty($coursesArr['notHons'])) {
                if ($avgFees != 0) {
                    self::$_avgCourseFees[$course->id] =  $avgFees;
                }
                $items[] = [
                    'course_id' => $course->id,
                    'course_name' => $course->name,
                    'course_short_name' => $course->short_name,
                    'course_slug' => array_pop($coursesArr['notHons']),
                    'stream_id' => $course->stream->id ?? '',
                    'stream_name' => $course->stream->name ?? '',
                    'stream_slug' => $course->stream->slug ?? '',
                    'specialization' => isset($specializationArr['notHons']) && !empty($specializationArr['notHons']) ? array_column($specializationArr['notHons'], 'slug') : [],
                    // 'degree' => array_column($degrees, 'degree') ?? [],
                    'degree' => $course->degree ?? [],
                    'type' => array_column($type, 'type') ?? [],
                    'mode' => array_column($mode, 'mode') ?? [],
                    // 'type' => !empty($type) ? array_unique($type) : [],
                    // 'mode' => !empty($mode) ? array_unique($mode) : [],
                    'avg_fees' => $avgFees,
                    'fees_range' => $feesRange
                ];
            }

            if (!empty($coursesArr['isHons'])) {
                $items[] = [
                    'course_id' => $course->id,
                    'course_name' => $course->name,
                    'course_short_name' => $course->short_name . ' Hons',
                    'course_slug' => array_pop($coursesArr['isHons']),
                    'stream_id' => $course->stream->id ?? '',
                    'stream_name' => $course->stream->name ?? '',
                    'stream_slug' => $course->stream->slug ?? '',
                    'specialization' => isset($specializationArr['isHons']) && !empty($specializationArr['isHons']) ? array_column($specializationArr['isHons'], 'slug') : [],
                    // 'degree' => array_column($degrees, 'degree') ?? [],
                    'degree' => $course->degree ?? [],
                    'type' => array_column($type, 'type') ?? [],
                    'mode' => array_column($mode, 'mode') ?? [],
                    // 'type' => !empty($type) ? array_unique($type) : [],
                    // 'mode' => !empty($mode) ? array_unique($mode) : [],
                    'avg_fees' => $avgFees,
                    'fees_range' => $feesRange,
                ];
            }
        }
           
        self::$minAvgFees = (!empty($feesRangeData) && count($feesRangeData)>1) ? min($feesRangeData) :0;
        self::$maxAvgFees = !empty($feesRangeData) ? max($feesRangeData) :0;
       
        return $items ?? [];
    }

    public function updateCollegeFilterSubPage(College $college)
    {
        if (empty($college)) {
            return [];
        }

        $model = DocumentsCollege::find()->where(['college_id' => $college->id])->one();

        if (empty($model)) {
            return [];
        }

        $subPages = $this->getSubpages((new CollegeService())->getMenu($college));
        $model->sub_pages = $subPages ?? [];
        $model->save();
    }

    public function getSubpages($subpages)
    {
        $excludePages = ['info', 'courses-fees', 'reviews', 'compare'];
        $items = [];
        foreach ($subpages as $key => $value) {
            if (in_array($key, $excludePages) || is_int($value)) {
                continue;
            }

            $items[$key] = $value;
        }

        return $items;
    }

    public function calculateAverage($array)
    {
        $items = [];
        foreach ($array as $ar) {
            if (isset($ar['avg_fees']) && is_numeric($ar['avg_fees']) && $ar['avg_fees'] > 0) {
                $items[] = $ar['avg_fees'];
            }
        }

        if (!empty($items)) {
            return array_sum($items) / count($items);
        } else {
            return 0;
        }
    }

    private function getCollegeCourseExamByCollegeId($collegeId)
    {
        // dd($collegeId);
        $query = new Query();
        $query->select('cce.exam_id')
            ->distinct('cce.exam_id')
            ->from('college_program cc')
            ->join('join', 'college_program_exam as cce')
            ->where('cce.college_program_id = cc.id')
            ->andWhere('cc.status = 1')
            ->andWhere(['cc.college_id' => $collegeId]);
            
        $examIdList =  ArrayHelper::getColumn($query->all(), 'exam_id');
        $exams = Exam::find()->select('slug')->where(['in', 'id', $examIdList])->all();
        // dd($exams);
        return ArrayHelper::getColumn($exams, 'slug');
    }

    public function updateFilterMapping(CollegeProgram $program)
    {
        $filterGroup = FilterGroup::find()->where(['name' => 'Specialization'])->one();
        if (!$filterGroup) {
            echo 'filter group specialization does not exist';
            return;
        }

        $collegeProgram = ProgramCourseMapping::find()->where(['program_id' => $program->program_id])->one();

        if (empty($collegeProgram)) {
            return false;
        }

        if ($collegeProgram->specialization_id == null) {
            return false;
        }

        if (!$collegeProgram->course->stream) {
            return false;
        }

        //exclude the phd
        // if ($collegeProgram->course_id == Course::EXCLUDE_PHD) {
        //     return false;
        // }

        $parent = Filter::find()->where(['slug' => $collegeProgram->course->stream->slug])->one();
        if (!$parent) {
            return false;
        }

        if ($collegeProgram->program->is_hons == 1) {
            if (str_contains($collegeProgram->course->slug, '-hons')) {
                $arr = explode('-', $collegeProgram->course->slug);
                $filterSlug = $arr[0] . '-' . $collegeProgram->specialization->slug . '-hons';
                $filterName = $program->course->short_name . ' ' . $program->specialization->display_name . ' Hons';
            } else {
                $filterSlug = $collegeProgram->course->slug . '-' . $collegeProgram->specialization->slug . '-hons';
                $filterName = $program->course->short_name . ' ' . $program->specialization->display_name . ' Hons';
            }
        } else {
            if (!empty($collegeProgram->specialization_id)) {
                $filterSlug = $collegeProgram->course->slug . '-' . $collegeProgram->specialization->slug;
                $filterName = $program->course->short_name . ' ' . $program->specialization->display_name;
            }
        }

        $model = Filter::find()->where(['slug' => $filterSlug])->one();
        if (!$model) {
            $model = new Filter();
        }

        $model->filter_group_id = $filterGroup->id;
        $model->name = $filterName;
        // $model->name = $collegeProgram->course->short_name . ' ' . $collegeProgram->specialization->display_name;
        $model->slug = $filterSlug;
        $model->parent_id = $parent->id;
        $model->type = 'Checkbox';
        $model->status = Filter::STATUS_ACTIVE;

        if ($model->save()) {
            echo "{$model->id} \t {$model->name} \t {$collegeProgram->program->slug} \n";
        } else {
            print_r($model->getErrors());
        }
    }

    public static function updateCollegeFilterExam($collegeId)
    {
        $exams = self::getCollegeCourseExamByCollegeId($collegeId);
        $model = DocumentsCollege::find()->where(['college_id' => $collegeId])->one();
        if (empty($model)) {
            return [];
        }
        if (empty($model->exams)) {
            $model->exams = $exams;
            $model->save();
        } else {
            $checkExam = in_array($exams, $model->exams);
            if ($checkExam !== true) {
                $model->exams = $exams;
                $model->save();
            }
        }
    }

    public function getProgramType($courseId, $collegeId)
    {
        $query = new Query();
            $query->select('p.type')
            ->distinct('p.type')
            ->from(Program::tableName() . ' as p')
            ->innerJoin(CollegeProgram::tableName() . ' as cp', 'cp.program_id = p.id')
            ->where(['cp.course_id' => $courseId])
            ->andWhere(['cp.college_id' => $collegeId])
            ->andWhere(['cp.status' => Program::STATUS_ACTIVE]);
            
        $type = $query->all();

        return $type ?? [];
    }

    public function getProgramMode($courseId, $collegeId)
    {
        $query = new Query();
            $query->select('p.mode')
            ->distinct('p.mode')
            ->from(Program::tableName() . ' as p')
            ->innerJoin(CollegeProgram::tableName() . ' as cp', 'cp.program_id = p.id')
            ->where(['cp.course_id' => $courseId])
            ->andWhere(['cp.college_id' => $collegeId])
            ->andWhere(['cp.status' => Program::STATUS_ACTIVE]);

        $type = $query->all();

        return $type ?? [];
    }

    public function getSpecialization($courseId, $collegeId)
    {
        $query = new Query();
        $query->select(['s.slug', 'p.is_hons'])
            ->from(CollegeProgram::tableName() . ' as cp')
            ->innerJoin(ProgramCourseMapping::tableName() . ' as pc', 'pc.program_id = cp.program_id')
            ->innerJoin(Program::tableName() . ' as p', 'p.id = pc.program_id')
            ->innerJoin(SpecializationNew::tableName() . ' s', 's.id = pc.specialization_id')
            ->where(['cp.course_id' => $courseId])
            ->andWhere(['cp.college_id' => $collegeId])
            ->andWhere(['cp.status' => Specialization::STATUS_ACTIVE]);

        $data = $query->all();

        return $data ?? [];
    }

    public function updateCourseFilter(CollegeProgram $program)
    {
        $filterGroup = FilterGroup::find()->where(['name' => 'Courses'])->one();
        if (!$filterGroup) {
            echo 'Filter group course does not exist';
            return;
        }

        $collegeProgram = ProgramCourseMapping::find()->where(['program_id' => $program->program_id])->one();

        if (empty($collegeProgram)) {
            return false;
        }

        if (!$collegeProgram->course->stream) {
            return false;
        }

        $parent = Filter::find()->where(['slug' => $collegeProgram->course->stream->slug])->one();
        if (!$parent) {
            return false;
        }

        $filterSlug = $collegeProgram->course->slug;
        $filterName = $collegeProgram->course->short_name;

        if ($collegeProgram->specialization_id !== null) {
            $filterSlug = $filterSlug . '-' . $collegeProgram->specialization->slug;
            $filterName = $filterName . ' ' . $collegeProgram->specialization->display_name;
        }

        if ($collegeProgram->program->is_hons == 1) {
            $filterSlug = $filterSlug . '-hons';
            $filterName = $filterName . ' Hons';
        }

        $filterSlugCheck = Filter::find()->where(['slug' => $filterSlug])->one();
        if ($filterSlugCheck) {
            return false;
        }

        $model = new Filter();
        $model->filter_group_id = $filterGroup->id;
        $model->name = $filterName;
        $model->slug = $filterSlug;
        $model->parent_id = $parent->id;
        $model->type = 'Checkbox';
        $model->status = Filter::STATUS_ACTIVE;
        if ($model->save()) {
            echo "{$model->id} \t {$model->name} \t {$collegeProgram->program->slug} \n";
        } else {
            print_r($model->getErrors());
        }
    }

    public function updateElasticCollegeFilter(College $college)
    {

        
        $collegeService = new CollegeService();
        $indexedFeatures = [];
        $instituteTypeName = '';

        $rows = $this->searchById($college->slug);
        $collegerows = $this->searchCollegeById($college->id);
        
        $primaryID = null;
        $isExist = false;
        if (!empty($rows['hits']['hits']) ||  !empty($collegerows['hits']['hits'])) {
            if (!empty($rows['hits']['hits'][0]['_id'])) {
                $primaryID = $rows['hits']['hits'][0]['_id'];
            } elseif (!empty($collegerows['hits']['hits'][0]['_id'])) {
                $primaryID = $collegerows['hits']['hits'][0]['_id'];
            }
        }
       
        try {
            $model = CollegeElastic::get($primaryID);
            if (!$model) {
                $model = new CollegeElastic;
                $model->setPrimaryKey($primaryID);
            } else {
                $isExist = true;
            }
        } catch (\Exception $e) {
            $model = new CollegeElastic ;
            $model->setPrimaryKey($primaryID);
        }

        $review = (new ReviewService)->getCollegeReviewsFiltePage($college->id);
        $revCategoryRating = (new ReviewService)->getCollegeBasedCategoryRating($college->id);
        $reviewCount = (new ReviewService)->getReviewRatingCount($college->id);

        $subPages = $this->getSubpages($collegeService->getMenu($college));
        $allFeatures = $collegeService->getFeatures($college);
        if (!empty($allFeatures)) {
            $indexedFeatures = ArrayHelper::index($collegeService->getFeatures($college), null, 'featureGroupName');
            foreach ($indexedFeatures as $key => $value) {
                $indexedFeatures[$key] = ArrayHelper::getColumn($indexedFeatures[$key], 'featureSlug', false);
                if (isset($value[0]['featureSlug']) && $value[0]['featureSlug'] == 'institution-type') {
                    $instituteTypeName = $value[0]['value'];
                }
            }
        }
        $nirfRankData = CollegeRankingPublisher::find()
                         ->select(['id'])
                         ->where(['slug'=>'nirf'])
                         ->one();
       // echo "<pre>"; print_r($nirfRankData);
        $ovellNirfData = OtherRankings::find()
                            ->select(['id','slug'])
                            ->where(['publisher_id'=>$nirfRankData->id])
                            ->andWhere(['slug'=>'overall'])
                            ->one();
                           // echo "<pre>"; print_r($ovellNirfData);
           $rankData =    CollegeRankings::find()
                            ->select(['rank'])
                            ->where(['publisher_id'=>$nirfRankData->id])
                            ->andWhere(['criteria_id'=>$ovellNirfData->id])
                            ->andWhere(['college_id'=>$college->id])
                            ->andWhere(['year'=>date('Y')])
                            ->one();
                           // echo "<pre>"; print_r($rankData);

        $features = $collegeService->getFeatures($college);
        $accridtions =  $collegeService->getFeatureStringValues($features, 'Accreditations');
        $model->college_id = $college->id;
        $model->name = $college->name;
        $model->display_name = $college->display_name;
        $model->slug = $college->slug;
        $model->logo = $college->logo_image ? $college->logo_image : $college->getOldAttribute('logo_image');
        $model->banner_image = $college->cover_image ? $college->cover_image : $college->getOldAttribute('cover_image');
        $model->city_id = $college->city->id;
        $model->city_name = $college->city->name;
        $model->city_slug = trim($college->city->slug);
        $model->state_id = $college->city->state->id;
        $model->state_name = $college->city->state->name;
        $model->state_slug = $college->city->state->slug;
        $model->course =  $this->getCourseLists($college);
        $model->avgFees = $this->calculateAvgFeesElastic($college);
        $model->review = !empty($review) ? ContentHelper::htmlDecode($review['content']) : '';
        $model->rev_category_rating = $revCategoryRating ?? [];
        $model->review_count = !empty($reviewCount) ? $reviewCount['reviewCount'] : '';
        $model->rating = !empty($revCategoryRating) ? CollegeHelper::getTotalRating($revCategoryRating) : '';
        $model->sub_pages = (array)$subPages ?? [];
        $model->exams = $this->getCollegeCourseExamByCollegeId($college->id) ?? [];
        $model->type = $instituteTypeName ?? '';
        $model->is_popular = $college->is_popular ?? '';
        $model->rank = $college->rank ?? 999999;
        $model->is_sponsored = $college->is_sponsored ?? '';
        $model->status = $college->status;
        $model->courseCount = $this->collegeCourseCount($college, 'courses-fees', ['pgdm', 'diploma', 'certificate', 'fellowship-programme', 'other'], '', '', '');
        $model->accreditations = $accridtions ?? '';
        $model->minAvgFees = self::$minAvgFees ?? 0;
        $model->maxAvgFees = self::$maxAvgFees ?? 0;
        if (!empty($rankData)) {
            $model->nirf_rank_overall =  $rankData->rank ?? null;
        }
        if ($college->is_sponsored) {
            $model->sponser_college_view =  $college->sponser_college_view ?? null;
        } else {
            $model->sponser_college_view =   null;
        }
        
        if (isset($indexedFeatures['Approvals'])) {
            $model->approvals = $indexedFeatures['Approvals'];
        }
        if (!empty($college->position)) {
            $model->position = $college->position;
        } else {
            $model->position = 0;
        }
        if ($college->type == (College::TYPE_COLLEGE || College::TYPE_INSTITUTES) && !empty($college->parent)) {
            $model->affiliated_by = $college->parent->slug ?? '';
        }
         //dd($model);
        
        try {
            if (!$isExist) {
                $model->insert();
                echo "{$model->college_id} \t {$model->name} \n";
            } else {
                $result = $model->update();
               // dd($result);
                echo "{$model->college_id} \t {$model->name} \n";
            }
        } catch (\Exception $e) {
            print_r($e);
        }
    }

    public function searchById($slug)
    {
        if (empty($slug)) {
            return '';
        }

        $rows = [];
        $query = new ElasticQuery();
        $filters = [];
        $filters['bool']['must'][]['match']['slug']  = $slug;
        $query->query($filters);
        $query->from(CollegeElastic::index(), CollegeElastic::type())->limit(1);
        $command = $query->createCommand();
        $rows = $command->search();

        return $rows;
    }


    public function searchCollegeById($college_id)
    {
        if (empty($college_id)) {
            return '';
        }

        $rows = [];
        $query = new ElasticQuery();
        $filters = [];
        $filters['bool']['must'][]['match']['college_id']  = $college_id;
        $query->query($filters);
        $query->from(CollegeElastic::index(), CollegeElastic::type())->limit(1);
        $command = $query->createCommand();
        $rows = $command->search();

        return $rows;
    }

    public function collegeCourseCount(College $college, $page = null, $exclude = [], $entity = '', $degree = '', $action = '')
    {
        $query = DocumentsCollegeProgram::find()
            ->where(['college_id' => $college->id])
            ->andWhere(['status' => CollegeCourse::STATUS_ACTIVE])
            ->orderBy(['course_position' => SORT_ASC]);

        if (!empty($entity) && $entity == 'ci' && !empty($degree)) {
            $query->andWhere(['degree' => $degree]);
        }

        $data = $query->all();

        $items = [];
        foreach ($data as $d) {
            //exclude the Course contain + symbol from page
            if (!empty($page) && strpos($d['course'], '+') !== false) {
                continue;
            }

            if (!empty($exclude)) {
                if (in_array($d['course_slug'], $exclude)) {
                    continue;
                }

                // if (in_array($d['course_slug'], ['pgdm', 'diploma', 'certificate', 'fellowship-programme']) && !in_array($page, ['courses-fees', 'info'])) {
                //     continue;
                // }
            }

            $items[$d['course']] = [
                'id' => $d['college_program_id'],
                'program_id' => $d['program_id'],
                'course_id' => $d['course_id'] ?? '',
            ];
        }
        if ($action!='') {
            return $items;
        }
         return (count($items) > 0) ?   count($items) : 0;
    }
    
    public function calculateAvgFeesElastic($college)
    {
        if (!empty(self::$_avgCourseFees)) {
            return array_sum(self::$_avgCourseFees) / count(self::$_avgCourseFees);
        } else {
            return 0;
        }
    }
}
