<?php

namespace common\event;

use common\helpers\CollegeHelper;
use common\models\CollegeContent;
use common\models\Course;
use common\models\documents\Review as DocumentsReview;
use common\models\Review;
use common\models\ReviewElastic;
use common\services\ReviewService;

class ReviewFilterEvent
{
    /**
     * Update ReviewFilter Collection in Mongodb.
     * Call this function when college details updated.
     *
     * @param Review $review \common\models\Review
     * @return Object | Null
     */
    public function updateReviews(Review $review)
    {
        $reviewService = new ReviewService();

        $model = DocumentsReview::find()->where(['review_id' => $review->id])->one();

        $reviewCategoryRatings = $reviewService->getReviewCategoryRating($review->id);
        if (!$model) {
            $model = new DocumentsReview();
        }

        $model->review_id = $review->id;
        $model->college_id = $review->college_id ?? '';
        $model->name = $review->college->name ?? '';
        $model->display_name = $review->college->display_name ?? '';
        $model->slug = $review->college->slug ?? '';
        $model->city_id = !empty($review->college) ? $review->college->city->id : '';
        $model->city_name = !empty($review->college) ? $review->college->city->name : '';
        $model->city_slug = !empty($review->college) ? trim($review->college->city->slug) : '';
        $model->state_id = !empty($review->college) ? $review->college->city->state->id : '';
        $model->state_name = !empty($review->college) ? $review->college->city->state->name : '';
        $model->state_slug = !empty($review->college) ? $review->college->city->state->slug : '';
        $model->course = $this->getCourse($review->course_id);
        $model->review = $this->getReviews($review);
        $model->review_overall_rating = !empty($reviewCategoryRatings) ? CollegeHelper::getTotalRating($reviewCategoryRatings) : '';
        $model->batch = $review->admission_year ?? '';
        $model->review_created_at = $review->created_at ?? '';
        $model->status = $review->status;

        if ($model->save()) {
            // echo "{$model->college_id} \t {$model->review_id} \n";
        } else {
            print_r($model->getErrors());
        }
    }

    public function updateReviewElastic(Review $review)
    {
        try {
            $reviewService = new ReviewService();

            $reviewCategoryRatings = $reviewService->getReviewCategoryRating($review->id);

            // Calculate overall rating
            $overallRating = CollegeHelper::getTotalRating($reviewCategoryRatings);

            // Calculate batch from admission year
            $batch = $review->admission_year ? (int)$review->admission_year : 0;

            // Create or update Elasticsearch document
            try {
                $reviewElastic = ReviewElastic::get($review->id);
            } catch (\Exception $e) {
                $reviewElastic = null;
            }

            if (!$reviewElastic) {
                $reviewElastic = new ReviewElastic();
                $reviewElastic->_id = $review->id;
            }

            // Set attributes
            $reviewElastic->review_id = $review->id;
            $reviewElastic->college_id = $review->college_id ?? '';
            $reviewElastic->college_name = $review->college->name ?? '';
            $reviewElastic->college_slug = $review->college->slug ?? '';
            $reviewElastic->college_display_name = $review->college->display_name ?? '';
            $reviewElastic->college_city_id = $review->college->city->id ?? '';
            $reviewElastic->college_city_name = $review->college->city->name ?? '';
            $reviewElastic->city_slug = $review->college->city->slug ?? '';

            $reviewElastic->college_state_id = !empty($review->college) ? $review->college->city->state->id : '';
            $reviewElastic->college_state_name = !empty($review->college) ? $review->college->city->state->name : '';
            $reviewElastic->state_slug = !empty($review->college) ? $review->college->city->state->slug : '';

            $reviewElastic->course_id = $review->course_id ?? '';
            $reviewElastic->course_name = $review->course->name ?? '';
            $reviewElastic->course_slug = $review->course->slug ?? '';

            $reviewElastic->stream_id = $review->course->stream->id ?? '';
            $reviewElastic->stream_name = $review->course->stream->name ?? '';
            $reviewElastic->stream_slug = $review->course->stream->slug ?? '';

            $reviewElastic->review_slug = $review->slug ?? '';
            $reviewElastic->student_name = $review->user->name ?? '';

            $reviewElastic->review_overall_rating = $overallRating;

            $reviewElastic->batch = $batch;

            $createdAt = $review->created_at;
            if ($createdAt instanceof \yii\db\Expression) {
                // Replace with current timestamp
                $createdAt = date('Y-m-d H:i:s');
            } elseif ($createdAt instanceof \DateTime) {
                // Format DateTime object
                $createdAt = $createdAt->format('Y-m-d H:i:s');
            } elseif (is_numeric($createdAt)) {
                // Handle Unix timestamp
                $createdAt = date('Y-m-d H:i:s', $createdAt);
            }

            $reviewElastic->review_created_at = $createdAt ?: '';

            $reviewElastic->status = $review->status;

            if ($reviewElastic->save()) {
                // echo "Review {$review->id} indexed successfully.\n";
            }
        } catch (\Exception $e) {
            echo "Error indexing review {$review->id}: " . $e->getMessage() . "\n";
        }
    }

    public function getCourse($courseId)
    {
        $course = Course::find()->where(['id' => $courseId])->one();

        if (empty($course)) {
            return [];
        }

        $items = [
            'course_id' => $course->id ?? '',
            'course_name' => $course->name ?? '',
            'course_slug' => $course->slug ?? '',
            'stream_id' => $course->stream->id ?? '',
            'stream_name' => $course->stream->name ?? '',
            'stream_slug' => $course->stream->slug ?? '',
        ];

        return $items ?? [];
    }

    public function getReviews($review)
    {
        if (empty($review)) {
            return [];
        }

        $items = [
            'review_slug' => !empty($review->slug) ? $review->slug : '',
            'student_name' => !empty($review->user) ? $review->user->name : ''
        ];

        return $items ?? [];
    }

    public function updateReviewOverallRating($reviewContent)
    {
        $reviewService = new ReviewService();
        $reviewCategoryRatings = $reviewService->getReviewCategoryRating($reviewContent->review_id);

        $model = DocumentsReview::find()->where(['review_id' => $reviewContent->review_id])->one();

        if (empty($model)) {
            return [];
        }

        $model->review_overall_rating = !empty($reviewCategoryRatings) ? CollegeHelper::getTotalRating($reviewCategoryRatings) : '';

        if ($model->save()) {
        } else {
            print_r($model->getErrors());
        }
    }

    //update college subpage reviews to active
    public function updateCollegeReviewSubpage($collegeId, $status, $supage = 'reviews')
    {
        $reviewSubPage = CollegeContent::find()
            ->where(['entity_id' => $collegeId])
            ->andWhere(['sub_page' => $supage])
            ->one();

        if (empty($reviewSubPage)) {
            return '';
        }

        if ($status == 1 && $reviewSubPage->status == CollegeContent::STATUS_INACTIVE) {
            $reviewSubPage->status = CollegeContent::STATUS_ACTIVE;
            if ($reviewSubPage->save()) {
            } //do not remove this
        }
    }
}
