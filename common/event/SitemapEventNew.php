<?php

namespace common\event;

use common\helpers\DataHelper;
use common\models\CollegeContent;
use common\models\documents\Sitemap;
use common\helpers\CollegeHelper;
use common\models\Article;
use common\models\BoardContent;
use common\models\Program;
use common\models\Course;
use common\models\CareerContent;
use common\models\Category;
use common\models\CourseContent;
use common\models\ExamContent;
use common\models\NewsSubdomain;
use common\models\OlympiadContent;
use common\models\Scholarship;
use common\models\ScholarshipContent;
use frontend\services\ExamService;
use common\models\SitemapUpdate;
use frontend\helpers\Url;

class SitemapEventNew
{
    const DOMAIN = 'https://www.getmyuni.com';
    const CHANGE_FREQ_DAILY = 'daily';
    const CHANGE_FREQ_MONTHLY = 'monthly';


    /** Update the sitemap collection
     * @param $boardId Board ID
     * @param $slug  board page slug
     */
    public function generateBoardSitemap($boardId, $pageSlug = null)
    {
        $query = BoardContent::find()->with('board')->where(['board_id' => $boardId]);

        if (!empty($pageSlug)) {
            $boardPages = $query->andWhere(['page_slug' => $pageSlug]);
        }

        $boardPages = $query->all();

        if (empty($boardPages)) {
            return false;
        }

        foreach ($boardPages as $page) {
            if ($page->page_slug == 'overview') {
                if (empty($page->board->slug)) {
                    return false;
                }

                if ($page->lang_code == 2) {
                    $url = 'hi/boards/' . $page->board->slug;
                } else {
                    $url = 'boards/' . $page->board->slug;
                }
            } else {
                if ($page->parent_id != null) {
                    $boardContent = BoardContent::findOne($page->parent_id);
                    if ($boardContent->page_slug == 'supplementary') {
                        if ($page->lang_code == 2) {
                            $url = 'hi/boards/' . $page->board->slug . '-' . $boardContent->page_slug . '-' . $page->page_slug;
                        } else {
                            $url = 'boards/' . $page->board->slug . '-' . $boardContent->page_slug . '-' . $page->page_slug;
                        }
                    } else {
                        if ($page->lang_code == 2) {
                            $url = 'hi/boards/' . $page->board->slug . '-' . $page->page_slug . '-' . $boardContent->page_slug;
                        } else {
                            $url = 'boards/' . $page->board->slug . '-' . $page->page_slug . '-' . $boardContent->page_slug;
                        }
                    }
                } else {
                    if ($page->lang_code == 2) {
                        $url = 'hi/boards/' . $page->board->slug . '-' . $page->page_slug;
                    } else {
                        $url = 'boards/' . $page->board->slug . '-' . $page->page_slug;
                    }
                }
            }

            $model = SitemapUpdate::find()
                ->where(['slug' => $url])
                ->andWhere(['sub_page' => $page->page_slug])
                ->andWhere(['lang_code' => $page->lang_code])
                ->one();

            $status = ($page->board->status  == SitemapUpdate::STATUS_INACTIVE) ? SitemapUpdate::STATUS_INACTIVE : (($page->status == SitemapUpdate::STATUS_ACTIVE) ?  SitemapUpdate::STATUS_ACTIVE : SitemapUpdate::STATUS_INACTIVE);
            $this->saveSitemapUpdate($model, Sitemap::ENTITY_BOARD, $url, $page->page_slug, $status, $page->updated_at, null, $page->lang_code);
        }
    }

    public function generateCollegeSitemap($collegeId, $pageSlug = null)
    {
        $query = CollegeContent::find()->with('college')->where(['entity_id' => $collegeId]);
        //->andWhere(['status' => 1]);
        if (!empty($pageSlug)) {
            $collegePages = $query->andWhere(['sub_page' => $pageSlug]);
        }
        $collegePages = $query->all();
        if (!empty($collegePages)) {
            foreach ($collegePages as $page) {
                if ($page->parent_id != null) {
                    $collegeContent = CollegeContent::find()->where(['id' => $page->parent_id])->one();
                    $url = CollegeHelper::collegeDropDownUrlFormate($page->college->slug, $page->sub_page, $collegeContent->sub_page);
                } else {
                    $url = CollegeHelper::collegeUrlFormate($page->college->slug, $page->sub_page);
                }

                $url = 'college/' . $url;

                $model = SitemapUpdate::find()
                    ->where(['slug' => $url])
                    ->andWhere(['sub_page' => $page->sub_page])
                    ->one();

                $status = ($page->college->status  == SitemapUpdate::STATUS_INACTIVE) ? SitemapUpdate::STATUS_INACTIVE : (($page->status == SitemapUpdate::STATUS_ACTIVE) ?  SitemapUpdate::STATUS_ACTIVE : SitemapUpdate::STATUS_INACTIVE);

                $this->saveSitemapUpdate($model, Sitemap::ENTITY_COLLEGE, $url, $page->sub_page, $status, $page->updated_at);
            }
        }
    }

    /** Update the sitemap collection
     * @param $examId Exam ID
     * @param $slug Exam page slug
     */
    public function generateExamSitemap($examId, $pageSlug = null)
    {
        $query = ExamContent::find()->with('exam')->where(['exam_id' => $examId]);

        if (!empty($pageSlug)) {
            $examPages = $query->andWhere(['sub_page' => $pageSlug]);
        }

        $examPages = $query->all();

        if (empty($examPages)) {
            return false;
        }

        foreach ($examPages as $page) {
            if ($page->parent_id != null) {
                $parentSlug = ExamContent::findOne($page->parent_id);
                $url = self::toExamDetailSubPage($page->exam->slug, $parentSlug->slug, $page->slug, DataHelper::getLangCode($page->lang_code), true);
            } else {
                $url = self::toExamDetailSubPage($page->exam->slug, $page->slug, null, DataHelper::getLangCode($page->lang_code));
            }

            $model = SitemapUpdate::find()
                ->where(['slug' => $url])
                ->andWhere(['sub_page' => $page->slug])
                ->one();

            $status = ($page->exam->status  == SitemapUpdate::STATUS_INACTIVE) ? SitemapUpdate::STATUS_INACTIVE : (($page->status == SitemapUpdate::STATUS_ACTIVE) ?  SitemapUpdate::STATUS_ACTIVE : SitemapUpdate::STATUS_INACTIVE);

            $this->saveSitemapUpdate($model, SitemapUpdate::ENTITY_EXAM, $url, $page->slug, $status, $page->updated_at);
        }
    }

    public static function toExamDetailSubPage($slug, $pageSlug = null, $dropSlug = null, $lang_code = '', $parent = false)
    {
        $base = 'exams/';
        $langPrefix = (!empty($lang_code) && $lang_code !== 'en') ? "/$lang_code" : '';
        $url = $langPrefix . $base;

        if (!$parent) {
            if ($pageSlug === 'overview') {
                return $url . $slug;
            }
            return $url . $slug . '-' . $pageSlug;
        }

        // Logic for child pages
        if ($pageSlug === 'syllabus') {
            return $url . $slug . '-' . $dropSlug . '-syllabus';
        }

        if ($pageSlug === 'cut-off' && $dropSlug !== 'qualifying-marks') {
            return $url . $slug . '-' . $pageSlug . '-' . $dropSlug;
        }

        if (in_array($pageSlug, ['answer-key', 'exam-pattern'])) {
            return $url . $slug . '-' . $dropSlug . '-' . $pageSlug;
        }

        return $url . $slug . '-' . $dropSlug;
    }

    public function updateScholarShipSitemap($scholarshipId, $pageSlug = null)
    {
        $query = ScholarshipContent::find()->with('scholarship')->where(['scholarship_id' => $scholarshipId]);

        if (!empty($pageSlug)) {
            $scholarshipPages = $query->andWhere(['page' => $pageSlug]);
        }

        $scholarshipPages = $query->all();

        if (empty($scholarshipPages)) {
            return false;
        }

        foreach ($scholarshipPages as $page) {
            if ($page->page == 'overview') {
                $url = 'scholarships/' . $page->scholarship->slug;
            } else {
                $url = 'scholarships/' . $page->scholarship->slug . '-' . $page->page;
            }

            $model = SitemapUpdate::find()
                ->where(['slug' => $url])
                ->andWhere(['sub_page' => $page->page])
                ->one();

            $status = ($page->scholarship->status  == SitemapUpdate::STATUS_INACTIVE) ? SitemapUpdate::STATUS_INACTIVE : (($page->status == SitemapUpdate::STATUS_ACTIVE) ?  SitemapUpdate::STATUS_ACTIVE : SitemapUpdate::STATUS_INACTIVE);
            $this->saveSitemapUpdate($model, SitemapUpdate::ENTITY_SCHOLARSHIP, $url, $page->page, $status, $page->updated_at);
        }
    }

    public function generateCareerSitemap($careerId, $pageSlug = null)
    {
        $query = CareerContent::find()->with('career')->where(['career_id' => $careerId]);

        if (!empty($pageSlug)) {
            $careerPages = $query->andWhere(['page' => $pageSlug]);
        }

        $careerPages = $query->all();

        if (empty($careerPages)) {
            return false;
        }

        foreach ($careerPages as $page) {
            if ($page->page == 'overview') {
                $url = 'careers/' . $page->career->slug;
            } else {
                $url = 'careers/' . $page->career->slug . '-' . $page->page;
            }

            $model = SitemapUpdate::find()
                ->where(['slug' => $url])
                ->andWhere(['sub_page' => $page->page])
                ->one();

            $status = ($page->career->status  == SitemapUpdate::STATUS_INACTIVE) ? SitemapUpdate::STATUS_INACTIVE : (($page->status == SitemapUpdate::STATUS_ACTIVE) ?  SitemapUpdate::STATUS_ACTIVE : SitemapUpdate::STATUS_INACTIVE);

            $this->saveSitemapUpdate($model, SitemapUpdate::ENTITY_CAREER, $url, $page->page, $status, $page->updated_at);
        }
    }

    public function generateOlympiadSitemap($olympiadId, $pageSlug = null)
    {
        $query = OlympiadContent::find()->with('olympiad')->where(['olympiad_id' => $olympiadId]);

        if (!empty($pageSlug)) {
            $olympiadPages = $query->andWhere(['page' => $pageSlug]);
        }

        $olympiadPages = $query->all();

        if (empty($olympiadPages)) {
            return false;
        }

        foreach ($olympiadPages as $page) {
            if ($page->page == 'overview') {
                $url = 'olympiad/' . $page->olympiad->slug;
            } else {
                $url = 'olympiad/' . $page->olympiad->slug . '-' . $page->page;
            }

            $model = SitemapUpdate::find()
                ->where(['slug' => $url])
                ->andWhere(['sub_page' => $page->page])
                ->one();

            $status = ($page->olympiad->status  == SitemapUpdate::STATUS_ACTIVE) ? SitemapUpdate::STATUS_ACTIVE : (($page->status == SitemapUpdate::STATUS_ACTIVE) ?  SitemapUpdate::STATUS_ACTIVE : SitemapUpdate::STATUS_INACTIVE);

            $this->saveSitemapUpdate($model, SitemapUpdate::ENTITY_OLYMPIAD, $url, $page->page, $status, $page->updated_at);
        }
    }

    public function generateCourseSitemap($courseId, $pageSlug = null)
    {
        $query = CourseContent::find()->with('course')->where(['course_id' => $courseId]);
        if (!empty($pageSlug)) {
            $coursePages = $query->andWhere(['page' => $pageSlug]);
        }
        $coursePages = $query->all();

        if (empty($coursePages)) {
            return false;
        }
        foreach ($coursePages as $page) {
            if ($page->parent_id != null) {
                $final = preg_replace('#[ -]+#', '-', $page->page);
                $courseContent = CourseContent::find()->where(['id' => $page->parent_id])->one();
                $url = $page->course->slug . '-' . $courseContent->page . '/' . strtolower($final);
            } else {
                if ($page->page == 'about') {
                    $url = $page->course->slug . '-course';
                } else {
                    $url = $page->course->slug . '-' . $page->page;
                }
            }

            $model = SitemapUpdate::find()
                ->where(['slug' => $url])
                ->andWhere(['sub_page' => $pageSlug])
                ->one();

            $status = ($page->course->status  == SitemapUpdate::STATUS_INACTIVE) ? SitemapUpdate::STATUS_INACTIVE : (($page->status == SitemapUpdate::STATUS_ACTIVE) ?  SitemapUpdate::STATUS_ACTIVE : SitemapUpdate::STATUS_INACTIVE);

            $this->saveSitemapUpdate($model, Sitemap::ENTITY_COURSE, $url, $page->page, $status, $page->updated_at);
        }
    }

    public function generateNewsSitemap($newsId, $status, $updatedAt = null, $publishedAt = null)
    {
        $news = NewsSubdomain::findOne($newsId);

        $statusVale = $status;

        if ($news->status == 1) {
            $statusVale = $status;
        }

        $model = SitemapUpdate::find()->where(['slug' => $news->slug])->one();

        $this->saveSitemapUpdate($model, Sitemap::ENTITY_NEWS, $news->slug, 'news', $statusVale, $updatedAt, $publishedAt);
    }

    /**
     * Update Article to sitemap collection
     * $articleId Article ID
     */
    public function updateArticleSitemap($articleId)
    {
        $article = Article::findOne($articleId);

        if (empty($article)) {
            return false;
        }

        if ($article->lang_code == 2) {
            $url = 'hi/articles/' . $article->slug;
        } else {
            $url = 'articles/' . $article->slug;
        }

        $model = SitemapUpdate::find()
            ->where(['slug' => $url])
            ->andWhere(['lang_code' => $article->lang_code])
            ->andWhere(['entity' => SitemapUpdate::ENTITY_ARTICLE])
            ->one();

        $this->saveSitemapUpdate($model, Sitemap::ENTITY_ARTICLE, $url, 'articles', $article->status, $article->updated_at, $article->published_at, $article->lang_code);
    }

    public function updateSingleBoardPage($boardId, $status, $pageSlug = null)
    {
        $query = BoardContent::find()->with('board')->where(['board_id' => $boardId]);
        if (!empty($pageSlug)) {
            $boardPages = $query->andWhere(['page_slug' => $pageSlug]);
        }
        $boardPages = $query->one();

        if (empty($boardPages)) {
            return false;
        }


        if ($boardPages->page_slug == 'overview') {
            if (empty($page->board->slug)) {
                return false;
            }

            $url = 'boards/' . $boardPages->board->slug;
        } else {
            if ($boardPages->parent_id != null) {
                $boardContent = BoardContent::findOne($boardPages->parent_id);
                if ($boardContent->page_slug == 'supplementary') {
                    $url = 'boards/' . $boardPages->board->slug . '-' . $boardContent->page_slug . '-' . $boardPages->page_slug;
                } else {
                    $url = 'boards/' . $boardPages->board->slug . '-' . $boardPages->page_slug . '-' . $boardContent->page_slug;
                }
            } else {
                $url = 'boards/' . $boardPages->board->slug . '-' . $boardPages->page_slug;
            }
        }

        $model = SitemapUpdate::find()
            ->where(['slug' => $url])
            ->andWhere(['sub_page' => $boardPages->page_slug])
            ->one();

        $status = ($boardPages->board->status  == SitemapUpdate::STATUS_ACTIVE) ? SitemapUpdate::STATUS_ACTIVE : (($boardPages->status == SitemapUpdate::STATUS_ACTIVE) ?  SitemapUpdate::STATUS_ACTIVE : SitemapUpdate::STATUS_INACTIVE);
        $this->saveSitemapUpdate($model, Sitemap::ENTITY_BOARD, $url, $boardPages->page_slug, $status, $boardPages->updated_at);
    }

    public function updateCollegeSitemap($collegeId, $status, $slug = null)
    {
        $query = CollegeContent::find()->with('college')->where(['entity_id' => $collegeId]);
        if (!empty($slug)) {
            $collegePages = $query->andWhere(['sub_page' => $slug]);
        }
        $collegePages = $query->all();
        if (!empty($collegePages)) {
            foreach ($collegePages as $page) {
                if ($page->parent_id != null) {
                    $collegeContent = CollegeContent::find()->where(['id' => $page->parent_id])->one();
                    $url = CollegeHelper::collegeDropDownUrlFormate($page->college->slug, $page->sub_page, $collegeContent->sub_page);
                } else {
                    $url = CollegeHelper::collegeUrlFormate($page->college->slug, $page->sub_page);
                }

                $url = 'college/' . $url;

                $model = SitemapUpdate::find()
                    ->where(['slug' => $url])
                    ->andWhere(['sub_page' => $page->sub_page])
                    ->one();

                $status = ($page->college->status  == SitemapUpdate::STATUS_ACTIVE) ? SitemapUpdate::STATUS_ACTIVE : (($page->status == SitemapUpdate::STATUS_ACTIVE) ?  SitemapUpdate::STATUS_ACTIVE : SitemapUpdate::STATUS_INACTIVE);

                $this->saveSitemapUpdate($model, Sitemap::ENTITY_COLLEGE, $url, $page->sub_page, $status, $page->updated_at);
            }
        }
    }

    public function updateExamSitemap($examId, $slug = null)
    {
        $examService = new ExamService();

        $query = ExamContent::find()->with('exam')->where(['exam_id' => $examId]);
        if (!empty($slug)) {
            $examPages = $query->andWhere(['slug' => $slug]);
        }
        $examPages = $query->all();

        if (empty($examPages)) {
            return false;
        }

        foreach ($examPages as $page) {
            if ($page->parent_id != null) {
                $parentSlug = ExamContent::findOne($page->parent_id);
                $url = 'exams/' . $examService->examUrlFormate($page->exam->slug, $parentSlug->slug, $page->slug);
            } else {
                $url =  'exams/' . $examService->examUrlFormate($page->exam->slug, $page->slug);
            }

            $model = SitemapUpdate::find()
                ->where(['slug' => $url])
                ->andWhere(['sub_page' => $page->slug])
                ->one();

            $status = ($page->exam->status  == SitemapUpdate::STATUS_ACTIVE) ? SitemapUpdate::STATUS_ACTIVE : (($page->status == SitemapUpdate::STATUS_ACTIVE) ?  SitemapUpdate::STATUS_ACTIVE : SitemapUpdate::STATUS_INACTIVE);

            $this->saveSitemapUpdate($model, SitemapUpdate::ENTITY_EXAM, $url, $page->slug, $status, $page->updated_at);
        }
    }

    /** Update the sitemap pi collection
     * @param $collegeId | College ID
     *
     */
    public function updateCollegeProgramSitemap($collegeProgram, $collegeSlug, $collegeStatus)
    {
        if ($collegeProgram->updated_at instanceof \yii\db\Expression) {
            $timeStamp = date('Y-m-d H:i:s');
            $date = date(DATE_ATOM, strtotime($timeStamp));
        } else {
            $date = date(DATE_ATOM, strtotime($collegeProgram->updated_at));
        }

        $program = Program::find()->where(['id' => $collegeProgram->program_id])->one();

        if (!empty($program) && !empty($collegeProgram->course) && $collegeProgram->course->status == Course::STATUS_ACTIVE) {
            $url = 'college/' . CollegeHelper::collegeUrlFormate($collegeSlug, 'pi', $program->slug);

            $model = SitemapUpdate::find()
                ->where(['slug' => $url])
                ->andWhere(['sub_page' => 'pi'])
                ->one();

            $this->saveSitemapUpdate($model, Sitemap::ENTITY_COLLEGE, $url, 'pi', $collegeProgram->status, $collegeProgram->updated_at);
        }
    }

    public function updateCollegeCourseSitemapMysl($collegeCourse, $collegeSlug, $collegeStatus)
    {
        if ($collegeCourse->updated_at instanceof \yii\db\Expression) {
            $timeStamp = date('Y-m-d H:i:s');
            $date = date(DATE_ATOM, strtotime($timeStamp));
        } else {
            $date = date(DATE_ATOM, strtotime($collegeCourse->updated_at));
        }

        $course = Course::find()->where(['id' => $collegeCourse->course_id])->andWhere(['status' => Course::STATUS_ACTIVE])->one();

        if (!empty($course)) {
            $url = 'college/' . CollegeHelper::collegeUrlFormate($collegeSlug, 'ci', $course->slug);

            $model = SitemapUpdate::find()
                ->where(['slug' => $url])
                ->andWhere(['sub_page' => 'ci'])
                ->one();

            $this->saveSitemapUpdate($model, Sitemap::ENTITY_COLLEGE, $url, 'ci', $collegeCourse->status, $collegeCourse->updated_at);
        }
    }

    public function updateNewsUpdateXml($newsId, $status, $updatedAt = null, $publishedAt = null)
    {
        $news = NewsSubdomain::findOne($newsId);
        
        $statusVale = $status;
        
        if ($news->status == 1) {
            $statusVale = $status;
        }

        $model = SitemapUpdate::find()->where(['slug' => $news->slug])->one();
 
        $this->saveSitemapUpdate($model, Sitemap::ENTITY_NEWS, $news->slug, 'news', $statusVale, $updatedAt, $publishedAt);
    }
    

    private function saveSitemapUpdate($model, $entity, $url, $subPage, $status, $updatedAt = null, $publishedAt = null, $langCode = null)
    {
        if (!$model) {
            $model = new SitemapUpdate();
        } else {
            $model->touch('updated_at');
        }

        if ($entity == 'news') {
            $domain = 'https://news.getmyuni.com';
            $model->lang_code = 1;
        } else {
            $domain = self::DOMAIN;
        }

        $model->entity = $entity;
        $model->domain = $domain;
        $model->slug = $url;
        $model->sub_page = $subPage;
        $model->lang_code = $langCode ?? '';
        $model->priority = '1.0';
        $model->change_freq = self::CHANGE_FREQ_DAILY;
        $model->sitemap_update = $updatedAt ?? null;
        if (!empty($publishedAt)) {
            $model->published_at = $publishedAt;
        }

        $model->status = $status;

        if ($model->save()) {
            return true;
            echo "{$model->slug} \t {$model->entity} \n";
        } else {
            print_r($model->getErrors());
        }
    }
}
