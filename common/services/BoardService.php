<?php

namespace common\services;

use common\helpers\BoardHelper;
use common\helpers\DataHelper;
use common\models\Board;
use common\models\BoardContent;
use common\models\BoardExam;
use common\models\BoardSamplePaper;
use common\models\BoardSamplePaperFile;
use common\models\ManageNavMenuOrder;
use common\models\College;
use common\models\State;
use common\models\Comment;
use common\models\documents\College as DocumentsCollege;
use common\models\GmuMetaCategory;
use common\models\BoardPages;
use yii\helpers\ArrayHelper;
use yii\db\Query;
use Yii;

class BoardService
{
    const HOMEPAGE_CONTENT = 'boards';
    const YEAR = '2025';
    /**
     * Get all board
     *
     * @return array | []
     */

    public function getBoardList()
    {
        $lang_code = DataHelper::getLangId();
        $cacheKey = 'board_list_' . $lang_code;  // Generate a unique cache key based on lang_code
        $cacheDuration = 10800; // Cache for 3 hour

        $data = Yii::$app->cache->get($cacheKey);
        // Yii::$app->cache->delete('board_list_' . $lang_code);  // Clear the cache for the given language

        if ($data === false) {
            $boards = Board::find()
                ->select(['state_id', 'name', 'type', 'slug', 'logo', 'lang_code', 'display_name', 'id'])
                ->with(['state'])
                ->where(['status' => Board::STATUS_ACTIVE])
                ->andWhere(['lang_code' => $lang_code])
                ->orderBy(['type' => SORT_ASC])
                ->all();

            foreach ($boards as $board) {
                if ($board->type == Board::TYPE_NATIONAL || $board->type == Board::TYPE_OPEN) {
                    $data[$board->type][$board->name][] = [
                        'id' => $board->id ?? '',
                        'slug' => $board->slug,
                        'display_name' => $board->display_name,
                        'logo' => $board->logo,
                        'lang_code' => $board->lang_code,
                    ];
                } else {
                    $data[$board->type][$board->state->name][] = [
                        'id' => $board->id ?? '',
                        'slug' => $board->slug,
                        'display_name' => $board->display_name,
                        'logo' => $board->logo,
                        'lang_code' => $board->lang_code,
                    ];
                }
            }
            Yii::$app->cache->set($cacheKey, $data, $cacheDuration);
        }
        return $data;
    }

    /**
     *
     * @param string|integer $identifier id or slug of board
     * @return object
     */
    public function getDetail($identifier)
    {
        $lang_code = DataHelper::getLangId();
        $board = Board::find()->select(['state_id', 'level', 'name', 'type', 'slug', 'logo', 'lang_code', 'display_name', 'id'])->where(['lang_code' => $lang_code])->with(['state'])->active()->bySlug($identifier)->one();

        if (empty($board)) {
            $board = Board::find()->select(['state_id', 'level', 'name', 'type', 'slug', 'logo', 'lang_code', 'display_name', 'id'])->with(['state'])->active()->byId($identifier)->one();
        }
        //  echo "<pre>"; print_r($board); die;
        return $board;
    }

    /**
     * Get board content
     *
     * @param $id Board ID
     * @param $page Current sub-page
     * @param $parent Check if it's main page
     * @return object
     */
    public function getContent(int $id, string $section = '')
    {
        $boardContent = BoardContent::find()
            ->with('author')
            ->andWhere(['board_id' => $id]);
        if ($section == '') {
            $boardContent = $boardContent->andWhere(['page_slug' => 'overview']);
        } else {
            $boardContent = $boardContent->andWhere(['page_slug' => $section]);
        }

        return $boardContent->andWhere(['status' => BoardContent::STATUS_ACTIVE, 'parent_id' => null])->one();
    }

    /**
     * Get board Sub Page content
     *
     * @param $id Board ID
     * @param $page Current sub-page
     * @param $parent Check if it's main page
     * @return object
     */
    public function getSubPageContent(int $id, string $section = '', $parent = null)
    {
        if ($parent == 'supplementary') {
            return BoardContent::find()
                ->with('author')
                ->andWhere(['board_id' => $id, 'page_slug' => $section, 'status' => BoardContent::STATUS_ACTIVE])->andWhere(['IS NOT', 'parent_id', null])->one();
        } else {
            $boardParentContent = BoardContent::find()
                ->andWhere(['board_id' => $id, 'page_slug' => $parent])->one();
            return BoardContent::find()
                ->with('author')
                ->andWhere(['board_id' => $id, 'parent_id' => $boardParentContent->id, 'page_slug' => $section, 'status' => BoardContent::STATUS_ACTIVE])->andWhere(['IS NOT', 'parent_id', null])->one();
        }
    }

    /**
     * Return pages for board page
     *
     * @param integer $boardId
     * @return void
     */
    public function getPages(int $boardId)
    {
        return BoardContent::find()
            ->select(['page', 'page_slug', 'lang_code'])
            ->where(['board_id' => $boardId])
            ->andWhere(['parent_id' => null])
            ->andWhere(['status' => BoardContent::STATUS_ACTIVE])
            ->all();
    }

    /**
     * Get colleges by statewise
     *
     * @return object
     */
    public function getCollegesByState($state_id, $limit = 6)
    {
        $getColleges = College::find()
            ->select([
                'cover_image',
                'logo_image',
                'college.name',
                'college.slug',
                'city_id'
            ])
            ->joinWith('city c')
            ->where(['college.status' => College::STATUS_ACTIVE]);

        if (!empty($state_id)) {
            $getColleges->andWhere(['c.state_id' => $state_id]);
        }

        $getColleges->limit($limit);
        $data = $getColleges->all();

        return $data ?? [];
    }

    /**
     * Get board sample paper subjects
     *
     * @return array | []
     */
    public function getBoardSubject($contentId)
    {
        $data = [];
        $boardSubject = BoardSamplePaper::find()
            ->with('boardDetails')
            ->with(['subject'])
            ->where(['board_content_id' => $contentId])
            ->andWhere(['status' => BoardSamplePaper::STATUS_ACTIVE])
            ->all();

        foreach ($boardSubject as $paper) {
            if (!empty($paper->subject)) {
                $data[$paper->subject->year][] = [
                    'subject_slug' => $paper->subject_slug,
                    'pageSlug' => $paper->slug,
                ];
            }
        }

        return $data;
    }

    /**
     * Get board sample paper subject content
     *
     * @return object
     */
    public function getBoardSamplePaper($pageSlug)
    {
        $samplePaper = BoardSamplePaper::find()
            ->with('boardDetails')
            ->joinWith(['author'])
            ->where(['board_sample_paper.slug' => $pageSlug])
            ->one();

        return $samplePaper;
    }

    /**
     * Get board active menu
     *
     * @return object
     */
    public function getMenu($id)
    {
        $menus = [];

        $boardSamplePaperMenu = BoardContent::find()
            ->with(['board'])
            ->select(['page_slug', 'lang_code', 'board_id'])
            ->andWhere(['board_id' => $id])
            ->andWhere(['status' => BoardContent::STATUS_ACTIVE])
            ->all();

        if (!empty($boardSamplePaperMenu) && !empty($boardSamplePaperMenu[0]->board)) {
            $menus = $boardSamplePaperMenu;
        } else {
            $menus = [];
        }

        return $menus;
    }

    /**
     * Get board pages
     *
     * @return object
     */
    public function getPageName($id)
    {
        return BoardContent::find()
            ->select(['page_slug', 'lang_code'])
            ->andWhere(['id' => $id])
            ->andWhere(['status' => BoardContent::STATUS_ACTIVE])
            ->one();
    }

    /**
     * Get board sample paper set
     *
     * @return array || []
     */
    public function getBoardSamplePaperSet($id)
    {
        $data = [];
        $samplePaperSet = BoardSamplePaperFile::find()
            ->with(['boardSamplePaper'])
            ->where(['board_sample_paper_id' => $id])
            ->andWhere(['status' => BoardSamplePaper::STATUS_ACTIVE])
            ->all();

        foreach ($samplePaperSet as $year) {
            if (!empty($year->question_paper) || !empty($year->answer_paper)) {
                $data[$year->year][$year->set] = [
                    'set' => $year->set,
                    'question_paper' => $year->question_paper,
                    'answer_paper' => $year->answer_paper,
                ];
            }
        }
        return $data;
    }

    /**
     * Get board sample paper Comment
     *
     * @return object
     */
    public function getComments($id)
    {
        return Comment::find()
            ->with('children')
            ->where(['entity' => BoardSamplePaper::ENTITY_BOARD_SAMPLE_PAPER])
            ->andWhere(['entity_id' => $id])
            ->andWhere(['parent_id' => null])
            ->active()
            ->all();
    }

    /**
     * Get board Exam
     *
     * @return object
     */
    public function getExam(int $boardId)
    {
        $boardExam = BoardExam::find()
            ->with(['exam'])
            ->where(['board_id' => $boardId])
            ->all();

        return $boardExam;
    }

    public function getAdTargetData(Board $board)
    {
        $result = Board::find()
            ->select(['state_id', 'name', 'type', 'slug', 'logo', 'lang_code', 'display_name', 'id'])
            ->with(['state'])
            ->where(['id' => $board->id])
            ->one();
        // echo "<pre>"; print_r($result); die;
        $data = [
            'BoardName' => $result->slug ?? '',
            'State' => $result->state ? $result->state->name : '',
        ];

        return $data ?? [];
    }

    /**
     *  Get page Content and Meta data
     */
    public function getHomePageContent($slug = '')
    {
        $lang_code = DataHelper::getLangId();
        return GmuMetaCategory::find()
            ->select(['h1', 'title', 'description', 'top_content'])
            ->where(['slug' =>  self::HOMEPAGE_CONTENT])
            ->andWhere(['status' => GmuMetaCategory::STATUS_ACTIVE])
            ->andWhere(['lang_code' => $lang_code])
            ->one();
    }

    /**
     * Get board Sub Pages
     *
     * @return object
     */
    public function getSubPageDropdown($board, $page)
    {

        BoardPages::find()->where(['status' => BoardPages::STATUS_ACTIVE])->all();

        $dropDownArr = [];
        $query = new Query;
        $boardContent = $query->select(['b.page as parent_page', 'bb.id', 'bb.page', 'bb.page_slug', 'bb.parent_id'])
            ->from('board_content as b')
            ->innerJoin('board_content as bb', 'bb.parent_id = b.id')
            ->where(['b.board_id' => $board->id, 'b.status' => BoardContent::STATUS_ACTIVE, 'bb.status' => BoardContent::STATUS_ACTIVE])->orderBy(['bb.page' => SORT_ASC])->all();
        //echo "<pre>"; print_r($boardContent);
        foreach ($boardContent as $dropDownContent) {
            $parentBoardContent =   BoardContent::find()->select(['page_slug'])->where(['id' => $dropDownContent['parent_id']])->one();
            $checkPageActive =  BoardPages::find()->where(['status' => BoardPages::STATUS_ACTIVE])->andWhere(['slug' => $parentBoardContent->page_slug])->one();
            if (empty($checkPageActive)) {
                continue;
            }
            $checkSubpageActive =  BoardPages::find()->where(['status' => BoardPages::STATUS_INACTIVE])
                ->andWhere(['parent_id' => $checkPageActive->id])
                ->andWhere(['slug' => $dropDownContent['page_slug']])
                ->one();

            if (!empty($checkSubpageActive) && ($checkSubpageActive->status == 0)) {
                continue;
            }
            $dropDownArr[$dropDownContent['parent_page']][] = $dropDownContent;
        }

        return $dropDownArr;
    }

    /**
     * Get board hindi Sub Pages
     *
     * @return object
     */

    public function checkHindSubpage(string $slug, string $section = '', int $lang_code = null)
    {
        $hindi_board = Board::find()->select('id')->andWhere(['slug' => $slug])
            ->andWhere(['!=', 'lang_code', $lang_code])->one();
        if (empty($hindi_board)) {
            return null;
        }
        $boardHindiContent = BoardContent::find()
            ->with('author')
            ->andWhere(['board_id' => $hindi_board['id']])
            ->andWhere(['!=', 'lang_code', $lang_code]);
        if (empty($section)) {
            $boardHindiContent = $boardHindiContent->andWhere(['page_slug' => 'overview']);
        } else {
            $boardHindiContent = $boardHindiContent->andWhere(['page_slug' => $section]);
        }

        return $boardHindiContent->andWhere(['status' => BoardContent::STATUS_ACTIVE, 'parent_id' => null])->one();
    }

    public function getStateName($state_id)
    {
        $state = State::find()
            ->select(['name', 'slug'])
            ->where(['id' => $state_id])
            ->one();
        return $state;
    }


    /** Get city list based on specialization*/
    public function getStreamBasedStateList($state)
    {
        $collection = Yii::$app->mongodb->getCollection(DocumentsCollege::COLLECTION_NAME);
        $courses = $collection->aggregate(
            [
                ['$match' => ['state_id' => $state, 'is_sponsored' => 0]],
                ['$unwind' => ['path' => '$course', 'includeArrayIndex' => 'string', 'preserveNullAndEmptyArrays' => false]],
                [
                    '$group' => [
                        '_id' => [
                            'stream_slug' => '$course.stream_slug',
                            'stream_name' => '$course.stream_name'
                        ],
                        'colleges' => ['$addToSet' => '$college_id']
                    ]
                ],
                [
                    '$project' => ['slug' => '$_id.stream_slug', 'count' => ['$size' => '$colleges'], '_id' => 0]
                ]
            ]
        );

        return !empty($courses) ? ArrayHelper::map($courses, 'slug', 'count') : [];
    }

    public function getAllBoardPage()
    {
        return ArrayHelper::map(BoardPages::find()
            ->where(['status' => BoardPages::STATUS_ACTIVE])
            ->andWhere(['is', 'parent_id', new \yii\db\Expression('null')])->all(), 'slug', 'name');
    }

    public function getMenuOrder($board_id, $entity)
    {
        $getMenuOrder = ManageNavMenuOrder::find()
            ->select(['menu_order'])
            ->where(['entity_id' => $board_id])
            ->andWhere(['entity' => DataHelper::$manageMenuOrder[$entity]])
            ->one();
        $boardContentList = BoardContent::find()
            ->select(['page_slug', 'page', 'id', 'board_id'])
            ->where(['board_id' => $board_id])
            ->andWhere(['parent_id' => null])
            ->andWhere(['status' => BoardContent::STATUS_ACTIVE])
            ->asArray()
            ->all();

        if (!empty($getMenuOrder)) {
            foreach (unserialize($getMenuOrder->menu_order) as $key => $val) {
                $menuOrder[$key] = strstr($val, '--', true);
            }
            foreach ($boardContentList as $board) {
                if (!isset($menuOrder[$board['page_slug']])) {
                    $menuOrder[$board['page_slug']] = $board['page'];
                }
            }
            return  $menuOrder;
        } else {
            return [];
        }
    }

    public function getBoardDefaultSeoInfo($board, $page, $subPage, $isDropdown)
    {

        if ($isDropdown) {
            $parentId = BoardPages::find()->select(['id'])->where(['slug' => $page])->one();
            $seoInfo = BoardPages::find()->select(['h1', 'title', 'description', 'name'])
                ->where(['slug' => $subPage])
                ->andWhere(['parent_id' => $parentId->id])
                ->one();
        } else {
            $seoInfo = BoardPages::find()->select(['h1', 'title', 'description', 'name'])
                ->where(['slug' => $page])
                ->andWhere(['is', 'parent_id', new \yii\db\Expression('null')])
                ->one();
        }
        if (empty($seoInfo)) {
            return [];
        }

        $data = [];

        $boardName = preg_replace('/\s*(10th|12th)$/i', '', $board->display_name);
        foreach ($seoInfo as $key => $value) {
            $data[$key] = strtr($value, [
                '{board-name}' => $boardName,
                '{year}' => self::YEAR,
                '{class}' => $board->level,
                '{subject-name}' =>  $seoInfo->name,
            ]);
        }

        return $data;
    }
    public function correctUrl($board, $page)
    {
        if (isset(BoardHelper::$boardCorrecturlFormat[$page]) && str_contains($board, BoardHelper::$boardCorrecturlFormat[$page]['board_contains'])) {
            $board = str_replace('-' . BoardHelper::$boardCorrecturlFormat[$page]['board_contains'], '', $board);
            $page = BoardHelper::$boardCorrecturlFormat[$page]['page'];
        }

        return [
            'board' => $board,
            'page' => $page,
        ];
    }
}
