<?php

namespace common\models;

use Yii;
use yii\behaviors\SluggableBehavior;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "sa_college_subpage_sidemenu".
 *
 * @property int $id
 * @property string|null $name
 * @property string|null $slug
 * @property int|null $status
 * @property string|null $created_by
 * @property string|null $updated_by
 * @property string|null $created_at
 * @property string|null $updated_at
 *
 * @property SaCollegeSubpageSidemenuDetail[] $saCollegeSubpageSidemenuDetails
 */
class SaCollegeSubpageSidemenu extends \yii\db\ActiveRecord
{
    const STATUS_ACTIVE = 1;
    const STATUS_INACTIVE = 0;

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => ['created_at', 'updated_at'],
                    ActiveRecord::EVENT_BEFORE_UPDATE => 'updated_at',
                ],
                'value' => new \yii\db\Expression('NOW()'),
            ],
            'sluggable' => [
                'class' => SluggableBehavior::class,
                'attribute' => 'name',
                'immutable' => true
            ],
            // 'bedezign\yii2\audit\AuditTrailBehavior'
        ];
    }


    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'sa_college_subpage_sidemenu';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['status'], 'integer'],
            [['created_by', 'updated_by', 'created_at', 'updated_at'], 'safe'],
            [['name', 'slug'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'name' => 'Name',
            'slug' => 'Slug',
            'status' => 'Status',
            'created_by' => 'Created By',
            'updated_by' => 'Updated By',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * Gets query for [[SaCollegeSubpageSidemenuDetails]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\SaCollegeSubpageSidemenuDetailQuery
     */
    public function getSaCollegeSubpageSidemenuDetails()
    {
        return $this->hasMany(SaCollegeSubpageSidemenuDetail::className(), ['sa_college_subpage_sidemenu_id' => 'id']);
    }

    /**
     * Gets query for [[Author]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\UserQuery
     */
    public function getAuthor()
    {
        return $this->hasOne(User::className(), ['id' => 'created_by']);
    }

    /**
     * Gets query for [[Author]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\UserQuery
     */
    public function getUpdater()
    {
        return $this->hasOne(User::className(), ['id' => 'updated_by']);
    }

    /**
     * {@inheritdoc}
     * @return \common\models\query\SaCollegeSubpageSidemenuQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new \common\models\query\SaCollegeSubpageSidemenuQuery(get_called_class());
    }
}
