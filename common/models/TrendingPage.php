<?php

namespace common\models;

use Yii;
use yii\behaviors\SluggableBehavior;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "trending_page".
 *
 * @property int $id
 * @property int $entity
 * @property string|null $display_name
 * @property string|null $url
 * @property string|null $created_at
 * @property string|null $updated_at
 * @property string|null $expires_at
 *
 */
class TrendingPage extends \yii\db\ActiveRecord
{
    public static $fields = [
        'article' => ['id', 'title'],
        'exam' => ['id', 'name'],
        'board' => ['id', 'display_name'],
        'college' => ['id', 'name'],
        'course' => ['id', 'name'],
        'olympiad' => ['id', 'name'],
        'others' => ['id', 'name'],
    ];

    const STATUS_ACTIVE = 1;
    const STATUS_INACTIVE = 0;

    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => ['created_at', 'updated_at'],
                    ActiveRecord::EVENT_BEFORE_UPDATE => 'updated_at',
                ],
                'value' => new \yii\db\Expression('NOW()'),
            ],
            // 'bedezign\yii2\audit\AuditTrailBehavior'
        ];
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'trending_pages';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['entity', 'display_name', 'url'], 'required'],
            [['url'], 'url'],
            [['entity_id', 'status'], 'integer'],
            [['entity'], 'string'],
            [['entity', 'entity_id', 'url'], 'validateUniqueAttributes'],
            ['entity', 'in', 'range' => array_keys($this->getEntityTypes())],
            [['created_at', 'updated_at', 'expires_at'], 'safe'],
            [['display_name', 'url'], 'string', 'max' => 255],
        ];
    }
    
    /**
     * Validation method to handle unique validation conditionally
     */
    public function validateUniqueAttributes($attribute, $params)
    {
        if (!empty($this->entity_id)) {
            $uniqueValidator = new \yii\validators\UniqueValidator([
                'targetAttribute' => ['entity', 'entity_id', 'url']
            ]);
        } else {
            $uniqueValidator = new \yii\validators\UniqueValidator([
                'targetAttribute' => ['entity', 'url']
            ]);
        }
        $uniqueValidator->validateAttribute($this, $attribute);
    }
    
    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'entity' => 'Entity',
            'display_name' => 'Display Name',
            'url' => 'URL',
            'expires_at' => 'Expires At',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    public static function getEntityTypes()
    {
        return [
            'course' => 'Trending Course',
            'college' => 'Trending College',
            'exam' => 'Trending Exam',
            'olympiad' => 'Trending Olympiad',
            'board' => 'Trending Boards',
            'others' => 'Trending Now',
        ];
    }

    /**
     * Gets query for [[Exam]].
     *
     * @return \yii\db\ActiveQuery|ExamQuery
     */
    public function getExam()
    {
        return $this->hasOne(Exam::className(), ['id' => 'entity_id']);
    }

    /**
     * Gets query for [[College]].
     *
     * @return \yii\db\ActiveQuery|CollegeQuery
     */
    public function getCollege()
    {
        return $this->hasOne(College::className(), ['id' => 'entity_id']);
    }

    /**
     * Gets query for [[Board]].
     *
     * @return \yii\db\ActiveQuery|BoardQuery
     */
    public function getBoard()
    {
        return $this->hasOne(Board::className(), ['id' => 'entity_id']);
    }

    /**
     * Gets query for [[Course]].
     *
     * @return \yii\db\ActiveQuery|BoardQuery
     */
    public function getCourse()
    {
        return $this->hasOne(Course::className(), ['id' => 'entity_id']);
    }

    public function getTitle()
    {
        if (!empty($this->entity)) {
            $entity = $this->entity;
            list($id, $name) = self::$fields[$entity];
            return !empty($this->$entity->$name) ? $this->$entity->$name : '';
        }
    }

    // public static function getEntityTypes()
    // {
    //     return [
    //         0 => 'Trending Course',
    //         1 => 'Trending College',
    //         2 => 'Trending Exam',
    //         3 => 'Trending Olympiad',
    //         4 => 'Trending Boards',
    //         5 => 'Trending Now',
    //     ];
    // }
}
