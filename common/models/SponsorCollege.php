<?php

namespace common\models;

use Yii;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;
use yii\db\Exception;

/**
 * This is the model class for table "sponsor_college".
 *
 * @property int|null $college_id
 * @property string|null $state
 * @property string|null $stream
 * @property string|null $course
 * @property int|null $position
 * @property int $status
 * @property string|null $created_at
 * @property string|null $updated_at
 *
 * @property College $college
 */
class SponsorCollege extends \yii\db\ActiveRecord
{
    const STATUS_INACTIVE = 0;
    const STATUS_ACTIVE = 1;


    const VIEWS_GRID_SLIDER = 1;
    const VIEWS_LISTING_VIEW = 2;
   
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'sponsor_college';
    }

    public static function primaryKey()
    {
        return ['college_id'];
    }

    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => ['created_at', 'updated_at'],
                    ActiveRecord::EVENT_BEFORE_UPDATE => 'updated_at',
                ],
                'value' => new \yii\db\Expression('NOW()'),
            ],
            'bedezign\yii2\audit\AuditTrailBehavior'
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['college_id', 'position', 'status'], 'integer'],
            [['created_at', 'updated_at'], 'safe'],
            [['state', 'stream', 'course'], 'string', 'max' => 255],
            [['state', 'stream', 'course'], 'safe'],
            ['position', 'compare', 'compareValue' => 0, 'operator' => '>', 'message' => 'Position has to be greater than 0.'],
            [['college_id'], 'exist', 'skipOnError' => true, 'targetClass' => College::className(), 'targetAttribute' => ['college_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'college_id' => 'College',
            'state' => 'State',
            'stream' => 'Stream',
            'course' => 'Course',
            'position' => 'Position',
            'status' => 'Status',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * Gets query for [[College]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\CollegeQuery
     */
    public function getCollege()
    {
        return $this->hasOne(College::className(), ['id' => 'college_id']);
    }

    /**
     * Gets query for [[Colleges]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\CollegeQuery
     */
    public function getColleges()
    {
        return $this->hasMany(College::className(), ['id' => 'college_id']);
    }
    /**
     * Gets query for [[SponsorCollege]].
     *
     */
    public function getSponsorCollege()
    {
        return $this->hasMany(SponsorCollege::className(), ['college_id' => 'college_id']);
    }

    /**
     * {@inheritdoc}
     * @return \common\models\query\SponsorCollegeQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new \common\models\query\SponsorCollegeQuery(get_called_class());
    }
}
