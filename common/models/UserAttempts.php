<?php

namespace common\models;

use Yii;
/**
 * This is the model class for table "user_attempts".
 *
 * @property int $id
 * @property int|null $student_id
 * @property int|null $question_id
 * @property string|null $user_input
 * @property int|null $is_correct
 * @property string|null $created_at
 * @property string|null $updated_at
 *
 * @property ArticleSubpageSubsectionQuesAns $question
 * @property Student $student
 */
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;

class UserAttempts extends \yii\db\ActiveRecord
{

    const CORRECT_ANSWER = 1;
    const INCORRECT_ANSWER = 0;
   
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'user_attempts';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['student_id', 'question_id', 'is_correct'], 'integer'],
           // [['user_input'], 'string'],
            [['created_at', 'updated_at'], 'safe'],
           // [['question_id'], 'exist', 'skipOnError' => true, 'targetClass' => ArticleSubpageSubsectionQuesAns::className(), 'targetAttribute' => ['question_id' => 'id']],
           // [['student_id'], 'exist', 'skipOnError' => true, 'targetClass' => Student::className(), 'targetAttribute' => ['student_id' => 'id']],
        ];
    }

    public function behaviors()
    {
        return [
          
            [
                'class' => TimestampBehavior::class,
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => ['created_at', 'updated_at'],
                    ActiveRecord::EVENT_BEFORE_UPDATE => 'updated_at',
                ],
                'value' => new \yii\db\Expression('NOW()'),
            ],
            //'bedezign\yii2\audit\AuditTrailBehavior'
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'student_id' => 'Student ID',
            'question_id' => 'Question ID',
            'user_input' => 'User Input',
            'is_correct' => 'Is Correct',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * Gets query for [[Question]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getQuestion()
    {
        return $this->hasOne(ArticleSubpageSubsectionQuesAns::className(), ['id' => 'question_id']);
    }

    /**
     * Gets query for [[Student]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getStudent()
    {
        return $this->hasOne(Student::className(), ['id' => 'student_id']);
    }
}
