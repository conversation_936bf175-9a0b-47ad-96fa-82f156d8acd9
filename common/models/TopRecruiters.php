<?php

namespace common\models;

use Yii;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "top_recruiters".
 *
 * @property int $id
 * @property int|null $college_id
 * @property string|null $recruiter_name
 * @property string|null $recruiter_image
 * @property int|null $status
 * @property string|null $created_at
 * @property string|null $updated_at
 *
 * @property College $college
 */
class TopRecruiters extends \yii\db\ActiveRecord
{
    const STATUS_ACTIVE = 1;
    const STATUS_INACTIVE = 0;

    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => ['created_at', 'updated_at'],
                    ActiveRecord::EVENT_BEFORE_UPDATE => 'updated_at',
                ],
                'value' => new \yii\db\Expression('NOW()'),
            ],
            // 'bedezign\yii2\audit\AuditTrailBehavior'
        ];
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'top_recruiters';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['college_id', 'recruiter_name'], 'required'],
            [['college_id', 'status'], 'integer'],
            [['created_at', 'updated_at'], 'safe'],
            [['recruiter_name'], 'string', 'max' => 255],
            [['college_id'], 'exist', 'skipOnError' => true, 'targetClass' => College::className(), 'targetAttribute' => ['college_id' => 'id']],
            [['recruiter_image'], 'image', 'maxWidth' => '276', 'maxHeight' => '207', 'maxSize' => 1024 * 50, 'extensions' => 'webp', 'message' => 'Image size should not be greater than 50kb'],
            [['college_id', 'recruiter_name'], 'unique', 'targetAttribute' => ['college_id', 'recruiter_name']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'college_id' => 'College ID',
            'recruiter_name' => 'Recruiter Name',
            'recruiter_image' => 'Recruiter Image',
            'status' => 'Status',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * Gets query for [[College]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\CollegeQuery
     */
    public function getCollege()
    {
        return $this->hasOne(College::className(), ['id' => 'college_id']);
    }

    /**
     * {@inheritdoc}
     * @return \common\models\query\TopRecruitersQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new \common\models\query\TopRecruitersQuery(get_called_class());
    }
}
