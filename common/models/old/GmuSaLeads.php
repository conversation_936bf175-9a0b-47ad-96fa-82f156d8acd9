<?php

namespace common\models\old;

use Yii;
use common\traits\GmuDbTrait;
use frontend\helpers\Util;
use yii\db\Query;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "gmu_sa_leads".
 *
 * @property int $id
 * @property int|null $sa_clg_id
 * @property string|null $sa_clg_name
 * @property string|null $full_name
 * @property string|null $mobile_num
 * @property string|null $email_id
 * @property string|null $current_country
 * @property string|null $current_city
 * @property string|null $study_destination
 * @property string|null $planning_duration
 * @property string|null $intrestred_degree
 * @property string|null $appeared_exam
 * @property string|null $click_source
 * @property string|null $type
 * @property string|null $course
 * @property string|null $qualification
 * @property string|null $source
 * @property string|null $created_on
 */
class GmuSaLeads extends \yii\db\ActiveRecord
{

    const MOBILE_VERIFIED_YES = 1;
    const MOBILE_VERIFIED_NO = 0;

    public $url;
    
    use GmuDbTrait;

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => ['created_on']
                ],
                'value' => new \yii\db\Expression('NOW()'),
            ]
        ];
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'gmu_sa_leads';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['sa_clg_id'], 'integer'],
            [['created_on', 'url', 'otp'], 'safe'],
            [['full_name', 'email_id', 'mobile_num', 'current_country', 'study_destination', 'planning_duration'], 'required'],
            [['email_id'], 'match', 'pattern' => '/^([a-z0-9\+_\-]+)(\.[a-z0-9\+_\-]+)*@([a-z0-9\-]+\.)+[a-z]{2,6}$/ix'],
            [['full_name'], 'match', 'pattern' => '/^[a-zA-Z\s]+$/'],
            [['sa_clg_name', 'full_name', 'email_id'], 'string', 'max' => 255],
            [['mobile_num'], 'string', 'max' => 15],
            [['current_country', 'study_destination', 'source', 'course', 'type', 'qualification'], 'string', 'max' => 80],
            [['current_city'], 'string', 'max' => 100],
            [['planning_duration'], 'string', 'max' => 10],
            [['intrestred_degree', 'click_source'], 'string', 'max' => 50],
            [['appeared_exam'], 'string', 'max' => 200],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'sa_clg_id' => 'Sa Clg ID',
            'sa_clg_name' => 'Sa Clg Name',
            'full_name' => 'Full Name',
            'mobile_num' => 'Mobile Num',
            'email_id' => 'Email ID',
            'current_country' => 'Current Country',
            'current_city' => 'Current City',
            'study_destination' => 'Study Destination',
            'planning_duration' => 'Planning Duration',
            'intrestred_degree' => 'Intrestred Degree',
            'appeared_exam' => 'Appeared Exam',
            'click_source' => 'Click Source',
            'type' => 'Type',
            'source' => 'Source',
            'course' => 'Coures',
            'qualification' => 'Qualification',
            'created_on' => 'Created On',
        ];
    }

    public function getCountry()
    {
        $query = new Query();
        $query->select('*')
            ->from('gmu_sa_current_country');

        $country = $query->all(\Yii::$app->gmudb);

        return $country;
    }

    public function beforeSave($insert)
    {
        if ($insert) {
            $this->mobile_num = Util::cleanMobile($this->mobile_num);
        }

        return parent::beforeSave($insert);
    }
}
