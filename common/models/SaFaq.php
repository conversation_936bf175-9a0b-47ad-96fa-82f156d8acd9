<?php

namespace common\models;

use Yii;
use yii\behaviors\TimestampBehavior;
use yii\db\Expression;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;

/**
 * This is the model class for table "sa_faq".
 *
 * @property int $id
 * @property string|null $entity_id
 * @property string|null $question
 * @property string|null $answer
 * @property int|null $status
 * @property string|null $created_at
 * @property string|null $updated_at
 */
class SaFaq extends \yii\db\ActiveRecord
{
    const STATUS_ACTIVE = 1;
    const STATUS_INACTIVE = 0;

    const ENTITY_COUNTRY = 1;
    const ENTITY_COUNTRY_COLLEGE = 2;

    public static $fields = [
        '1' => ['id', 'name'],
        '2' => ['id', 'name'],
    ];

    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'createdAtAttribute' => 'created_at',
                'updatedAtAttribute' => 'updated_at',
                'value' => new Expression('NOW()'),
            ]
        ];
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'sa_faq';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['qnas', 'entity', 'entity_id'], 'required'],
            [['status', 'entity', 'entity_id'], 'integer'],
            [['created_at', 'updated_at'], 'safe'],
            [['entity', 'entity_id'], 'unique', 'targetAttribute' => ['entity', 'entity_id'], 'message' => 'Combination of this entity and page has already been taken'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'entity' => 'Entity',
            'entity_id' => 'Entity ID',
            'qnas' => 'Questions & Answers',
            'status' => 'Status',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * Gets query for [[Country]].
     *
     * @return \yii\db\ActiveQuery|CountryQuery
     */
    public function getCountry()
    {
        return $this->hasOne(SaCountry::className(), ['id' => 'entity_id']);
    }

    /**
     * Gets query for [[Country]].
     *
     * @return \yii\db\ActiveQuery|CountryQuery
     */
    public function getCollege()
    {
        return $this->hasOne(SaCollege::className(), ['id' => 'entity_id']);
    }

    public function beforeSave($insert)
    {
        if (is_array($this->qnas)) {
            $this->qnas = Json::decode(Json::encode(array_values($this->qnas)), true);
        }

        $this->qnas = json_encode($this->qnas);
        return parent::beforeSave($insert);
    }

    public function afterFind()
    {
        $this->qnas =  ArrayHelper::toArray(json_decode($this->qnas));
        return parent::afterFind();
    }

    public function getTitle()
    {
        $entity = $this->entity;

        $entityName = $entity == '1' ? 'country' : 'college';

        if (strtolower($entity) == 'filter') {
            return $this->page;
        }
        if ($this->entity == 'articles') {
            $entity = 'article';
        }
        list($id, $name) = self::$fields[$entity];

        return !empty($this->$entityName->$name) ? $this->$entityName->$name : '';
    }

    /**
     * {@inheritdoc}
     * @return \common\models\query\SaFaqQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new \common\models\query\SaFaqQuery(get_called_class());
    }
}
