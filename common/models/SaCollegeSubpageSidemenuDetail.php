<?php

namespace common\models;

use Yii;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "sa_college_subpage_sidemenu_detail".
 *
 * @property int $id
 * @property int $sa_college_id
 * @property int $sa_college_subpage_id
 * @property int $sa_college_subpage_sidemenu_id
 * @property string|null $content
 * @property int $status
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property string|null $created_at
 * @property string|null $updated_at
 *
 * @property SaCollege $saCollege
 * @property SaCollegeSubpage $saCollegeSubpage
 * @property SaCollegeSubpageSidemenu $saCollegeSubpageSidemenu
 */
class SaCollegeSubpageSidemenuDetail extends \yii\db\ActiveRecord
{

    const STATUS_INACTIVE = 0;
    const STATUS_ACTIVE = 1;

    /**
     * {@inheritdoc}
    */
    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => ['created_at', 'updated_at'],
                    ActiveRecord::EVENT_BEFORE_UPDATE => 'updated_at',
                ],
                'value' => new \yii\db\Expression('NOW()'),
            ],
            // 'bedezign\yii2\audit\AuditTrailBehavior'
        ];
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'sa_college_subpage_sidemenu_detail';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['sa_college_id', 'sa_college_subpage_id', 'sa_college_subpage_sidemenu_id'], 'required'],
            [['sa_college_id', 'sa_college_subpage_id', 'sa_college_subpage_sidemenu_id', 'status', 'created_by', 'updated_by'], 'integer'],
            [['content'], 'string'],
            [['created_at', 'updated_at'], 'safe'],
            [['sa_college_id'], 'exist', 'skipOnError' => true, 'targetClass' => SaCollege::className(), 'targetAttribute' => ['sa_college_id' => 'id']],
            [['sa_college_subpage_id'], 'exist', 'skipOnError' => true, 'targetClass' => SaCollegeSubpage::className(), 'targetAttribute' => ['sa_college_subpage_id' => 'id']],
            [['sa_college_subpage_sidemenu_id'], 'exist', 'skipOnError' => true, 'targetClass' => SaCollegeSubpageSidemenu::className(), 'targetAttribute' => ['sa_college_subpage_sidemenu_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'sa_college_id' => 'Sa College ID',
            'sa_college_subpage_id' => 'Sa College Subpage ID',
            'sa_college_subpage_sidemenu_id' => 'Sa College Subpage Sidemenu ID',
            'content' => 'Content',
            'status' => 'Status',
            'created_by' => 'Created By',
            'updated_by' => 'Updated By',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * Gets query for [[SaCollege]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\SaCollegeQuery
     */
    public function getSaCollege()
    {
        return $this->hasOne(SaCollege::className(), ['id' => 'sa_college_id']);
    }

    /**
     * Gets query for [[SaCollegeSubpage]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\SaCollegeSubpageQuery
     */
    public function getSaCollegeSubpage()
    {
        return $this->hasOne(SaCollegeSubpage::className(), ['id' => 'sa_college_subpage_id']);
    }

    /**
     * Gets query for [[SaCollegeSubpageSidemenu]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\SaCollegeSubpageSidemenuQuery
     */
    public function getSaCollegeSubpageSidemenu()
    {
        return $this->hasOne(SaCollegeSubpageSidemenu::className(), ['id' => 'sa_college_subpage_sidemenu_id']);
    }

    /**
     * Gets query for [[Author]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\UserQuery
     */
    public function getAuthor()
    {
        return $this->hasOne(User::className(), ['id' => 'created_by']);
    }

    /**
     * Gets query for [[Author]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\UserQuery
     */
    public function getUpdater()
    {
        return $this->hasOne(User::className(), ['id' => 'updated_by']);
    }

    /**
     * {@inheritdoc}
     * @return \common\models\query\SaCollegeSubpageSidemenuDetailQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new \common\models\query\SaCollegeSubpageSidemenuDetailQuery(get_called_class());
    }
}
