<?php

namespace common\models\calculator;

use yii\base\Model;

class PrCalculator extends Model
{
    public $age;
    public $education;
    public $workExperience;
    public $englishReading;
    public $englishWriting;
    public $englishListening;
    public $englishSpeaking;
    public $yourFrenchProficiency;
    public $youWorkedInCanada;
    public $youStudiedInCanada;
    public $relativeInCanada;
    public $arrangedEmployment;
    public $maritalStatus;
    public $spouseLanguageProficiency;
    public $spouseWorkedInCanada;
    public $spouseStudiedInCanada;
    public $finalScore;

    const ENGLISH_READING = [
        '8' => 8,
        '7' => 7,
        '6.5' => 6.5,
        '6' => 6
    ];
    const ENGLISH_WRITING = [
        '7.5' => 7.5,
        '7' => 7,
        '6.5' => 6.5,
        '6' => 6
    ];
    const ENGLISH_LISTENING = [
        '8.5' => 8.5,
        '8' => 8,
        '7.5' => 7.5,
        '6' => 6
    ];
    const ENGLISH_SPEAKING = [
        '7.5' => 7.5,
        '7' => 7,
        '6.5' => 6.5,
        '6' => 6
    ];

    const YES = 1;
    const NO = 0;
    const YES_NO = [
        self::YES => 'Yes',
        self::NO => 'No'
    ];


    public function rules()
    {
        return [
            [['age', 'education', 'workExperience', 'englishReading', 'englishWriting', 'englishListening', 'englishSpeaking', 'yourFrenchProficiency', 'youWorkedInCanada', 'youStudiedInCanada', 'relativeInCanada', 'arrangedEmployment', 'maritalStatus'], 'required', 'message' => 'Please select any option'],
            [['spouseLanguageProficiency', 'spouseWorkedInCanada', 'spouseStudiedInCanada'], 'validateSpouseData', 'skipOnEmpty' => false]
        ];
    }

    public static function workExperienceList()
    {
        return [
            '1 year' => '1 year',
            '2 - 3 years' => '2 - 3 years',
            '4 - 5 years' => '4 - 5 years',
            '6 or more' => '6 or more years'
        ];
    }

    public static function ageList()
    {
        $age['under_18'] = 'Under 18';
        $age['18-35'] = '18 - 35';
        foreach (range(36, 46, 1) as $i) {
            $age[$i] = $i;
        }
        $age['above_47'] = '47 or above';
        return $age;
    }

    public static function educationQualificationList()
    {
        return [
            'University degree at the Doctoral (PhD) level or equal' => 'University degree at the Doctoral (PhD) level or equal',
            "University degree at the Master's level or equal" => "University degree at the Master's level or equal",
            'Bachelors degree theee or more years' => 'Bachelors degree theee or more years',
            'Prefessional degree needed to practice in licensed profession' => 'Prefessional degree needed to practice in licensed profession',
            'Two or more Canadian post-secondary degrees or diplomas or equal (at least one must be for a program of at least three years)' => 'Two or more Canadian post-secondary degrees or diplomas or equal (at least one must be for a program of at least three years)',
            'Canadian post-secondary degree or diploma for a program of three years or longer, or equal' => 'Canadian post-secondary degree or diploma for a program of three years or longer, or equal',
            'Canadian post-secondary degree or diploma for a two-year program, or equal' => 'Canadian post-secondary degree or diploma for a two-year program, or equal',
            'Canadian post-secondary degree or diploma for a one-year program, or equal' => 'Canadian post-secondary degree or diploma for a one-year program, or equal',
            'Canadian high school diploma, or equal' => 'Canadian high school diploma, or equal'
        ];
    }

    public function validateSpouseData($attribute)
    {
        if ($this->maritalStatus == self::YES && !in_array($this->{$attribute}, ['0', '1'])) {
            $this->addError($attribute, 'Please select any option');
            return false;
        }
        return true;
    }
}
