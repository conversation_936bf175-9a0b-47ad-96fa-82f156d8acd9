<?php

namespace common\models\calculator;

use yii\base\Model;

class GermanyOppCalculator extends Model
{
    public $countryCitizenship;
    public $financialProof;
    public $qualification;
    public $workExperience;
    public $germanyLanguage;
    public $englishLanguage;
    public $age;
    public $germanyPrevStay;
    public $germanySpouseApplication;
    public $finalScore;


    const YES = 1;
    const NO = 0;
    const YES_NO = [
        self::YES => 'Yes',
        self::NO => 'No'
    ];

    const FINANCIAL_PROOF_LIST = [
        'work-contract' => 'Work contract',
        'blocked-account' => 'Blocked account',
        'declaration-of-commitment' => 'Declaration of commitment'
    ];

    const QUALIFICATION_LIST = [
        'Bachelor' => 'Recognized Bachelor’s Degree',
        'Master' => 'Recognized Higher Education/Vocational Training',
        'Vocational' => 'Partially Recognized Vocational Training',
        'None' => 'None'
    ];

    const WORK_EXP_LIST = [
        '3-years' => 'More than 3 years',
        '1-3-years' => '1 - 3 years',
        '1-year' => 'Less than 1 year',
    ];

    const GERMANY_LANGUAGE_LIST = [
        'b1-higher' => 'B1 or higher',
        'a1-a2' => 'A1 or A2',
        'none' => 'None',
    ];

    const ENGLISH_LANGUAGE_LIST = [
        'b2-higher' => 'B2 or higher',
        'none' => 'Lower levels/None',
    ];

    const AGE_LIST = [
        '<35' => 'Between 18 to 35',
        '35-40' => 'Between 35 to 40',
        '>40' => 'Over 40',
    ];

    //////////////
    public function rules()
    {
        return [
            [[
                'countryCitizenship', 'financialProof', 'qualification', 'workExperience', 'germanyLanguage', 'englishLanguage', 'age', 'germanyPrevStay', 'germanySpouseApplication'
            ], 'required', 'message' => 'Please select any option'],
        ];
    }
}
