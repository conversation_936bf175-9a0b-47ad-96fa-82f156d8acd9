<?php

namespace common\models\calculator;

use yii\base\Model;

class CrsCalculator extends Model
{
    public $maritalStatus;
    public $citizenship;
    public $accompaniedBySpouse;
    public $age;
    public $education;
    public $canadianDegree;
    public $canadianEducation;
    public $latestResult;
    public $primaryExam;
    public $primaryReadingScore;
    public $primaryWritingScore;
    public $primaryListeningScore;
    public $primarySpeakingScore;
    public $secondaryLanguage;
    public $secondaryReadingScore;
    public $secondaryWritingScore;
    public $secondaryListeningScore;
    public $secondarySpeakingScore;
    public $skilledLastTenYearExp;
    public $foreignSkilledLastTenYearExp;
    public $certificate;
    public $validJob;
    public $noc;
    public $nomination;
    public $relativeInCanada;
    public $spouseEducation;
    public $spouseSkilledLastTenYearExp;
    public $spouseLatestResult;
    public $spousePrimaryExam;
    public $spousePrimaryReadingScore;
    public $spousePrimaryWritingScore;
    public $spousePrimaryListeningScore;
    public $spousePrimarySpeakingScore;
    public $finalScore;

    const MARITAL_STATUS = [
        'Annulled Marriage' => 'Annulled Marriage',
        'Common Law' => 'Common Law',
        'Married' => 'Married',
        'Divorced/Separated' => 'Divorced/Separated',
        'Legally Separated' => 'Legally Separated',
        'Never Married/Single' => 'Never Married/Single',
        'Widowed' => 'Widowed'
    ];

    const MARITAL_STATUS_VALUE = [
        'Annulled Marriage' => self::NO,
        'Common Law' => self::YES,
        'Married' => self::YES,
        'Divorced/Separated' => self::NO,
        'Legally Separated' => self::NO,
        'Never Married/Single' => self::NO,
        'Widowed' => self::NO
    ];

    const EDUCATION_LIST = [
        'Less than secondary school (high school)' => 'Less than secondary school (high school)',
        'Secondary diploma (high school graduation)' => 'Secondary diploma (high school graduation)',
        'One-year degree, diploma or certificate from  a university, college, trade or technical school, or other institute' => 'One-year degree, diploma or certificate from  a university, college, trade or technical school, or other institute',
        'Two-year program at a university, college, trade or technical school, or other institute' => 'Two-year program at a university, college, trade or technical school, or other institute',
        "Bachelor's degree OR  a three or more year program at a university, college, trade or technical school, or other institute" => "Bachelor's degree OR  a three or more year program at a university, college, trade or technical school, or other institute",
        'Two or more certificates, diplomas, or degrees. One must be for a program of three or more years' => 'Two or more certificates, diplomas, or degrees. One must be for a program of three or more years',
        "Master's degree, OR professional degree needed to practice in a licensed profession" => "Master's degree, OR professional degree needed to practice in a licensed profession",
        'Doctoral level university degree (Ph.D.)' => 'Doctoral level university degree (Ph.D.)'
    ];

    const CANADIAN_EDUCATION_LIST = [
        'Secondary High School or less' => 'Secondary High School or less',
        'One or two year diploma or certificate' => 'One or two year diploma or certificate',
        "Degree or diploma or certificate of three years or longer OR a Master's or professional or doctoral degree of at least one academic year" => "Degree or diploma or certificate of three years or longer OR a Master's or professional or doctoral degree of at least one academic year"
    ];

    const YES = 1;
    const NO = 0;
    const YES_NO = [
        self::YES => 'Yes',
        self::NO => 'No',
    ];

    const EXAM_IELTS = 'IELTS';
    const EXAM_CELPIP = 'CELPIP';
    const EXAM_TEF = 'TEF';
    const EXAM_TCF = 'TCF';
    const EXAM_TYPE = [
        self::EXAM_IELTS => 'IELTS',
        self::EXAM_CELPIP => 'CELPIP - G',
        self::EXAM_TEF => 'TEF Canada',
        self::EXAM_TCF => 'TCF Canada'
    ];

    const SECONDARY_EXAM = [
        self::EXAM_TEF => 'TEF Canada',
        self::EXAM_TCF => 'TCF Canada'
    ];

    const LAST_10_YR_EXP = [
        '1_less' => 'None or less than a year',
        '1_year' => '1 Year',
        '2_year' => '2 Year',
        '3_year' => '3 Year',
        '4_year' => '4 Year',
        '5_more' => '5 or more years'
    ];

    const FOREIGN_YEAR_EXP = [
        '1_less' => 'None or less than a year',
        '1_year' => '1 Year',
        '2_year' => '2 Year',
        '3_more' => '3 years or more'
    ];

    const NOC_TYPE = [
        'NOC Skill Type 00' => 'NOC Skill Type 00',
        'NOC Skill Level A or B or any other Type 0 other than 00' => 'NOC Skill Level A or B or any other Type 0 other than 00',
        'NOC Skill Level C or D' => 'NOC Skill Level C or D'
    ];

    const IELTS_READING_BAND = [
        'Band 8.0 - 9.0' => '8.0 - 9.0',
        'Band 7 - 7.5' => '7 - 7.5',
        'Band 6.5' => '6.5',
        'Band 6' => '6',
        'Band 5 - 5.5' => '5 - 5.5',
        'Band 4 - 4.5' => '4 - 4.5',
        'Band 3.5' => '3.5',
        'Band 0 - 3.5' => '0 - 3.5'
    ];
    const IELTS_WRITING_BAND = [
        'Band 7.5 - 9.0' => '7.5 - 9.0',
        'Band 7' => '7',
        'Band 6.5' => '6.5',
        'Band 6' => '6',
        'Band 5.5' => '5.5',
        'Band 5' => '5',
        'Band 4 - 4.5' => '4 - 4.5',
        'Band 0 - 3.5' => '0 - 3.5'
    ];
    const IELTS_LISTENING_BAND = [
        'Band 8.5 - 9.0' => '8.5 - 9.0',
        'Band 8' => '8',
        'Band 7.5' => '7.5',
        'Band 6 - 7' => '6 - 7',
        'Band 5.5' => '5.5',
        'Band 5' => '5',
        'Band 4.5' => '4.5',
        'Band 0 - 4' => '0 - 4'
    ];
    const IELTS_SPEAKING_BAND = [
        'Band 7.5 - 9.0' => '7.5 - 9.0',
        'Band 7' => '7',
        'Band 6.5' => '6.5',
        'Band 6' => '6',
        'Band 5.5' => '5.5',
        'Band 5' => '5',
        'Band 4 - 4.5' => '4 - 4.5',
        'Band 0 - 3.5' => '0 - 3.5'
    ];

    const CELPIP_READING_BAND = [
        'Band 10 - 12' => '10 - 12',
        'Band 9' => '9',
        'Band 8' => '8',
        'Band 7' => '7',
        'Band 6' => '6',
        'Band 5' => '5',
        'Band 4' => '4',
        'Band 0 - 3' => 'M;0 - 3'
    ];
    const CELPIP_WRITING_BAND = [
        'Band 10 - 12' => '10 - 12',
        'Band 9' => '9',
        'Band 8' => '8',
        'Band 7' => '7',
        'Band 6' => '6',
        'Band 5' => '5',
        'Band 4' => '4',
        'Band 0 - 3' => 'M;0 - 3'
    ];
    const CELPIP_LISTENING_BAND = [
        'Band 10 - 12' => '10 - 12',
        'Band 9' => '9',
        'Band 8' => '8',
        'Band 7' => '7',
        'Band 6' => '6',
        'Band 5' => '5',
        'Band 4' => '4',
        'Band 0 - 3' => 'M;0 - 3'
    ];
    const CELPIP_SPEAKING_BAND = [
        'Band 10 - 12' => '10 - 12',
        'Band 9' => '9',
        'Band 8' => '8',
        'Band 7' => '7',
        'Band 6' => '6',
        'Band 5' => '5',
        'Band 4' => '4',
        'Band 0 - 3' => 'M;0 - 3'
    ];

    const TEF_READING_BAND = [
        'Band 263 - 300' => '263 - 300',
        'Band 248 - 262' => '248 - 262',
        'Band 233 - 247' => '233 - 247',
        'Band 207 - 232' => '207 - 232',
        'Band 181 - 206' => '181 - 206',
        'Band 151 - 180' => '151 - 180',
        'Band 121 - 150' => '121 - 150',
        'Band 0 - 120' => '0 - 120'
    ];
    const TEF_WRITING_BAND = [
        'Band 393 - 450' => '393 - 450',
        'Band 371 - 392' => '371 - 392',
        'Band 349 - 370' => '349 - 370',
        'Band 310 - 348' => '310 - 348',
        'Band 271 - 309' => '271 - 309',
        'Band 226 - 270' => '226 - 270',
        'Band 181 - 225' => '181 - 225',
        'Band 0 - 180' => '0 - 180'
    ];
    const TEF_LISTENING_BAND = [
        'Band 316 - 360' => '316 - 360',
        'Band 298 - 315' => '298 - 315',
        'Band 280 - 297' => '280 - 297',
        'Band 249 - 279' => '249 - 279',
        'Band 217 - 248' => '217 - 248',
        'Band 181 - 216' => '181 - 216',
        'Band 145 - 180' => '145 - 180',
        'Band 0 - 144' => '0 - 144'
    ];
    const TEF_SPEAKING_BAND = [
        'Band 393 - 450' => '393 - 450',
        'Band 371 - 392' => '371 - 392',
        'Band 349 - 370' => '349 - 370',
        'Band 310 - 348' => '310 - 348',
        'Band 271 - 309' => '271 - 309',
        'Band 226 - 270' => '226 - 270',
        'Band 181 - 225' => '181 - 225',
        'Band 0 - 180' => '0 - 180'
    ];

    const TCF_READING_BAND = [
        'Band 549 - 699' => '549 - 699',
        'Band 524 - 548' => '524 - 548',
        'Band 499 - 523' => '499 - 523',
        'Band 453 - 498' => '453 - 498',
        'Band 406 - 452' => '406 - 452',
        'Band 375 - 405' => '375 - 405',
        'Band 342 - 374' => '342 - 374',
        'Band 0 - 341' => '0 - 341'
    ];
    const TCF_WRITING_BAND = [
        'Band 16 - 20' => '16 - 20',
        'Band 14 - 15' => '14 - 15',
        'Band 12 - 13' => '12 - 13',
        'Band 10 - 11' => '10 - 11',
        'Band 7 - 9' => '7 - 9',
        'Band 6' => '6',
        'Band 4 - 5' => '4 - 5',
        'Band 0 - 3' => '0 - 3'
    ];
    const TCF_LISTENING_BAND = [
        'Band 549 - 699' => '549 - 699',
        'Band 523 - 548' => '523 - 548',
        'Band 503 - 522' => '503 - 522',
        'Band 458 - 502' => '458 - 502',
        'Band 398 - 457' => '398 - 457',
        'Band 369 - 397' => '369 - 397',
        'Band 331 - 368' => '331 - 368',
        'Band 0 - 330' => '0 - 330'
    ];
    const TCF_SPEAKING_BAND = [
        'Band 16 - 20' => '16 - 20',
        'Band 14 - 15' => '14 - 15',
        'Band 12 - 13' => '12 - 13',
        'Band 10 - 11' => '10 - 11',
        'Band 7 - 9' => '7 - 9',
        'Band 6' => '6',
        'Band 4 - 5' => '4 - 5',
        'Band 0 - 3' => '0 - 3'
    ];

    const EXAM_SECTION = [
        'IELTS' => [
            'Reading' => self::IELTS_READING_BAND,
            'Writing' => self::IELTS_WRITING_BAND,
            'Listening' => self::IELTS_LISTENING_BAND,
            'Speaking' => self::IELTS_SPEAKING_BAND
        ],
        'CELPIP' => [
            'Reading' => self::CELPIP_READING_BAND,
            'Writing' => self::CELPIP_WRITING_BAND,
            'Listening' => self::CELPIP_LISTENING_BAND,
            'Speaking' => self::CELPIP_SPEAKING_BAND
        ],
        'TEF' => [
            'Reading' => self::TEF_READING_BAND,
            'Writing' => self::TEF_WRITING_BAND,
            'Listening' => self::TEF_LISTENING_BAND,
            'Speaking' => self::TEF_SPEAKING_BAND
        ],
        'TCF' => [
            'Reading' => self::TCF_READING_BAND,
            'Writing' => self::TCF_WRITING_BAND,
            'Listening' => self::TCF_LISTENING_BAND,
            'Speaking' => self::TCF_SPEAKING_BAND
        ]
    ];

    public static function ageList()
    {
        $age['under_17'] = '17 years or less';
        $age['18'] = '18';
        $age['19'] = '19';
        $age['20_29'] = '20 to 29 years';
        foreach (range(30, 44, 1) as $i) {
            $age[$i] = $i;
        }
        $age['above_45'] = '45 or above';
        return $age;
    }

    public function rules()
    {
        return [
            [['maritalStatus', 'age', 'education', 'canadianDegree', 'latestResult', 'skilledLastTenYearExp', 'foreignSkilledLastTenYearExp', 'certificate', 'validJob', 'nomination', 'relativeInCanada'], 'required'],
            [['citizenship', 'accompaniedBySpouse', 'secondaryLanguage', 'spouseLatestResult'], 'safe'],
            ['canadianEducation', 'validateCanadianEducation', 'skipOnEmpty' => false],
            ['primaryExam', 'validatePrimaryExam', 'skipOnEmpty' => false],
            [['primaryReadingScore', 'primaryWritingScore', 'primaryListeningScore', 'primarySpeakingScore'], 'validatePrimaryExamScore', 'skipOnEmpty' => false],
            [['secondaryReadingScore', 'secondaryWritingScore', 'secondaryListeningScore', 'secondarySpeakingScore'], 'validateSecondaryExamScore', 'skipOnEmpty' => false],
            ['noc', 'validateNoc', 'skipOnEmpty' => false],
            [['spouseEducation', 'spouseSkilledLastTenYearExp'], 'validateSpouseData', 'skipOnEmpty' => false],
            ['spousePrimaryExam', 'validateSpousePrimaryExam', 'skipOnEmpty' => false],
            [['spousePrimaryReadingScore', 'spousePrimaryWritingScore', 'spousePrimaryListeningScore', 'spousePrimarySpeakingScore'], 'validateSpousePrimaryExamScore', 'skipOnEmpty' => false],
        ];
    }

    public function validateCanadianEducation($attribute)
    {
        if ($this->canadianDegree == self::YES && empty($this->{$attribute})) {
            $this->addError($attribute, 'Canadian Education is mandatory');
            return false;
        }
        return true;
    }

    public function validatePrimaryExam($attribute)
    {
        if ($this->latestResult == self::YES && empty($this->{$attribute})) {
            $this->addError($attribute, 'This cannot be blank');
            return false;
        }
        return true;
    }

    public function validatePrimaryExamScore($attribute)
    {
        if (!empty($this->primaryExam) && empty($this->{$attribute})) {
            $this->addError($attribute, 'Score can not be blank');
            return false;
        }
        return true;
    }

    public function validateSecondaryExamScore($attribute)
    {
        if (!empty($this->secondaryLanguage) && ($this->secondaryLanguage != 'NA') && empty($this->{$attribute})) {
            $this->addError($attribute, 'Score can not be blank');
            return false;
        }
        return true;
    }

    public function validateNoc($attribute)
    {
        if ($this->validJob == self::YES && empty($this->{$attribute})) {
            $this->addError($attribute, 'NOC can not be blank for a valid job offer');
            return false;
        }
        return true;
    }

    public function validateSpouseData($attribute)
    {
        if (self::MARITAL_STATUS_VALUE[$this->maritalStatus] && empty($this->{$attribute})) {
            $this->addError($attribute, 'This field can not be blank');
            return false;
        }
        return true;
    }

    public function validateSpousePrimaryExam($attribute)
    {
        if ($this->spouseLatestResult == self::YES && empty($this->{$attribute})) {
            $this->addError($attribute, 'This cannot be blank');
            return false;
        }
        return true;
    }

    public function validateSpousePrimaryExamScore($attribute)
    {
        if (!empty($this->spousePrimaryExam) && empty($this->{$attribute})) {
            $this->addError($attribute, 'Score can not be blank');
            return false;
        }
        return true;
    }
}
