<?php

namespace common\models\calculator;

use yii\base\Model;

class AustraliaPrPointsCalculator extends Model
{
    public $age;
    public $englishProficiency;
    public $workExperienceAbroad;
    public $workExperienceAus;
    public $overseaseducation;
    public $auseducationqualification;
    public $regeducationqualification;
    public $specialization;
    public $languagetest;
    public $partnercategory;
    public $professionalyear;
    public $nomination;
    public $nominationsponsorship;
    public $finalScore;

    const YES = 1;
    const NO = 0;
    const YES_NO = [
        self::YES => 'Yes',
        self::NO => 'No'
    ];

    public static function ageList()
    {
        $age['18-24'] = '18 - 24 years';
        $age['25-32'] = '25 - 32 years';
        $age['33-39'] = '33 - 39 years';
        $age['40-44'] = '40 - 44 years';
        return $age;
    }
    public static function englishProficiency()
    {
        $engPro['Competent English'] = 'Competent: IELTS - 6.0 ( Each Module ) | PTE 50 | OET B | TOEFL 12, 13, 21, 18 | CAE 169';
        $engPro['Proficient English'] = 'Proficient: IELTS - 7.0 ( Each Module ) | PTE 65 | OET B | TOEFL 24, 24, 27, 23 | CAE 185';
        $engPro['Superior English'] = 'Superior: IELTS - 8.0 ( Each Module ) | PTE 79 | OET A | TOEFL 28, 29, 30, 26 | CAE 200';
        return $engPro;
    }
    public static function workExperienceAbList()
    {
        $workExpAbroad['0-2.11'] = '0 - 2.11 Years';
        $workExpAbroad['3-5'] = '3 - 5 Years';
        $workExpAbroad['5-8'] = '5 - 8 Years';
        $workExpAbroad['8+'] = '8+ Years';
        return $workExpAbroad;
    }
    public static function workExperienceAusList()
    {
        $workExpAus['0-1'] = '0 - 1 Years';
        $workExpAus['1-3'] = '1 - 3 Years';
        $workExpAus['3-5'] = '3 - 5 Years';
        $workExpAus['5-8'] = '5 - 8 Years';
        $workExpAus['8+'] = '8+ Years';
        return $workExpAus;
    }
    public static function overseasEducationQualificationList()
    {
        return [
            'Diploma or Trade qualification' => 'Diploma or Trade qualification',
            "Bachelor's Degree" => "Bachelor's Degree",
            'Master’s Degree'  => 'Master’s Degree',
            'Ph.D. (Doctor of Philosophy)' => 'Ph.D. (Doctor of Philosophy)'
        ];
    }
    public static function partnercategoryList()
    {
        return [
            'Single' => 'Single',
            'Your spouse has applied for the same visa subclass as you, and possesses competent English proficiency, but is neither an Australian PR nor a citizen' => 'Your spouse has applied for the same visa subclass as you, and possesses competent English proficiency, but is neither an Australian PR nor a citizen.',
            'Spouse holds an Australian PR or Citizenship'  => 'Spouse holds an Australian PR or Citizenship.',
            'Your spouse is below 45 years, has competent English proficiency, a positive skills assessment for a suitable occupation (excluding Subclass 485), and applied for the same visa subclass as you' => 'Your spouse is below 45 years, has competent English proficiency, a positive skills assessment for a suitable occupation (excluding Subclass 485), and applied for the same visa subclass as you.'
        ];
    }
    public static function nominationsponsorshipList()
    {
        return [
            "You received an invitation to apply for Subclass 491 skilled work Regional (Provisional) visa since you were nominated, and the nominating State/Territory government agency didn't withdraw the nomination" => "You received an invitation to apply for Subclass 491 skilled work Regional (Provisional) visa since you were nominated, and the nominating State/Territory government agency didn't withdraw the nomination.",
            'Your family member is sponsoring you for a Skilled Work Regional (Provisional) visa (Subclass 491), and the sponsorship has been accepted by the minister' => 'Your family member is sponsoring you for a Skilled Work Regional (Provisional) visa (Subclass 491), and the sponsorship has been accepted by the minister.',
            'Not Applicable' => 'Not Applicable.'
        ];
    }
    //////////////
    public function rules()
    {
        return [
            [['age', 'englishProficiency', 'workExperienceAbroad', 'workExperienceAus', 'overseaseducation', 'auseducationqualification', 'regeducationqualification', 'specialization', 'languagetest', 'partnercategory', 'professionalyear', 'nomination', 'nominationsponsorship'], 'required', 'message' => 'Please select any option'],
        ];
    }
}
