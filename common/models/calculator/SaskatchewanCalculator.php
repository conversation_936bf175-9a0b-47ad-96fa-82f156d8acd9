<?php

namespace common\models\calculator;

use yii\base\Model;

class SaskatchewanCalculator extends Model
{
    public $education;
    public $workExperienceFiveYrPrior;
    public $workExperienceTenYrPrior;
    public $age;
    public $englishProficiency;
    public $frenchProficiency;
    public $employmentOffer;
    public $relativeInSaskatchewan;
    public $studyExperienceInSaskatchewan;
    public $workExperienceInSaskatchewan;
    public $finalScore;

    const CLB_8_OR_HIGHER = 8;
    const CLB_7 = 7;
    const CLB_6 = 6;
    const CLB_5 = 5;
    const CLB_4 = 4;
    const CLB_NOT_APPLICABLE = 0;

    const PROFICIENCY_LIST = [
        self::CLB_8_OR_HIGHER => 'CLB 8 and higher',
        self::CLB_7 => 'CLB 7',
        self::CLB_6 => 'CLB 6',
        self::CLB_5 => 'CLB 5',
        self::CLB_4 => 'CLB 4',
        self::CLB_NOT_APPLICABLE => 'CLB 3 or lower / NA',
    ];



    const YES = 1;
    const NO = 0;
    const YES_NO = [
        self::YES => 'Yes',
        self::NO => 'No'
    ];

    const AGE_LIST = [
        'under_18' => 'Under 18',
        '18-21' => '18 - 21 years',
        '22-34' => '22 - 34 years',
        '35-45' => '35 - 45 years',
        '46-50' => '46 - 50 years',
        'above_50' => 'more than 50'
    ];

    const EDUCATION_LIST = [
        "PhD or Master's degree" => "PhD or Master's degree",
        "Bachelor's degree OR at least a three-year degree at a university or college" => "Bachelor's degree OR at least a three-year degree at a university or college",
        'Trade certification equivalent to journeyperson status in Saskatchewan' => 'Trade certification equivalent to journeyperson status in Saskatchewan',
        '2 Year Post secondary Diploma' => '2 Year Post secondary Diploma',
        '1 Year post secondary diploma' => '1 Year post secondary diploma'
    ];

    const WORK_EXP_LIST = [
        '5_years' => '5 years',
        '4_years' => '4 years',
        '3_years' => '3 years',
        '2_years' => '2 years',
        '1_years' => '1 years',
        'less_than_1' => 'Less than 1 year',
    ];

    public function rules()
    {
        return [
            [['education', 'workExperienceFiveYrPrior', 'workExperienceTenYrPrior', 'age', 'englishProficiency', 'frenchProficiency', 'employmentOffer', 'relativeInSaskatchewan', 'studyExperienceInSaskatchewan', 'workExperienceInSaskatchewan'], 'required', 'message' => 'Please select any option']
        ];
    }
}
