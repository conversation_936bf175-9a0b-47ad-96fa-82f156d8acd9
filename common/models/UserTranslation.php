<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "user_translation".
 *
 * @property int $id
 * @property int|null $tag_user_id
 * @property string|null $user_name
 * @property int|null $lang_code
 */
class UserTranslation extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'user_translation';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['tag_user_id', 'lang_code'], 'integer'],
            [['user_name', 'about', 'job_role'], 'string'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'tag_user_id' => 'Tag User ID',
            'user_name' => 'Name',
            'lang_code' => 'Language Code',
            'about' => 'About',
            'job_role' => 'Job Role',
        ];
    }
    public function getTranslation()
    {
        return $this->hasMany(User::className(), ['id' => 'tag_user_id'])->viaTable('user_translation', ['id' => 'id'])->orderBy(['updated_at' => SORT_DESC]);
    }

    //get profile
    public function getProfile()
    {
        return $this->hasOne(Profile::className(), ['user_id' => 'tag_user_id']);
    }
}
