<?php

namespace common\models\query;

/**
 * This is the ActiveQuery class for [[\common\models\SaCollegeSubpageContent]].
 *
 * @see \common\models\SaCollegeSubpageContent
 */
class SaCollegeSubpageContentQuery extends \yii\db\ActiveQuery
{
    /*public function active()
    {
        return $this->andWhere('[[status]]=1');
    }*/

    /**
     * {@inheritdoc}
     * @return \common\models\SaCollegeSubpageContent[]|array
     */
    public function all($db = null)
    {
        return parent::all($db);
    }

    /**
     * {@inheritdoc}
     * @return \common\models\SaCollegeSubpageContent|array|null
     */
    public function one($db = null)
    {
        return parent::one($db);
    }
}
