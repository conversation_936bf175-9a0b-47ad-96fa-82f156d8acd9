<?php

namespace common\models\query;

/**
 * This is the ActiveQuery class for [[\common\models\ClpCollegeUsp]].
 *
 * @see \common\models\ClpCollegeUsp
 */
class ClpCollegeUspQuery extends \yii\db\ActiveQuery
{
    /*public function active()
    {
        return $this->andWhere('[[status]]=1');
    }*/

    /**
     * {@inheritdoc}
     * @return \common\models\ClpCollegeUsp[]|array
     */
    public function all($db = null)
    {
        return parent::all($db);
    }

    /**
     * {@inheritdoc}
     * @return \common\models\ClpCollegeUsp|array|null
     */
    public function one($db = null)
    {
        return parent::one($db);
    }
}
