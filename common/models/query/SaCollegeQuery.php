<?php

namespace common\models\query;

/**
 * This is the ActiveQuery class for [[\common\models\SaCollege]].
 *
 * @see \common\models\SaCollege
 */
class SaCollegeQuery extends \yii\db\ActiveQuery
{
    /*public function active()
    {
        return $this->andWhere('[[status]]=1');
    }*/

    /**
     * {@inheritdoc}
     * @return \common\models\SaCollege[]|array
     */
    public function all($db = null)
    {
        return parent::all($db);
    }

    /**
     * {@inheritdoc}
     * @return \common\models\SaCollege|array|null
     */
    public function one($db = null)
    {
        return parent::one($db);
    }
}
