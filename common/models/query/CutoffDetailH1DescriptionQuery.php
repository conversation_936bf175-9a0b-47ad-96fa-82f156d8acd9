<?php

namespace common\models\query;

/**
 * This is the ActiveQuery class for [[\common\models\CutoffDetailH1Description]].
 *
 * @see \common\models\CutoffDetailH1Description
 */
class CutoffDetailH1DescriptionQuery extends \yii\db\ActiveQuery
{
    /*public function active()
    {
        return $this->andWhere('[[status]]=1');
    }*/

    /**
     * {@inheritdoc}
     * @return \common\models\CutoffDetailH1Description[]|array
     */
    public function all($db = null)
    {
        return parent::all($db);
    }

    /**
     * {@inheritdoc}
     * @return \common\models\CutoffDetailH1Description|array|null
     */
    public function one($db = null)
    {
        return parent::one($db);
    }
}
