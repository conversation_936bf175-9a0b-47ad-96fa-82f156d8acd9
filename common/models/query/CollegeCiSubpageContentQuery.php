<?php

namespace common\models\query;

/**
 * This is the ActiveQuery class for [[\common\models\CollegeCiSubpageContent]].
 *
 * @see \common\models\CollegeCiSubpageContent
 */
class CollegeCiSubpageContentQuery extends \yii\db\ActiveQuery
{
    /*public function active()
    {
        return $this->andWhere('[[status]]=1');
    }*/

    /**
     * {@inheritdoc}
     * @return \common\models\CollegeCiSubpageContent[]|array
     */
    public function all($db = null)
    {
        return parent::all($db);
    }

    /**
     * {@inheritdoc}
     * @return \common\models\CollegeCiSubpageContent|array|null
     */
    public function one($db = null)
    {
        return parent::one($db);
    }
}
