<?php

namespace common\models\query;

/**
 * This is the ActiveQuery class for [[\common\models\College]].
 *
 * @see \common\models\College
 */
class CollegeQuery extends \yii\db\ActiveQuery
{
    public function active()
    {
        return $this->andWhere('[[status]]=1');
    }

    /**
     * {@inheritdoc}
     * @return \common\models\College[]|array
     */
    public function all($db = null)
    {
        return parent::all($db);
    }

    /**
     * {@inheritdoc}
     * @return \common\models\College|array|null
     */
    public function one($db = null)
    {
        return parent::one($db);
    }

    public function byId($id)
    {
        return $this->andWhere(['id' => $id])->active();
    }

    public function byOldId($id)
    {
        return $this->andWhere(['old_id' => $id])->active();
    }

    public function bySlug($slug)
    {
        return $this->andWhere(['slug' => $slug]);
    }

    public function collegeBySlugWithCity($id)
    {
        return $this->andWhere(['id' => $id])->active()->with('city')->one();
    }

    public function excludeByCollegeId($id)
    {
        return $this->andWhere(['<>', 'id', $id]);
    }
}
