<?php

namespace common\models\query;

/**
 * This is the ActiveQuery class for [[CollegeListingMapping]].
 *
 * @see CollegeListingMapping
 */
class CollegeListingMappingQuery extends \yii\db\ActiveQuery
{
    /*public function active()
    {
        return $this->andWhere('[[status]]=1');
    }*/

    /**
     * {@inheritdoc}
     * @return CollegeListingMapping[]|array
     */
    public function all($db = null)
    {
        return parent::all($db);
    }

    /**
     * {@inheritdoc}
     * @return CollegeListingMapping|array|null
     */
    public function one($db = null)
    {
        return parent::one($db);
    }
}
