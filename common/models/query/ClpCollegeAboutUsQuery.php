<?php

namespace common\models\query;

/**
 * This is the ActiveQuery class for [[\common\models\ClpCollegeAboutUs]].
 *
 * @see \common\models\ClpCollegeAboutUs
 */
class ClpCollegeAboutUsQuery extends \yii\db\ActiveQuery
{
    /*public function active()
    {
        return $this->andWhere('[[status]]=1');
    }*/

    /**
     * {@inheritdoc}
     * @return \common\models\ClpCollegeAboutUs[]|array
     */
    public function all($db = null)
    {
        return parent::all($db);
    }

    /**
     * {@inheritdoc}
     * @return \common\models\ClpCollegeAboutUs|array|null
     */
    public function one($db = null)
    {
        return parent::one($db);
    }
}
