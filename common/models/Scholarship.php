<?php

namespace common\models;

use Yii;
use yii\behaviors\SluggableBehavior;
use yii\behaviors\TimestampBehavior;
use common\services\ElasticSearchService;
use common\services\MysqlSearchService;
use common\models\ScholarshipCategory;
use yii\db\ActiveRecord;
use common\event\SitemapEventNew;

/**
 * This is the model class for table "scholarship".
 *
 * @property int $id
 * @property int $category_id
 * @property string $name
 * @property string $slug
 * @property string|null $cover_image
 * @property string|null $logo_url
 * @property string|null $conducted_by
 * @property string|null $eligibility
 * @property int|null $country_id
 * @property int|null $state_id
 * @property string|null $reward
 * @property string|null $deadline
 * @property int $is_published
 * @property int $status
 * @property string|null $created_at
 * @property string|null $updated_at
 *
 * @property ScholarshipCategory $category
 * @property ScholarshipContent[] $scholarshipContents
 * @property ScholarshipPivot[] $scholarshipPivots
 */
class Scholarship extends \yii\db\ActiveRecord
{
    const STATUS_INACTIVE = 0;
    const STATUS_ACTIVE = 1;

    const IS_PUBLISHED_YES = 1;
    const IS_PUBLISHED_NO = 0;

    const ENTITY_SCHOLARSHIP = 'scholarships';
    const ENTITY_SCHOLARSHIP_URL = 'scholarship';
    const ENTITY_SCHOLARSHIP_LOGO = 'scholarship_logo';

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'scholarship';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['name', 'slug', 'country_id'], 'required'],
            [['state_id', 'is_published', 'status'], 'integer'],
            [['deadline', 'created_at', 'updated_at'], 'safe'],
            [['name', 'slug', 'cover_image', 'country_id', 'logo_url', 'conducted_by', 'eligibility', 'reward'], 'string', 'max' => 255],
            [['slug'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => ['created_at', 'updated_at'],
                    ActiveRecord::EVENT_BEFORE_UPDATE => 'updated_at',
                ],
                'value' => new \yii\db\Expression('NOW()'),
            ],
            'sluggable' => [
                'class' => SluggableBehavior::class,
                'attribute' => 'name',
                'immutable' => true
            ],
            // 'bedezign\yii2\audit\AuditTrailBehavior'
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'name' => 'Name',
            'slug' => 'Slug',
            'cover_image' => 'Cover Image',
            'logo_url' => 'Logo Image',
            'conducted_by' => 'Conducted By',
            'eligibility' => 'Eligibility',
            'country_id' => 'Country ID',
            'state_id' => 'State ID',
            'reward' => 'Reward',
            'deadline' => 'Deadline',
            'is_published' => 'Is Published',
            'status' => 'Status',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * Gets query for [[Category]].
     *
     * @return \yii\db\ActiveQuery|ScholarshipCategoryQuery
     */
    public function getCategory()
    {
        return $this->hasMany(ScholarshipCategory::className(), ['id' => 'category_id'])->viaTable('scholarship_category_map', ['scholarship_id' => 'id'])->orderBy(['updated_at' => SORT_DESC]);
    }

    public function saveCategory(array $categoryIds = [])
    {
        if (empty($this->id)) {
            throw new Exception(ScholarshipCategory::class . ' id is required');
        }

        if (empty($categoryIds) || !$this->isNewRecord) {
            $this->unlinkAll('category', true);
        }

        foreach ($categoryIds as $categoryId) {
            $categoryModel = ScholarshipCategory::findOne($categoryId);
            if (!$categoryModel) {
                continue;
            }

            $this->link('category', $categoryModel);
        }
    }

    /**
     * Gets query for [[ScholarshipContents]].
     *
     * @return \yii\db\ActiveQuery|ScholarshipContentQuery
     */
    public function getScholarshipContents()
    {
        return $this->hasMany(ScholarshipContent::className(), ['scholarship_id' => 'id']);
    }

    /**
     * Gets query for [[ScholarshipPivots]].
     *
     * @return \yii\db\ActiveQuery|ScholarshipPivotQuery
     */
    public function getScholarshipPivots()
    {
        return $this->hasMany(ScholarshipPivot::className(), ['scholarship_id' => 'id']);
    }

    /**
     * {@inheritdoc}
     * @return ScholarshipQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new ScholarshipQuery(get_called_class());
    }

    // update sitemap collections
    public function afterSave($insert, $changedAttributes)
    {
        if (YII_ENV == 'prod') {
            (new ElasticSearchService)->updateElasticSearch($this, 'scholarship');
        }

        (new MysqlSearchService)->updateMysqlSearch($this, 'scholarship');
        (new SitemapEventNew())->updateScholarShipSitemap($this->slug, '');
        return parent::afterSave($insert, $changedAttributes);
    }
}
