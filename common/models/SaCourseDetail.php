<?php

namespace common\models;

use Yii;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "sa_course_detail".
 *
 * @property int $id
 * @property int|null $sa_course_id
 * @property int|null $sa_college_id
 * @property int|null $sa_stream_id
 * @property int|null $sa_degree_id
 * @property int|null $sa_specialization_id
 * @property int|null $total_fees
 * @property int|null $duration
 * @property int|null $duration_type
 * @property int|null $sa_course_duration_type_id
 * @property int $is_published
 * @property int $status
 * @property string|null $created_at
 * @property string|null $updated_at
 *
 * @property SaCollege $saCollege
 * @property SaCourse $saCourse
 * @property SaDegree $saDegree
 * @property SaSpecialization $saSpecialization
 * @property SaStream $saStream
 */
class SaCourseDetail extends \yii\db\ActiveRecord
{
    const STATUS_INACTIVE = 0;
    const STATUS_ACTIVE = 1;

    const IS_PUBLISHED_YES = 1;
    const IS_PUBLISHED_NO = 0;

     /**
     * {@inheritdoc}
    */
    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => ['created_at', 'updated_at'],
                    ActiveRecord::EVENT_BEFORE_UPDATE => 'updated_at',
                ],
                'value' => new \yii\db\Expression('NOW()'),
            ],
            // 'bedezign\yii2\audit\AuditTrailBehavior'
        ];
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'sa_course_detail';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['sa_course_id', 'sa_college_id', 'sa_stream_id', 'sa_degree_id', 'sa_specialization_id', 'total_fees', 'duration', 'duration_type', 'sa_course_duration_type_id', 'is_published', 'status', 'created_by', 'updated_by'], 'integer'],
            [['created_at', 'updated_at'], 'safe'],
            [['sa_college_id'], 'exist', 'skipOnError' => true, 'targetClass' => SaCollege::className(), 'targetAttribute' => ['sa_college_id' => 'id']],
            [['sa_course_id'], 'exist', 'skipOnError' => true, 'targetClass' => SaCourse::className(), 'targetAttribute' => ['sa_course_id' => 'id']],
            [['sa_degree_id'], 'exist', 'skipOnError' => true, 'targetClass' => SaDegree::className(), 'targetAttribute' => ['sa_degree_id' => 'id']],
            [['sa_specialization_id'], 'exist', 'skipOnError' => true, 'targetClass' => SaSpecialization::className(), 'targetAttribute' => ['sa_specialization_id' => 'id']],
            [['sa_stream_id'], 'exist', 'skipOnError' => true, 'targetClass' => SaStream::className(), 'targetAttribute' => ['sa_stream_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'sa_course_id' => 'Sa Course',
            'sa_college_id' => 'Sa College',
            'sa_stream_id' => 'Sa Stream',
            'sa_degree_id' => 'Sa Degree',
            'sa_specialization_id' => 'Sa Specialization',
            'total_fees' => 'Total Fees',
            'duration' => 'Duration',
            'duration_type' => 'Duration Type',
            'sa_course_duration_type_id' => 'Mode',
            'is_published' => 'Is Published',
            'status' => 'Status',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * Gets query for [[SaCollege]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\SaCollegeQuery
     */
    public function getSaCollege()
    {
        return $this->hasOne(SaCollege::className(), ['id' => 'sa_college_id']);
    }

    /**
     * Gets query for [[SaCourse]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\SaCourseQuery
     */
    public function getSaCourse()
    {
        return $this->hasOne(SaCourse::className(), ['id' => 'sa_course_id']);
    }

    /**
     * Gets query for [[SaDegree]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\SaDegreeQuery
     */
    public function getSaDegree()
    {
        return $this->hasOne(SaDegree::className(), ['id' => 'sa_degree_id']);
    }

    /**
     * Gets query for [[SaSpecialization]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\SaSpecializationQuery
     */
    public function getSaSpecialization()
    {
        return $this->hasOne(SaSpecialization::className(), ['id' => 'sa_specialization_id']);
    }

    /**
     * Gets query for [[SaStream]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\SaStreamQuery
     */
    public function getSaStream()
    {
        return $this->hasOne(SaStream::className(), ['id' => 'sa_stream_id']);
    }

     /**
     * Gets query for [[SaStream]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\SaCourseDurationType
     */
    public function getSaCourseDurationType()
    {
        return $this->hasOne(SaCourseDurationType::className(), ['id' => 'sa_course_duration_type_id']);
    }

    /**
     * Gets query for [[Author]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\UserQuery
     */
    public function getAuthor()
    {
        return $this->hasOne(User::className(), ['id' => 'created_by']);
    }

    /**
     * Gets query for [[Author]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\UserQuery
     */
    public function getUpdater()
    {
        return $this->hasOne(User::className(), ['id' => 'updated_by']);
    }

    /**
     * {@inheritdoc}
     * @return \common\models\query\SaCourseDetailQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new \common\models\query\SaCourseDetailQuery(get_called_class());
    }
}
