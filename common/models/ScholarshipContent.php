<?php

namespace common\models;

use common\services\UserService;
use Yii;
use yii\behaviors\SluggableBehavior;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;
use common\event\SitemapEventNew;

/**
 * This is the model class for table "scholarship_content".
 *
 * @property int $id
 * @property int $scholarship_id
 * @property int $author_id
 * @property string|null $page
 * @property string|null $h1
 * @property string|null $meta_title
 * @property string|null $meta_description
 * @property string|null $content
 * @property int $status
 * @property string|null $created_at
 * @property string|null $updated_at
 *
 * @property User $author
 * @property Scholarship $scholarship
 */
class ScholarshipContent extends \yii\db\ActiveRecord
{
    const STATUS_INACTIVE = 0;
    const STATUS_ACTIVE = 1;
    const STATUS_DRAFT = 2;
    const SCENARIO_IMPORTER = 'importer';

    const SCHOLARSHIP_CONTENT_ENTITY = 'scholarship-content';

    public $skipAfterSave = false;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'scholarship_content';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['scholarship_id', 'author_id'], 'required', 'except' => self::SCENARIO_IMPORTER],
            [['scholarship_id', 'author_id', 'status'], 'integer'],
            [['content', 'meta_description'], 'string'],
            [['created_at', 'updated_at'], 'safe'],
            [['page', 'h1', 'meta_title'], 'string', 'max' => 255],
            [['author_id'], 'exist', 'skipOnError' => true, 'targetClass' => User::className(), 'targetAttribute' => ['author_id' => 'id']],
            [['scholarship_id'], 'exist', 'skipOnError' => true, 'targetClass' => Scholarship::className(), 'targetAttribute' => ['scholarship_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => ['created_at', 'updated_at'],
                    ActiveRecord::EVENT_BEFORE_UPDATE => 'updated_at',
                ],
                'value' => new \yii\db\Expression('NOW()'),
            ],
            // 'bedezign\yii2\audit\AuditTrailBehavior'
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'scholarship_id' => 'Scholarship ID',
            'author_id' => 'Author ID',
            'page' => 'Page',
            'h1' => 'H1',
            'meta_title' => 'Meta Title',
            'meta_description' => 'Meta Description',
            'content' => 'Content',
            'status' => 'Status',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * Gets query for [[Author]].
     *
     * @return \yii\db\ActiveQuery|yii\db\ActiveQuery
     */
    public function getAuthor()
    {
        return $this->hasOne(User::className(), ['id' => 'author_id'])->where(['not', ['user.status' => User::STATUS_DELETED]]);
    }

    public function getDefaultuser()
    {
        return UserService::getDefaultUserData();
    }

    /**
     * Gets query for [[Author]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\UserQuery
     */
    public function getBackendauthor()
    {
        return $this->hasOne(User::className(), ['id' => 'author_id']);
    }

    /**
     * Gets query for [[Scholarship]].
     *
     * @return \yii\db\ActiveQuery|ScholarshipQuery
     */
    public function getScholarship()
    {
        return $this->hasOne(Scholarship::className(), ['id' => 'scholarship_id']);
    }

    /**
     * {@inheritdoc}
     * @return ScholarshipContentQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new ScholarshipContentQuery(get_called_class());
    }

    public function afterSave($insert, $changedAttributes)
    {
        if ($this->skipAfterSave) {
            return;
        }

        (new SitemapEventNew())->updateScholarShipSitemap($this->scholarship_id, $this->page);

        return parent::afterSave($insert, $changedAttributes);
    }
}
