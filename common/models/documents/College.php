<?php

namespace common\models\documents;

use Yii;

/**
 * This is the model class for collection "collegeFilter".
 *
 * @property \MongoDB\BSON\ObjectID|string $_id
 * @property mixed $college_id
 * @property mixed $college_old_id
 * @property mixed $name
 * @property mixed $display_name
 * @property mixed $slug
 * @property mixed $logo
 * @property mixed $banner_image
 * @property mixed $avgFees
 * @property mixed $city_id
 * @property mixed $city_name
 * @property mixed $city_slug
 * @property mixed $state_id
 * @property mixed $state_name
 * @property mixed $state_slug
 * @property mixed $course
 * @property mixed $review
 * @property mixed $rev_category_rating
 * @property mixed $review_count
 * @property mixed $rating
 * @property mixed $sub_pages
 * @property mixed $mode
 * @property mixed $type
 * @property mixed $exams
 * @property mixed $approvals
 * @property mixed $affiliated_by
 * @property mixed $is_popular
 * @property mixed $position
 * @property mixed $rank
 */
class College extends \yii\mongodb\ActiveRecord
{
    const STATUS_ACTIVE = 1;
    const STATUS_INACTIVE = 0;

    const COLLECTION_NAME = 'collegeFilter_v3';

    /**
     * {@inheritdoc}
     */
    public static function collectionName()
    {
        return ['getmyuni_email', self::COLLECTION_NAME];
    }

    /**
     * {@inheritdoc}
     */
    public function attributes()
    {
        return [
            '_id',
            'college_id',
            'college_old_id',
            'name',
            'display_name',
            'slug',
            'logo',
            'banner_image',
            'avgFees',
            'city_id',
            'city_name',
            'city_slug',
            'state_id',
            'state_name',
            'state_slug',
            'course',
            'review',
            'rev_category_rating',
            'review_count',
            'rating',
            'sub_pages',
            'mode',
            'type',
            'exams',
            'approvals',
            'affiliated_by',
            'is_popular',
            'is_sponsored',
            'position',
            'rank',
            'status',
            'courseCount',
            'accreditations',
            'maxAvgFees',
            'minAvgFees',
            'nirf_rank_overall'
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['college_id', 'college_old_id', 'name', 'display_name', 'slug', 'logo', 'banner_image', 'avgFees', 'city_id', 'city_name', 'city_slug', 'state_id', 'state_name', 'state_slug', 'status', 'is_sponsored'], 'safe'],
            [['course', 'review', 'rev_category_rating', 'review_count', 'rating', 'sub_pages', 'mode', 'type', 'exams', 'approvals', 'affiliated_by', 'is_popular', 'position', 'rank'], 'safe']
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            '_id' => 'ID',
            'college_id' => 'College Id',
            'college_old_id' => 'College Old Id',
            'name' => 'Name',
            'display_name' => 'Display Name',
            'slug' => 'Slug',
            'logo' => 'Logo',
            'banner_image' => 'Banner Image',
            'avgFees' => ' Average Fee',
            'city_id' => 'City Id',
            'city_name' => 'City Name',
            'city_slug' => 'City Slug',
            'state_id' => 'State Id',
            'state_name' => 'State Name',
            'state_slug' => 'State Slug',
            'course' => 'Course Details',
            'review' => 'Review',
            'rev_category_rating' => 'Review Category Ratings',
            'review_count' => 'Review Count',
            'rating' => 'Rating',
            'sub_pages' => 'Sub Pages',
            'mode' => 'Mode',
            'type' => 'Type',
            'exams' => 'Exams',
            'approvals' => 'Approvals',
            'affiliated_by' => 'Affiliated By',
            'is_popular' => 'Is Popular',
            'is_sponsored' => 'Is Sponsored',
            'position' => 'Position',
            'rank' => 'Rank',
            'status' => 'Status'
        ];
    }

    /**
     * Function for Type Casting Document's fields before saving.
     */
    public function beforeSave($insert)
    {
        $this->college_id = (int) $this->college_id;
        $this->avgFees = (float) $this->avgFees;
        $this->is_popular = (int) $this->is_popular;
        $this->is_sponsored = (int) $this->is_sponsored;
        $this->rank = (int) $this->rank;
        $this->position = (int) $this->position;
        $this->status = (int) $this->status;
        return parent::beforeSave($insert);
    }
}
