<?php

namespace common\models;

use Yii;
use yii\behaviors\SluggableBehavior;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "sa_country_detail".
 *
 * @property int $id
 * @property int|null $sa_country_id
 * @property string|null $title
 * @property string|null $description
 * @property string|null $h1
 * @property string|null $meta_title
 * @property string|null $meta_description
 * @property string|null $cover_image
 * @property string|null $related_article_h1
 * @property string|null $content
 * @property int $status
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property string|null $created_at
 * @property string|null $updated_at
 *
 * @property SaCountry $saCountry
 */
class SaCountryDetail extends \yii\db\ActiveRecord
{
    const STATUS_INACTIVE = 0;
    const STATUS_ACTIVE = 1;

    /**
     * {@inheritdoc}
     */
    // public function behaviors()
    // {
    //     return [
    //         [
    //             'class' => TimestampBehavior::class,
    //             'attributes' => [
    //                 ActiveRecord::EVENT_BEFORE_INSERT => ['created_at', 'updated_at'],
    //                 ActiveRecord::EVENT_BEFORE_UPDATE => 'updated_at',
    //             ],
    //             'value' => new \yii\db\Expression('NOW()'),
    //         ],

    //         // 'bedezign\yii2\audit\AuditTrailBehavior'
    //     ];
    // }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'sa_country_detail';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['sa_country_id', 'status', 'created_by', 'updated_by'], 'integer'],
            [['description', 'content', 'related_article_h1'], 'string'],
            [['created_at', 'updated_at'], 'safe'],
            [['title', 'h1', 'meta_title', 'meta_description', 'cover_image'], 'string', 'max' => 255],
            [['sa_country_id'], 'exist', 'skipOnError' => true, 'targetClass' => SaCountry::className(), 'targetAttribute' => ['sa_country_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'sa_country_id' => 'Sa Country ID',
            'title' => 'Title',
            'description' => 'Description',
            'h1' => 'H1',
            'meta_title' => 'Meta Title',
            'meta_description' => 'Meta Description',
            'cover_image' => 'Cover Image',
            'related_article_h1' => 'Related Article H1',
            'content' => 'Content',
            'status' => 'Status',
            'created_by' => 'Created By',
            'updated_by' => 'Updated By',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

     /**
     * Gets query for [[Author]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\UserQuery
     */
    public function getAuthor()
    {
        return $this->hasOne(User::className(), ['id' => 'created_by']);
    }
    
    /**
    * Gets query for [[Author]].
    *
    * @return \yii\db\ActiveQuery|\common\models\query\UserQuery
    */
    public function getUpdater()
    {
        return $this->hasOne(User::className(), ['id' => 'updated_by']);
    }
    
    /**
     * Gets query for [[SaCountry]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\SaCountryQuery
     */
    public function getSaCountry()
    {
        return $this->hasOne(SaCountry::className(), ['id' => 'sa_country_id']);
    }

    /**
     * {@inheritdoc}
     * @return \common\models\query\SaCountryDetailQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new \common\models\query\SaCountryDetailQuery(get_called_class());
    }
}
