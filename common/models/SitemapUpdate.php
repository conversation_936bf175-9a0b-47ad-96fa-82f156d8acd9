<?php

namespace common\models;

use Yii;
use yii\behaviors\TimestampBehavior;
use yii\db\Expression;
use common\helpers\DataHelper;

/**
 * This is the model class for table "media_drive".
 *
 * @property int $id
 * @property string $file_name
 */
class SitemapUpdate extends \yii\db\ActiveRecord
{

    const STATUS_INACTIVE = 0;
    const STATUS_ACTIVE = 1;
    const STATUS_DELETED = 2;

    const ENTITY_CATEGORY = 'category';
    const ENTITY_NEWS = 'news';
    const ENTITY_TAG = 'tag';
    const ENTITY_ARTICLE = 'articles';
    const ENTITY_EXAM = 'exams';
    const ENTITY_COLLEGE = 'college';
    const ENTITY_ARTICLE_CATEGORY = 'article-category';
    const ENTITY_STUDY_ABROAD = 'study-abroad';
    const ENTITY_BOARD = 'boards';
    const ENTITY_COURSE = 'courses';
    const ENTITY_LISTING = 'listing';
    const ENTITY_CAREER = 'careers';
    const ENTITY_NCERT = 'ncert';
    const ENTITY_OLYMPIAD = 'olympiad';
    const ENTITY_SCHOLARSHIP = 'scholarships';


    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'sitemap_update';
    }

    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'createdAtAttribute' => 'created_at',
                'updatedAtAttribute' => 'updated_at',
                'value' => new Expression('NOW()'),
            ]
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['entity', 'domain', 'slug', 'sub_page', 'priority', 'change_freq'], 'string'],
            [['created_at', 'updated_at', 'sitemap_update', 'published_at', 'lang_code'], 'safe']

        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'entity' => 'Entity',
            'domain' => 'Domain',
            'slug' => 'Slug',
            'sub_page' => 'Sub Page',
            'lang_code' => 'Lang Code',
            'priority' => 'Priority',
            'change_freq' => 'Change Frequency',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }
}
