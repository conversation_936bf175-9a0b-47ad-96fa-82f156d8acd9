<?php

namespace common\helpers;

use common\services\ExamService;
use frontend\helpers\Url;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use Yii;

class FilterHelper
{
    const YEAR = '2025';
    public static $_orderedFilterGroup = [
        'State',
        'City',
        'Streams',
        'Courses',
        'Specialization',
        'Program Mode',
        'Ownership',
        'Exams Accepted',
        'Course Type',
        'Affiliated By',
        'Approvals',
        'Total Fees',
    ];

    public static function getSubpages($slug, $subpage, $collegeDisplayName)
    {
        return Html::ul(array_slice($subpage, 0, 3), ['item' => function ($item, $index) use ($slug, $collegeDisplayName) {

            // if ($slug == 'lovely-professional-university-lpu-jalandhar') {
            //     $item = ($index == 'admission') ? $item . ' ' . '2024' : $item;
            // } else {
                $item = ($index == 'admission') ? $item . ' ' . self::YEAR : $item;
            // }

            return Html::tag(
                'li',
                Html::a($item, Url::toCollege($slug, $index), [
                    'title' => $collegeDisplayName . ' ' . ArrayHelper::getValue(CollegeHelper::$subPageTitles, $index)
                ])
            );
        }, 'class' => 'row']);
    }

    public static function getAvgFees(array $college)
    {
        if (!isset($college['course'])) {
            return '-';
        }
        $feesContainer = [];
        foreach ($college['course'] as $key => $value) {
            if (isset($value['avg_fees']) && $value['avg_fees'] != null) {
                $feesContainer[] = $value['avg_fees'];
            }
        }

        if (empty($feesContainer)) {
            return '--';
        }

        return '₹' . CollegeHelper::feesFormat(floor(array_sum($feesContainer) / count($feesContainer)));
    }

    public static function getExams($exams)
    {
        // $args = __CLASS__ . __FUNCTION__ . json_encode(func_get_args());
        // $key =  md5(base64_encode(serialize($args)));
        // $data = Yii::$app->cache->getOrSet($key, function () {
            $i = 0;
        if (empty($exams)) {
            return '-';
        }
            $items = [];
          
        foreach ($exams as $exam) {
            // if ($i == 2) {
            //     break;
            // }
            $examDetail = (new ExamService())->getDetail($exam);
            
            if ($examDetail) {
                $items[] = $examDetail->display_name;
            }
            // $i++;
        }
        
            asort($items);
            return $items;
            // return implode(', ', $items);
        // }, 60 * 60 * 24 * 90);

        // return $data;
    }

    public static function sortFilterGroup($filters)
    {
        if (empty($filters)) {
            return [];
        }

        $items = [];
        foreach (self::$_orderedFilterGroup as $value) {
            if (isset($filters[$value])) {
                $items[$value] = $filters[$value];
            }
        }

        return $items;
    }

    public static function isExamPageUrl($url)
    {
        if (strpos($url, '?')) {
            $url = explode('?', $url);
            $url = $url[0];
        }
        $url = ltrim($url, '/');
        $params = explode('/', $url);
        $urlPattern = '#colleges-accepting-(.*)-score-in-(.*)#';

        $isExamUrl = preg_match($urlPattern, $params[0], $matches);

        return $isExamUrl;
    }

    public static $removeHonsFromFilter = [
        'ba-hons',
        'bsc-hons',
        'bcom-hons',
        'mtech-computer-science',
        'electrical-electronics-engineering',
        'computer-science-colleges'
    ];
}
