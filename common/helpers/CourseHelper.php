<?php

namespace common\helpers;

class CourseHelper
{
    const YEAR = '2025';


    /**
     * List of Courses by discipline
     *
     * @var array
     */
    public static $coursesList = [
        'Engineering' => [
            ['title' => 'B.Tech', 'slug' => 'btech'],
            ['title' => 'B.E', 'slug' => 'be'],
            ['title' => 'M.Tech', 'slug' => 'mtech'],
            ['title' => 'M.E', 'slug' => 'me'],
        ],
        'Management' => [
            ['title' => 'BBA', 'slug' => 'bba'],
            ['title' => 'MBA', 'slug' => 'mba'],
            ['title' => 'BBM', 'slug' => 'bbm'],
            ['title' => 'MMS', 'slug' => 'mms'],
        ],
        'Science' => [
            ['title' => 'B.Sc', 'slug' => 'bsc'],
            ['title' => 'M.Sc', 'slug' => 'msc'],
            ['title' => 'B.<PERSON><PERSON><PERSON>', 'slug' => 'bfsc'],
            ['title' => '<PERSON><PERSON><PERSON><PERSON><PERSON>', 'slug' => 'mfsc'],
        ],
        'Pharmacy' => [
            ['title' => 'B.Pharma', 'slug' => 'bpharm'],
            ['title' => 'M.Pharma', 'slug' => 'mpharm'],
            ['title' => 'D.Pharma', 'slug' => 'dpharma'],
            ['title' => 'Pharma D', 'slug' => 'pharmd'],
        ],
        'Law' => [
            ['title' => 'LLB', 'slug' => 'llb'],
            ['title' => 'LLM', 'slug' => 'llm'],
            ['title' => 'DTL', 'slug' => 'diploma-in-taxation-law-dtl'],
            ['title' => 'Diploma in Labour Law', 'slug' => 'diploma-in-labour-law'],
        ],
        'Design' => [
            ['title' => 'B.Des', 'slug' => 'bdes'],
            ['title' => 'M.Des', 'slug' => 'mdes'],
            ['title' => 'Diploma in Interior Design', 'slug' => 'diploma-in-interior-design'],
            ['title' => 'Diploma in Fashion Design', 'slug' => 'diploma-in-fashion-design'],
        ],
        'Commerce' => [
            ['title' => 'B.Com', 'slug' => 'bcom'],
            ['title' => 'M.Com', 'slug' => 'mcom'],
            ['title' => 'CFA', 'slug' => 'chartered-financial-analyst-cfa'],
            ['title' => 'MFC', 'slug' => 'mfc']
        ],
        'Medical' => [
            ['title' => 'MBBS', 'slug' => 'mbbs'],
            ['title' => 'BAMS', 'slug' => 'bams'],
            ['title' => 'BUMS', 'slug' => 'bums'],
            ['title' => 'BHMS', 'slug' => 'bhms'],
        ],
        'Dental' => [
            ['title' => 'BDS', 'slug' => 'bds'],
            ['title' => 'MDS', 'slug' => 'mds'],
            ['title' => 'MDS Prosthodontics', 'slug' => 'mds-prosthodontics'],
            ['title' => 'MDS Periodontics', 'slug' => 'mds-periodontics'],
        ],
        'Architecture' => [
            ['title' => 'B.Arch', 'slug' => 'barch'],
            ['title' => 'M.Arch', 'slug' => 'march'],
            ['title' => 'Diploma in Architecture Engineering', 'slug' => 'diploma-in-architecture-engineering'],
            ['title' => 'B.Arch Interior Design', 'slug' => 'barch-interior-design'],
        ],
        'Arts' => [
            ['title' => 'BA', 'slug' => 'ba'],
            ['title' => 'MA', 'slug' => 'ma'],
            ['title' => 'BFA', 'slug' => 'bfa'],
            ['title' => 'BSW', 'slug' => 'bsw'],
        ],
        'Agriculture' => [
            ['title' => 'B.Sc Agriculture', 'slug' => 'bsc-agriculture'],
            ['title' => 'M.Sc Agriculture', 'slug' => 'msc-agriculture'],
            ['title' => 'Diploma in agriculture', 'slug' => 'diploma-in-agriculture'],
            ['title' => 'PhD Agriculture', 'slug' => 'phd-agriculture']
        ],
        'Paramedical' => [
            ['title' => 'ANM Nursing', 'slug' => 'nursing'],
            ['title' => 'DMLT', 'slug' => 'dmlt'],
            ['title' => 'GNM Nursing', 'slug' => 'gnm'],
            ['title' => 'BMLT', 'slug' => 'bmlt']
        ],
        'Education' => [
            ['title' => 'B.Ed', 'slug' => 'bed'],
            ['title' => 'M.Ed', 'slug' => 'med'],
            ['title' => 'D.EL.Ed', 'slug' => 'deled'],
            ['title' => 'B.P.Ed', 'slug' => 'bped']
        ],
        'Computer Application' => [
            ['title' => 'BCA', 'slug' => 'bca'],
            ['title' => 'MCA', 'slug' => 'mca'],
            ['title' => 'PGDCA', 'slug' => 'pgdca'],
            ['title' => 'CCNA', 'slug' => 'cisco-certified-network-associate-ccna']
        ],
    ];

    public static $subPages = [
        'about' => 'About',
        'syllabus-subjects' => 'Syllabus and Subjects',
        'jobs-scope-salary' => 'Job, Scope and Salary',
        'admission' => 'Admission',
        'specializations' => 'Specializations',
        'fees' => 'Fees',
        'placements' => 'Placements',
        // 'salary' => 'Salary'
    ];

    /** Used in Course Category for Subpage */
    public static $cateSubPages = [
        'about' => 'About',
        'syllabus-subjects' => 'Syllabus & Subjects',
        'jobs-scope-salary' => 'Job, Scope & Salary',
        'admission' => 'Admission'
    ];

    public static $degree = [
        'diploma' => 'Diploma',
        'bachelors' => 'Bachelors',
        'integrated-degree' => 'Integrated Degree',
        'masters' => 'Masters',
        'certificate' => 'Certificate',
        'doctorate' => 'Doctorate',
        'postgraduate-diploma' => 'Postgraduate Diploma',
        'postgraduate-certificate' => 'Postgraduate Certificate'
    ];

    public static $mode = [
        'full_time' => 'Full Time',
        'part_time' => 'Part Time',
        'distance_learning' => 'Distance',
    ];

    public static $type = [
        '0' => 'Top Content',
        '1' => 'Main Content',
    ];

    /**
     * Array of fields name of different page in old DB table as key.
     * Array of different Page name in new Table as value.
     */
    public static $fieldName = [
        'about' => 'about',
        'subject_syllabus' => 'syllabus-subjects',
        'jobs_scope_salary' => 'jobs-scope-salary'

    ];

    public static $featurArr = [
        'full_form' => 'degreeIcon',
        'duration' => 'clockIcon',
        'age' => 'ageIcon',
        'entrance_exam' => 'percentIcon',
        'subjects_required' => 'degreeIcon',
        'min_percentage' => 'percentIcon',
        'avg_fees' => 'feesIcon',
        'similar_options' => 'ageIcon',
        'avg_salary' => 'feesIcon',
        'emp_roles' => 'employmentIcon',
        'opportunities' => 'opportunityIcon'
    ];

    public static $categoryArr = [
        'engineering' => 'Engineering',
        'management' => 'Management',
        'science' => 'Science',
        'pharmacy' => 'Pharmacy ',
        'law' => 'Law ',
        'design' => 'Design ',
        'commerce' => 'Commerce',
        'medical' => 'Medical',
        'dental' => 'Dental',
        'architecture' => 'Architecture ',
        'arts' => 'Arts',
        'agriculture' => 'Agriculture',
        'paramedical' => 'Paramedical',
        'education' => 'Education',
        'computer' => 'Computer Application',
    ];

    public static $defaultSeoInfo = [
        'about' => [
            'title' => '{course-name}: Course Details, Eligibility, Admission, Fees',
            'h1' => '{course-name}: Course Details, Eligibility, Admission, Fees',
            'description' => 'Are you planning to pursue {course-name}? Explore more on Course Details, Full Form, Duration, Admission {year}, Fees, Eligibility Criteria, Entrance Exams, and Top Colleges.'
        ],

        'syllabus-subjects' => [
            'title' => '{course-name} Syllabus and Subjects {year} - Semester Wise',
            'h1' => '{course-name} Syllabus and Subjects',
            'description' => 'Want to know all about the {course-name} semester wise syllabus and subjects? Get complete insights on the best books, projects, and course structures.'
        ],
        'jobs-scope-salary' => [
            'title'  => '{course-name} Jobs, Scope, Salary in India {year}',
            'h1' => '{course-name} Jobs, Scope, Salary in India',
            'description' => 'Are you a {course-name} student looking for your dream job? Get top career guidance on government, private and overseas jobs, scope, salaries Top Companies to apply and designations.'
        ],
        'ladning-page' => [
            'title'  => 'Top Courses in India {year}: Get UG, PG, Diploma & Doctorate Courses',
            'h1' => 'Courses in India',
            'description' => 'Find Top Courses in India. Check UG, PG, Diploma, Doctorate, Distance, and Certificate Courses offered by colleges and universities in India.'
        ],
        'category-page' => [
            'title' => '{stream} Courses in India: Eligibility, Fees, Syllabus, Jobs, Colleges',
            'h1' => '{stream} Courses in India',
            'description' => 'Are you looking for {stream} Courses? Get List of {stream} UG, PG, Diploma Courses with Eligibility, Fees, Admission, Syllabus, and Job Scope details.'
        ],
        'admission' => [
            'title' => '{course-name} Admission {year}: Application Form, Eligibility, Entrance Exams, Colleges',
            'h1' => '{course-name} Admission {year}: Application Form, Eligibility, Entrance Exams, Colleges',
            'description' => 'Want to know complete {course-name} Admission {year} Details? Get Eligibility, Entrance Exams, Application Form, Admission Process, Imporant Dates, and Top Colleges.'
        ],
        'specializations' => [
            'title' => 'List of {course-name} Specializations: Courses in Demand, Subjects, Eligibility Criteria, Admission',
            'h1' => 'List of {course-name} Specializations: Courses in Demand, Subjects, Eligibility Criteria, Admission',
            'description' => 'Are you planning to pursue {course-name} but not sure which specialization to opt for? Explore the complete {course-name} specializations list and choose the right course.'
        ],
        'fees' => [
            'title' => '{course-name} Fees in India {year}: Check Fee Structure for Top {course-name} Colleges',
            'h1' => '{course-name} Fees in India {year}: Check Fee Structure for Top {course-name} Colleges',
            'description' => 'Want to know how much will {course-name} cost? Get complete details on the Fee Structure of Top colleges in India with the factors affecting the Fees.'
        ],
        'placements' => [
            'title' => '{course-name} Placements {year}: Reports, Highest & Average Packages in Top Colleges',
            'h1' => '{course-name} Placements {year}: Reports, Highest & Average Packages in Top Colleges',
            'description' => 'Want to know which colleges provide best placement opportunities for {course-name} students? Get the detailed average & highest salary reports from top colleges & companies.'
        ],
        'salary' => [
            'title' => '{course-name} Salary in India {year}: Average & Highest Salary Package',
            'h1' => '{course-name} Salary in India {year}: Average & Highest Salary Package',
            'description' => 'Want to know the detailed {course-name} Salary in India? Get Specialization and Experience wise average and highest salary in India.'
        ],
        'syllabus-subjects-dropdown' => [
            'title' => '{course-name} {page} Syllabus {year}: Complete Subjects List, Electives & Practicals',
            'h1' => '{course-name} {page} Syllabus {year}: Complete Subjects List, Electives & Practicals',
            'description' => 'Want to know the detailed {course-name} {page} syllabus for semester 1 & 2? Get insights on semester wise subjects, electives & practicals for {course-name}.'
        ],
        'admission-dropdown' => [
            'title' => '{page} {course-name} Admission {year}: Eligibility, Application Process, Fees, Top Colleges',
            'h1' => '{page} {course-name} Admission {year}: Eligibility, Application Process, Fees, Top Colleges',
            'description' => 'Are you planning to pursue {course-name} in {page}? Get complete details on the admission procedure, eligibility criteria, available seats and fee structure.'
        ]
    ];

    public static function getDefaultSeoInfForSubPages($page, $course = null, $parentPage = null)
    {
        if ($parentPage == 'admission') {
            $tempPage = $parentPage . '-' . 'dropdown';
            $seoInfo = self::$defaultSeoInfo[$tempPage] ?? [];
        } elseif ($parentPage == 'syllabus-subjects') {
            $tempPage = $parentPage . '-' . 'dropdown';
            $seoInfo = self::$defaultSeoInfo[$tempPage] ?? [];
        } else {
            $seoInfo = self::$defaultSeoInfo[$page] ?? [];
        }
        if (empty($seoInfo)) {
            return [];
        }

        $data = [];
        foreach ($seoInfo as $key => $value) {
            $data[$key] = strtr($value, [
                '{course-name}' => $course,
                '{stream}' => $course,
                '{year}' => self::YEAR,
                '{page}' => $page
            ]);
        }
        return $data;
    }

    public static function getDefaultSeoInfo($page, $course = null)
    {
        $seoInfo = self::$defaultSeoInfo[$page] ?? [];
        if (empty($seoInfo)) {
            return [];
        }

        $data = [];
        foreach ($seoInfo as $key => $value) {
            $data[$key] = strtr($value, [
                '{course-name}' => $course,
                '{stream}' => $course,
                '{year}' => self::YEAR
            ]);
        }
        return $data;
    }

    public static $feesRange = [
        '0-1 Lakh' => '0-100000',
        '1-2 Lakh' => '100001-200000',
        '2-3 Lakh' => '200001-300000',
        '3-5 Lakh' => '300001-500000',
        '5 Lakh' => '500001',
    ];

    /** Remove array after filter page is create */

    public static $filterPageSlug = [
        'phd-psychology',
        'bsc-animation',
        'post-graduate-programme-in-management-pgpm',
        'phd-human-resource-management',
        'diploma-in-agriculture',
        'phd-english',
        'phd-hindi',
        'phd-philosophy',
        'phd-economics',
        'phd-commerce',
        'phd-computer-science',
        'phd-physics',
        'dual-bba-mba',
        'diploma-in-electrical-engineering',
        'diploma-in-mechanical-engineering',
        'dual-bca-mca',
        'diploma-in-civil-engineering',
        'diploma-in-computer-science',
        'diploma-in-automobile-engineering',
        //list should be removed later
        'post-graduate-programme-in-food-and-agri-business-management-pgp-fabm',
        'fellow-programme-in-management-fpm',
        'executive-mba',
        'executive-post-graduate-programme-in-management-epgp',
        'mba-business-analytics',
        'bhsp',
        'pgp-abm',
    ];

    public static $courseCta = [
        'course_sub_pages_cta' => [
            'desktopCta' => [
                1 => 'course_desktop_subpage_1',
                2 => 'course_desktop_subpage_2',
                3 => 'course_desktop_subpage_3',
            ],
            'mobileCta' =>
            [
                1 => 'course_mobile_subpage_1',
                2 => 'course_mobile_subpage_2',
                3 => 'course_mobile_subpage_3',
            ],
        ],
    ];

    public static $syllabusSubPagesDropDown = [
        ['name' => '1st Year', 'value' => '1st Year'],
        ['name' => '2nd Year', 'value' => '2nd Year'],
        ['name' => '3rd Year', 'value' => '3rd Year'],
        ['name' => '4th Year', 'value' => '4th Year'],
        ['name' => '5th Year', 'value' => '5th Year']
    ];
}
